# Path Configuration and Security

This document describes how to safely handle file paths in the application to prevent security vulnerabilities and maintain consistency.

## 🔒 Security Principles

### ❌ **NEVER DO THIS - Dangerous Practices:**

```php
// ❌ Relative paths with __DIR__
$logFile = __DIR__ . '/../../../logs/activity.log';

// ❌ Manual path construction
$dbPath = __DIR__ . '/../../var/db/users.sqlite';

// ❌ Hardcoded paths
$configFile = '/var/www/html/config/settings.php';

// ❌ User input in paths without validation
$filePath = '/uploads/' . $_GET['filename']; // Path traversal risk!
```

**Why these are dangerous:**
- **Path traversal attacks** - `../../../etc/passwd`
- **Environment inconsistency** - different paths on dev/prod
- **Maintenance nightmare** - hardcoded paths everywhere
- **Testing difficulties** - can't mock or isolate paths

### ✅ **ALWAYS DO THIS - Safe Practices:**

```php
// ✅ Use settings for path configuration
$settings = $container->get('settings');
$logPath = $settings['logger']['path'] . '/activity.log';

// ✅ Use DI container for path-dependent services
$activityLogger = $container->get(ActivityLogger::class);

// ✅ Validate and sanitize user input
$filename = basename($_GET['filename']); // Remove directory traversal
$filePath = $settings['upload_path'] . '/' . $filename;
```

## ⚙️ Configuration Structure

### Settings-based Path Management

All paths are configured in `config/defaults.php`:

```php
$settings = [];

// Project root - single source of truth
$settings['root_dir'] = dirname(__DIR__, 1);

// Database paths
$settings['db'] = [
    'connections' => [
        'user' => [
            'database' => $settings['root_dir'] . '/var/db/user.sqlite',
        ],
        'article' => [
            'database' => $settings['root_dir'] . '/var/db/article.sqlite',
        ],
        'mark' => [
            'database' => $settings['root_dir'] . '/var/db/mark.sqlite',
        ],
    ],
];

// Logging paths
$settings['logger'] = [
    'path' => $settings['root_dir'] . '/logs',
];

// Template paths
$settings['renderer'] = [
    'path' => $settings['root_dir'] . '/templates',
];
```

### Environment-specific Overrides

Different environments can override paths in `config/env/env.{environment}.php`:

```php
// config/env/env.test.php
$settings['db']['connections']['user']['database'] = ':memory:';
$settings['logger']['path'] = '/tmp/test-logs';

// config/env/env.prod.php
$settings['logger']['path'] = '/var/log/myapp';
```

## 🏗️ Dependency Injection Pattern

### Service Registration

Register path-dependent services in `config/container.php`:

```php
// ✅ CORRECT - ActivityLogger with settings-based path
\App\Infrastructure\Logging\ActivityLogger::class => function (ContainerInterface $container) {
    $settings = $container->get('settings');
    $logPath = $settings['logger']['path'] . '/activity.log';

    return new \App\Infrastructure\Logging\ActivityLogger($logPath);
},

// ✅ CORRECT - Database connections with settings-based paths
'db.connection.user' => function (ContainerInterface $container) {
    $settings = $container->get('settings');
    $dbPath = $settings['db']['connections']['user']['database'];

    $driver = new Sqlite(['database' => $dbPath]);
    return new Connection(['driver' => $driver]);
},
```

### Service Usage

Use services through DI container in routes:

```php
// ✅ CORRECT - Get service from container
$users->post('/create', function ($request, $response) {
    $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);
    $activityLogger->logUserCreated($userId, 'admin');

    return $response;
});

// ❌ INCORRECT - Manual instantiation
$users->post('/create', function ($request, $response) {
    $activityLogger = new ActivityLogger(__DIR__ . '/../../../logs/activity.log');
    // This is dangerous and inconsistent!
});
```

## 🧪 Testing Considerations

### Test-specific Paths

Tests should use isolated paths to avoid conflicts:

```php
// tests/TestCase/Infrastructure/Logging/ActivityLoggerTest.php
class ActivityLoggerTest extends TestCase
{
    public function testLogging(): void
    {
        // ✅ Use temporary file for testing
        $tempLogFile = tempnam(sys_get_temp_dir(), 'activity_test_');
        $logger = new ActivityLogger($tempLogFile);

        $logger->logUserCreated('test-user-id', 'admin');

        $this->assertFileExists($tempLogFile);

        // Cleanup
        unlink($tempLogFile);
    }
}
```

### Test Environment Settings

Override paths in test configuration:

```php
// config/env/env.test.php
$settings['db']['connections']['user']['database'] = ':memory:';
$settings['logger']['path'] = sys_get_temp_dir() . '/test-logs';
```

## 📁 Directory Structure

### Standard Project Layout

```
project-root/
├── config/
│   ├── defaults.php          # Main path configuration
│   ├── env/
│   │   ├── env.test.php      # Test environment overrides
│   │   └── env.prod.php      # Production environment overrides
│   └── container.php         # DI container with path injection
├── logs/                     # Application logs
│   ├── activity.log          # User activity logs
│   └── app.log              # General application logs
├── var/
│   └── db/                   # Database files
│       ├── user.sqlite
│       ├── article.sqlite
│       └── mark.sqlite
├── templates/                # Template files
└── public/                   # Web-accessible files
    └── assets/               # Static assets
```

### Path Validation

Always validate paths before use:

```php
class PathValidator
{
    public static function validateLogPath(string $path): string
    {
        // Ensure path is within allowed directory
        $realPath = realpath(dirname($path));
        $allowedPath = realpath('/var/log/myapp');

        if (strpos($realPath, $allowedPath) !== 0) {
            throw new \InvalidArgumentException('Path outside allowed directory');
        }

        return $path;
    }

    public static function sanitizeFilename(string $filename): string
    {
        // Remove directory traversal attempts
        $filename = basename($filename);

        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);

        return $filename;
    }
}
```

## 🔧 Migration from Dangerous Paths

### Step-by-step Migration

1. **Identify dangerous paths:**
   ```bash
   grep -r "__DIR__" src/ --include="*.php"
   grep -r "\.\./\.\." src/ --include="*.php"
   ```

2. **Add paths to settings:**
   ```php
   // config/defaults.php
   $settings['custom_service'] = [
       'data_path' => $settings['root_dir'] . '/var/data',
   ];
   ```

3. **Register service in DI container:**
   ```php
   // config/container.php
   CustomService::class => function (ContainerInterface $container) {
       $settings = $container->get('settings');
       return new CustomService($settings['custom_service']['data_path']);
   },
   ```

4. **Update service constructor:**
   ```php
   class CustomService
   {
       public function __construct(string $dataPath)
       {
           $this->dataPath = $dataPath;
       }
   }
   ```

5. **Use service through DI:**
   ```php
   $customService = $this->get(CustomService::class);
   ```

## 🛣️ Route Configuration Best Practices

### FastRoute Ordering Rules

**CRITICAL:** FastRoute requires static routes to be defined before variable routes to avoid shadowing.

#### ✅ **CORRECT Order:**
```php
// Static routes first
$group->get('/users/create', ...);     // Static
$group->get('/users/roles', ...);      // Static
$group->get('/users/import', ...);     // Static

// Variable routes last
$group->get('/users/{id}', ...);       // Variable
$group->get('/users/{id}/edit', ...);  // Variable
```

#### ❌ **INCORRECT Order:**
```php
// This will cause "Static route shadowed" error
$group->get('/users/{id}', ...);       // Variable first
$group->get('/users/create', ...);     // Static - SHADOWED!
```

### Route Organization Strategy

1. **Static routes** - exact path matches
2. **Parameterized routes** - with path parameters
3. **Catch-all routes** - with wildcard parameters

### Common Route Patterns

```php
// Resource routes in correct order
$group->get('', ...);                  // List
$group->get('/create', ...);           // Create form
$group->post('/create', ...);          // Create action
$group->get('/export', ...);           // Export
$group->get('/import', ...);           // Import
$group->get('/{id}', ...);             // View
$group->get('/{id}/edit', ...);        // Edit form
$group->post('/{id}/edit', ...);       // Update action
$group->delete('/{id}', ...);          // Delete
```

## 📋 Checklist

### Before Deploying

- [ ] No `__DIR__ . '/../../..'` patterns in code
- [ ] All paths configured in settings
- [ ] Services registered in DI container
- [ ] Test environment uses isolated paths
- [ ] Production paths are secure and validated
- [ ] File permissions are properly set
- [ ] Directory creation is handled gracefully
- [ ] **Route ordering follows FastRoute rules**
- [ ] **Static routes defined before variable routes**

### Code Review Checklist

- [ ] No hardcoded paths in business logic
- [ ] User input is validated and sanitized
- [ ] Paths are resolved through settings
- [ ] Services use dependency injection
- [ ] Tests don't interfere with each other
- [ ] Error handling for missing directories
- [ ] **Route definitions follow correct ordering**
- [ ] **No route shadowing conflicts**

## 🚨 Common Pitfalls

### 1. Relative Path Fallbacks
```php
// ❌ WRONG - Still dangerous
$paths = $paths ?? new Paths(__DIR__ . '/../../..');
```

### 2. Mixed Path Sources
```php
// ❌ WRONG - Inconsistent path sources
$logPath = $settings['logger']['path'];
$dbPath = __DIR__ . '/../db/users.sqlite'; // Different source!
```

### 3. Missing Validation
```php
// ❌ WRONG - No validation
$uploadPath = '/uploads/' . $_POST['filename'];
file_put_contents($uploadPath, $data); // Dangerous!
```

### 4. Test Path Pollution
```php
// ❌ WRONG - Tests affect each other
$logger = new ActivityLogger('/tmp/shared-test.log');
```

## 📚 References

- [OWASP Path Traversal](https://owasp.org/www-community/attacks/Path_Traversal)
- [PHP Security Best Practices](https://www.php.net/manual/en/security.filesystem.php)
- [Dependency Injection Patterns](https://samuel-gfeller.ch/docs/Dependency-Injection)
- [Configuration Management](https://samuel-gfeller.ch/docs/Configuration)

---

**Remember: Security is not optional. Always use settings-based path configuration and dependency injection!** 🔒✅
