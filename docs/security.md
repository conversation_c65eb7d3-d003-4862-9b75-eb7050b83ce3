# Security Implementation Documentation

This document describes the security measures implemented in the application to protect against common web vulnerabilities and attacks.

## 🔒 Security Overview

The application implements multiple layers of security:

1. **Security Headers** - Protect against XSS, clickjacking, and other attacks
2. **Rate Limiting** - Prevent abuse and brute force attacks
3. **File Protection** - Secure sensitive files and directories
4. **Input Validation** - Sanitize and validate user input
5. **Session Security** - Secure session management

## 🛡️ Security Headers

### Implementation

**File:** `src/Application/Middleware/SecurityHeadersMiddleware.php`

**Registered in:** `config/middleware.php`

### Headers Applied

#### 1. **X-Content-Type-Options: nosniff**
- Prevents MIME type sniffing attacks
- Forces browsers to respect declared content types

#### 2. **X-XSS-Protection: 1; mode=block**
- Enables browser XSS protection
- Blocks pages when XSS attack detected

#### 3. **X-Frame-Options: SAMEORIGIN**
- Prevents clickjacking attacks
- Allows framing only from same origin
- Upgraded to `DENY` for admin/API endpoints

#### 4. **Referrer-Policy: strict-origin-when-cross-origin**
- Controls referrer information sent to other sites
- Protects sensitive URL parameters

#### 5. **Content-Security-Policy (CSP)**
```
default-src 'self';
script-src 'self' 'unsafe-inline' 'unsafe-eval';
style-src 'self' 'unsafe-inline';
img-src 'self' data: https:;
font-src 'self' data:;
connect-src 'self';
frame-ancestors 'self';
base-uri 'self';
form-action 'self'
```

#### 6. **Permissions-Policy**
- Disables dangerous browser features
- Prevents access to camera, microphone, geolocation, etc.

#### 7. **Strict-Transport-Security (HSTS)**
- Forces HTTPS connections (production only)
- Includes subdomains and preload directive

### Configuration

**File:** `config/defaults.php`
```php
$settings['security'] = [
    'force_https' => false, // Set to true in production
    'headers' => [
        // Custom headers can be added here
    ],
];
```

### Environment-Specific Settings

**Development:**
- HSTS disabled
- Relaxed CSP for development tools
- Local IPs whitelisted

**Production:**
- HSTS enabled
- Stricter CSP policies
- Additional security headers

## 🚦 Rate Limiting

### Implementation

**File:** `src/Application/Middleware/RateLimitMiddleware.php`

**Storage:** File-based (configurable directory)

### Rate Limits by Category

#### 1. **Global Endpoints**
- **Limit:** 100 requests per minute
- **Applies to:** All non-categorized endpoints

#### 2. **API Endpoints**
- **Limit:** 60 requests per minute
- **Applies to:** `/api/*` routes

#### 3. **Authentication Endpoints**
- **Limit:** 5 requests per 5 minutes
- **Applies to:** `/auth/*`, `/login` routes
- **Purpose:** Prevent brute force attacks

#### 4. **Admin Panel (Mark)**
- **Limit:** 30 requests per minute
- **Applies to:** `/mark/*` routes

### Configuration

**File:** `config/defaults.php`
```php
$settings['rate_limit'] = [
    'storage_dir' => $settings['root_dir'] . '/var/rate_limits',
    'limits' => [
        'global' => ['requests' => 100, 'window' => 60],
        'api' => ['requests' => 60, 'window' => 60],
        'auth' => ['requests' => 5, 'window' => 300],
        'mark' => ['requests' => 30, 'window' => 60],
    ],
    'whitelist' => ['127.0.0.1', '::1', 'localhost'],
];
```

### Response Headers

When rate limit is active, the following headers are added:
- `X-RateLimit-Limit` - Maximum requests allowed
- `X-RateLimit-Remaining` - Requests remaining in window
- `X-RateLimit-Reset` - Unix timestamp when limit resets
- `X-RateLimit-Window` - Time window in seconds

### Rate Limit Exceeded Response

**Status Code:** 429 Too Many Requests

**Response Body:**
```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Limit: 60 requests per 60 seconds for api endpoints.",
  "retry_after": 60
}
```

## 🗂️ File Protection (.htaccess)

### Protected Files and Directories

**File:** `public/.htaccess`

#### Hidden Files
```apache
<FilesMatch "^\.">
    Require all denied
</FilesMatch>
```

#### Configuration Files
```apache
<FilesMatch "\.(ini|conf|config|log|sql|bak|backup|old|tmp)$">
    Require all denied
</FilesMatch>
```

#### Protected Directories
```apache
RedirectMatch 403 ^/config/
RedirectMatch 403 ^/logs/
RedirectMatch 403 ^/var/
RedirectMatch 403 ^/vendor/
RedirectMatch 403 ^/src/
RedirectMatch 403 ^/tests/
RedirectMatch 403 ^/docs/
```

### PHP Security Settings

```apache
# Disable dangerous PHP functions
php_admin_value disable_functions "exec,passthru,shell_exec,system,proc_open,popen"

# Hide PHP version
php_flag expose_php Off

# Session security
php_value session.cookie_httponly 1
php_value session.cookie_secure 1
php_value session.use_strict_mode 1
php_value session.cookie_samesite "Strict"
```

## 🔐 Session Security

### Configuration

**Implemented via .htaccess and PHP settings:**

- **HttpOnly cookies** - Prevent JavaScript access
- **Secure cookies** - HTTPS only (production)
- **Strict mode** - Regenerate session ID on login
- **SameSite** - Prevent CSRF attacks

### Best Practices

1. **Session regeneration** on privilege changes
2. **Short session timeouts** for sensitive operations
3. **Secure session storage** outside web root
4. **Session validation** on each request

## 🛠️ Implementation Guide

### 1. Enable Security in Production

**File:** `config/env/env.prod.php`
```php
$settings['security']['force_https'] = true;
$settings['rate_limit']['whitelist'] = []; // Remove local IPs
```

### 2. Customize Rate Limits

```php
// Stricter limits for production
$settings['rate_limit']['limits'] = [
    'global' => ['requests' => 60, 'window' => 60],
    'api' => ['requests' => 30, 'window' => 60],
    'auth' => ['requests' => 3, 'window' => 600], // 3 attempts per 10 minutes
    'mark' => ['requests' => 20, 'window' => 60],
];
```

### 3. Add Custom Security Headers

```php
$settings['security']['headers'] = [
    'X-Custom-Header' => 'Custom-Value',
    'X-API-Version' => '1.0',
];
```

### 4. Monitor Security Events

Security events are automatically logged:
- Rate limit violations
- Blocked requests
- Security header violations

## 🧪 Testing Security

### 1. Test Security Headers

```bash
curl -I http://localhost:8080/
```

Expected headers:
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `X-Frame-Options: SAMEORIGIN`
- `Content-Security-Policy: ...`

### 2. Test Rate Limiting

```bash
# Test API rate limit
for i in {1..65}; do
  curl -s -o /dev/null -w "%{http_code}\n" http://localhost:8080/api/dashboard/stats
done
```

Expected: First 60 requests return 200, then 429.

### 3. Test File Protection

```bash
curl http://localhost:8080/config/defaults.php
curl http://localhost:8080/logs/activity.log
```

Expected: 403 Forbidden

## 🚨 Security Monitoring

### Log Analysis

Security events are logged in:
- **Application logs** - `logs/app-YYYY-MM-DD.log`
- **System logs** - Rate limiting, blocked requests
- **Error logs** - Security violations

### Key Metrics to Monitor

1. **Rate limit violations** - Potential abuse
2. **Failed authentication attempts** - Brute force attacks
3. **Blocked file access** - Directory traversal attempts
4. **CSP violations** - XSS attempts
5. **Unusual traffic patterns** - DDoS or bot activity

## 📋 Security Checklist

### Development
- [ ] Security headers middleware enabled
- [ ] Rate limiting configured
- [ ] File protection in place
- [ ] Session security configured
- [ ] Input validation implemented

### Production
- [ ] HTTPS enforced
- [ ] HSTS enabled
- [ ] Stricter rate limits
- [ ] Security monitoring active
- [ ] Regular security updates
- [ ] Backup and recovery plan

### Ongoing
- [ ] Regular security audits
- [ ] Dependency updates
- [ ] Log monitoring
- [ ] Incident response plan
- [ ] Security training

## 🔗 References

- [OWASP Security Headers](https://owasp.org/www-project-secure-headers/)
- [Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [Rate Limiting Best Practices](https://cloud.google.com/architecture/rate-limiting-strategies-techniques)
- [Session Security](https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet)

---

**Remember: Security is an ongoing process, not a one-time implementation!** 🔒🛡️✅
