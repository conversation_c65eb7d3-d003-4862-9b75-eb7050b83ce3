<?php

/**
 * Phinx configuration for SQLite with multiple databases.
 *
 * Usage:
 *   vendor/bin/phinx migrate -c phinx.php -e user
 *   vendor/bin/phinx migrate -c phinx.php -e article
 *   vendor/bin/phinx migrate -c phinx.php -e mark
 */

$dbBasePath = __DIR__ . '/var/db';

return [
    'paths' => [
        'migrations' => [
            'user' => 'resources/migrations/user',
            'article' => 'resources/migrations/article',
            'mark' => 'resources/migrations/mark',
        ],
        'seeds' => 'resources/seeds',
    ],
    'environments' => [
        'default_migration_table' => 'phinx_migration_log',
        'default_environment' => 'user',
        'user' => [
            'adapter' => 'sqlite',
            'name' => $dbBasePath . '/user.sqlite',
            'suffix' => '',
        ],
        'article' => [
            'adapter' => 'sqlite',
            'name' => $dbBasePath . '/article.sqlite',
            'suffix' => '',
        ],
        'mark' => [
            'adapter' => 'sqlite',
            'name' => $dbBasePath . '/mark.sqlite',
            'suffix' => '',
        ],
    ],
];
