# Paths Configuration

This document outlines the path configuration used in the Fretboard Pattern Visualizer application. The paths are managed using the `slim4/paths` package.

## Available Paths

### Base Paths
- `root` - Project root directory (`/`)
- `app` - Main application source code (`/src`)
- `templates` - Template files (`/templates`)
- `config` - Configuration files (`/config`)
- `resources` - Resource files (`/resources`)
- `tests` - Test files (`/tests`)
- `vendor` - Composer dependencies (`/vendor`)

### Web Paths
- `public` - Public web root (`/public`)
- `assets` - Public assets (`/public/assets`)
- `uploads` - File uploads directory (`/public/uploads`)

### System Paths
- `storage` - Application storage (`/var/storage`)
- `cache` - Cache files (`/var/cache`)
- `logs` - Application logs (`/logs`)

### Database Paths
- `user.sqlite` - User module database (`/var/db/user.sqlite`)
- `article.sqlite` - Article module database (`/var/db/article.sqlite`)
- `mark.sqlite` - Mark module database (`/var/db/mark.sqlite`)

## Usage

### In Controllers
```php
use Psr\Container\ContainerInterface;

class YourController
{
    private $paths;

    public function __construct(ContainerInterface $container)
    {
        $this->paths = $container->get('paths');
    }

    public function yourMethod()
    {
        $templatesPath = $this->paths->get('templates');
        $assetsPath = $this->paths->get('assets');
        // ...
    }
}
```

### In Container Definitions
```php
'your.service' => function (ContainerInterface $container) {
    $paths = $container->get('paths');
    return new YourService($paths->get('storage'));
},
```

## Database Configuration

The application uses modular SQLite databases for different components:

### Database Structure
```
var/db/
├── user.sqlite    # User management (authentication, profiles)
├── article.sqlite # Content management (blog posts, pages)
└── mark.sqlite    # Mark panel (admin functionality)
```

### Configuration Files

#### 1. Database Paths in `config/defaults.php`
```php
'connections' => [
    'user' => [
        'driver' => 'pdo_sqlite',
        'database' => $settings['root_dir'] . '/var/db/user.sqlite',
        'prefix' => 'user_',
    ],
    'article' => [
        'driver' => 'pdo_sqlite',
        'database' => $settings['root_dir'] . '/var/db/article.sqlite',
        'prefix' => 'article_',
    ],
    'mark' => [
        'driver' => 'pdo_sqlite',
        'database' => $settings['root_dir'] . '/var/db/mark.sqlite',
        'prefix' => 'mark_',
    ],
],
```

#### 2. Container Services in `config/container.php`
```php
'db.connection.user' => function (ContainerInterface $container) {
    $settings = $container->get('settings');
    $dbPath = $settings['db']['connections']['user']['database'];

    $driver = new Sqlite(['database' => $dbPath]);
    return new Connection(['driver' => $driver]);
},
```

#### 3. Phinx Migration Paths in `phinx.php`
```php
'environments' => [
    'user' => [
        'adapter' => 'sqlite',
        'name' => __DIR__ . '/var/db/user.sqlite',
    ],
    'article' => [
        'adapter' => 'sqlite',
        'name' => __DIR__ . '/var/db/article.sqlite',
    ],
    'mark' => [
        'adapter' => 'sqlite',
        'name' => __DIR__ . '/var/db/mark.sqlite',
    ],
],
```

### Key Principles

1. **Absolute Paths**: All database paths use `$settings['root_dir']` for absolute paths
2. **Modular Design**: Each module has its own database file
3. **Consistent Naming**: Database files follow `{module}.sqlite` pattern
4. **Environment Support**: Test environments can override database paths

### Creating New Module Database

To add a new module database:

1. **Add to defaults.php**:
```php
'your_module' => [
    'driver' => 'pdo_sqlite',
    'database' => $settings['root_dir'] . '/var/db/your_module.sqlite',
    'prefix' => 'your_module_',
],
```

2. **Add container service**:
```php
'db.connection.your_module' => function (ContainerInterface $container) {
    $settings = $container->get('settings');
    $dbPath = $settings['db']['connections']['your_module']['database'];

    $driver = new Sqlite(['database' => $dbPath]);
    return new Connection(['driver' => $driver]);
},
```

3. **Add Phinx environment**:
```php
'your_module' => [
    'adapter' => 'sqlite',
    'name' => __DIR__ . '/var/db/your_module.sqlite',
],
```

4. **Create database file**:
```bash
sqlite3 var/db/your_module.sqlite "CREATE TABLE your_table (id INTEGER PRIMARY KEY);"
```

## Adding New Paths

To add a new path, update the `Paths::class` service definition in `config/container.php`:

```php
Paths::class => function (ContainerInterface $container) {
    $root = $container->get('settings')['root_dir'];
    return new Paths($root); // ResponsiveSk\Slim4Paths\Paths expects string
},
```