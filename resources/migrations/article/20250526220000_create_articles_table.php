<?php

declare(strict_types=1);

namespace article;

use Phinx\Migration\AbstractMigration;

final class CreateArticlesTable extends AbstractMigration
{
    public function change(): void
    {
        $table = $this->table('articles', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'encoding' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
        ]);

        $table
            ->addColumn('id', 'uuid', ['null' => false])
            ->addColumn('title', 'string', ['limit' => 255, 'null' => false])
            ->addColumn('slug', 'string', ['limit' => 255, 'null' => false])
            ->addColumn('excerpt', 'text', ['null' => true])
            ->addColumn('content', 'text', ['null' => true])
            ->addColumn('image', 'string', ['limit' => 500, 'null' => true])
            ->addColumn('author_id', 'uuid', ['null' => true])

            // Product type classification
            ->addColumn('product_type', 'string', [
                'limit' => 20,
                'default' => 'article',
                'null' => false
            ])

            // Article specific type classification
            ->addColumn('article_type', 'string', [
                'limit' => 20,
                'default' => 'blog_post',
                'null' => true
            ])

            // Product subtype for more specific classification
            ->addColumn('product_subtype', 'string', ['limit' => 100, 'null' => true])

            // SKU for product identification
            ->addColumn('sku', 'string', ['limit' => 100, 'null' => true])

            // Price information
            ->addColumn('price', 'decimal', ['precision' => 10, 'scale' => 2, 'null' => true])
            ->addColumn('currency', 'string', ['limit' => 3, 'default' => 'EUR', 'null' => false])

            // Physical product specific fields
            ->addColumn('weight', 'decimal', ['precision' => 8, 'scale' => 3, 'null' => true])
            ->addColumn('length', 'decimal', ['precision' => 8, 'scale' => 2, 'null' => true])
            ->addColumn('width', 'decimal', ['precision' => 8, 'scale' => 2, 'null' => true])
            ->addColumn('height', 'decimal', ['precision' => 8, 'scale' => 2, 'null' => true])

            // Digital product specific fields
            ->addColumn('file_size', 'string', ['limit' => 50, 'null' => true])
            ->addColumn('download_link', 'string', ['limit' => 500, 'null' => true])
            ->addColumn('license_key', 'string', ['limit' => 255, 'null' => true])

            // Service specific fields
            ->addColumn('duration_minutes', 'integer', ['null' => true])
            ->addColumn('service_location', 'string', ['limit' => 255, 'null' => true])

            // Bundle specific fields
            ->addColumn('bundle_items', 'json', ['null' => true])

            // Status and visibility
            ->addColumn('status', 'string', [
                'limit' => 20,
                'default' => 'draft',
                'null' => false
            ])
            ->addColumn('is_featured', 'boolean', ['default' => false, 'null' => false])
            ->addColumn('is_public', 'boolean', ['default' => true, 'null' => false])

            // SEO fields
            ->addColumn('meta_title', 'string', ['limit' => 255, 'null' => true])
            ->addColumn('meta_description', 'text', ['null' => true])
            ->addColumn('meta_keywords', 'text', ['null' => true])

            // Timestamps
            ->addColumn('published_at', 'datetime', ['null' => true])
            ->addColumn('created_at', 'datetime', ['null' => false])
            ->addColumn('updated_at', 'datetime', ['null' => true])
            ->addColumn('deleted_at', 'datetime', ['null' => true])

            // Indexes
            ->addIndex(['slug'], ['unique' => true])
            ->addIndex(['sku'], ['unique' => true])
            ->addIndex(['product_type'])
            ->addIndex(['article_type'])
            ->addIndex(['product_subtype'])
            ->addIndex(['status'])
            ->addIndex(['is_featured'])
            ->addIndex(['is_public'])
            ->addIndex(['published_at'])
            ->addIndex(['created_at'])
            ->create();
    }

    public function down(): void
    {
        $this->table('articles')->drop()->save();
    }
}
