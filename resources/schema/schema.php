<?php

return array (
  'database' => 
  array (
    'default_character_set_name' => 'utf8mb4',
    'default_collation_name' => 'utf8mb4_general_ci',
  ),
  'tables' => 
  array (
    'user' => 
    array (
      'table' => 
      array (
        'table_name' => 'user',
        'engine' => 'InnoDB',
        'table_comment' => '',
        'table_collation' => 'utf8mb4_general_ci',
        'character_set_name' => 'utf8mb4',
        'row_format' => 'Dynamic',
      ),
      'columns' => 
      array (
        'id' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'user',
          'COLUMN_NAME' => 'id',
          'ORDINAL_POSITION' => 1,
          'COLUMN_DEFAULT' => NULL,
          'IS_NULLABLE' => 'NO',
          'DATA_TYPE' => 'int',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => 10,
          'NUMERIC_SCALE' => 0,
          'DATETIME_PRECISION' => NULL,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'int(11)',
          'COLUMN_KEY' => 'PRI',
          'EXTRA' => 'auto_increment',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'first_name' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'user',
          'COLUMN_NAME' => 'first_name',
          'ORDINAL_POSITION' => 2,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'varchar',
          'CHARACTER_MAXIMUM_LENGTH' => 100,
          'CHARACTER_OCTET_LENGTH' => 400,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => NULL,
          'CHARACTER_SET_NAME' => 'utf8mb4',
          'COLLATION_NAME' => 'utf8mb4_general_ci',
          'COLUMN_TYPE' => 'varchar(100)',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'last_name' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'user',
          'COLUMN_NAME' => 'last_name',
          'ORDINAL_POSITION' => 3,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'varchar',
          'CHARACTER_MAXIMUM_LENGTH' => 100,
          'CHARACTER_OCTET_LENGTH' => 400,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => NULL,
          'CHARACTER_SET_NAME' => 'utf8mb4',
          'COLLATION_NAME' => 'utf8mb4_general_ci',
          'COLUMN_TYPE' => 'varchar(100)',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'email' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'user',
          'COLUMN_NAME' => 'email',
          'ORDINAL_POSITION' => 4,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'varchar',
          'CHARACTER_MAXIMUM_LENGTH' => 254,
          'CHARACTER_OCTET_LENGTH' => 1016,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => NULL,
          'CHARACTER_SET_NAME' => 'utf8mb4',
          'COLLATION_NAME' => 'utf8mb4_general_ci',
          'COLUMN_TYPE' => 'varchar(254)',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'updated_at' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'user',
          'COLUMN_NAME' => 'updated_at',
          'ORDINAL_POSITION' => 5,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'datetime',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => 0,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'datetime',
          'COLUMN_KEY' => '',
          'EXTRA' => 'on update current_timestamp()',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'created_at' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'user',
          'COLUMN_NAME' => 'created_at',
          'ORDINAL_POSITION' => 6,
          'COLUMN_DEFAULT' => 'current_timestamp()',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'datetime',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => 0,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'datetime',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'deleted_at' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'user',
          'COLUMN_NAME' => 'deleted_at',
          'ORDINAL_POSITION' => 7,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'datetime',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => 0,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'datetime',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
      ),
      'indexes' => 
      array (
        'PRIMARY' => 
        array (
          1 => 
          array (
            'Table' => 'user',
            'Non_unique' => 0,
            'Key_name' => 'PRIMARY',
            'Seq_in_index' => 1,
            'Column_name' => 'id',
            'Collation' => 'A',
            'Sub_part' => NULL,
            'Packed' => NULL,
            'Null' => '',
            'Index_type' => 'BTREE',
            'Comment' => '',
            'Index_comment' => '',
          ),
        ),
      ),
      'foreign_keys' => NULL,
    ),
    'phinx_migration_log' => 
    array (
      'table' => 
      array (
        'table_name' => 'phinx_migration_log',
        'engine' => 'InnoDB',
        'table_comment' => '',
        'table_collation' => 'utf8mb4_unicode_ci',
        'character_set_name' => 'utf8mb4',
        'row_format' => 'Dynamic',
      ),
      'columns' => 
      array (
        'version' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'phinx_migration_log',
          'COLUMN_NAME' => 'version',
          'ORDINAL_POSITION' => 1,
          'COLUMN_DEFAULT' => NULL,
          'IS_NULLABLE' => 'NO',
          'DATA_TYPE' => 'bigint',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => 19,
          'NUMERIC_SCALE' => 0,
          'DATETIME_PRECISION' => NULL,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'bigint(20)',
          'COLUMN_KEY' => 'PRI',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'migration_name' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'phinx_migration_log',
          'COLUMN_NAME' => 'migration_name',
          'ORDINAL_POSITION' => 2,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'varchar',
          'CHARACTER_MAXIMUM_LENGTH' => 100,
          'CHARACTER_OCTET_LENGTH' => 400,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => NULL,
          'CHARACTER_SET_NAME' => 'utf8mb4',
          'COLLATION_NAME' => 'utf8mb4_unicode_ci',
          'COLUMN_TYPE' => 'varchar(100)',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'start_time' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'phinx_migration_log',
          'COLUMN_NAME' => 'start_time',
          'ORDINAL_POSITION' => 3,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'timestamp',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => 0,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'timestamp',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'end_time' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'phinx_migration_log',
          'COLUMN_NAME' => 'end_time',
          'ORDINAL_POSITION' => 4,
          'COLUMN_DEFAULT' => 'NULL',
          'IS_NULLABLE' => 'YES',
          'DATA_TYPE' => 'timestamp',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => NULL,
          'NUMERIC_SCALE' => NULL,
          'DATETIME_PRECISION' => 0,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'timestamp',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
        'breakpoint' => 
        array (
          'TABLE_CATALOG' => 'def',
          'TABLE_NAME' => 'phinx_migration_log',
          'COLUMN_NAME' => 'breakpoint',
          'ORDINAL_POSITION' => 5,
          'COLUMN_DEFAULT' => '0',
          'IS_NULLABLE' => 'NO',
          'DATA_TYPE' => 'tinyint',
          'CHARACTER_MAXIMUM_LENGTH' => NULL,
          'CHARACTER_OCTET_LENGTH' => NULL,
          'NUMERIC_PRECISION' => 3,
          'NUMERIC_SCALE' => 0,
          'DATETIME_PRECISION' => NULL,
          'CHARACTER_SET_NAME' => NULL,
          'COLLATION_NAME' => NULL,
          'COLUMN_TYPE' => 'tinyint(1)',
          'COLUMN_KEY' => '',
          'EXTRA' => '',
          'PRIVILEGES' => 'select,insert,update,references',
          'COLUMN_COMMENT' => '',
          'IS_GENERATED' => 'NEVER',
          'GENERATION_EXPRESSION' => NULL,
        ),
      ),
      'indexes' => 
      array (
        'PRIMARY' => 
        array (
          1 => 
          array (
            'Table' => 'phinx_migration_log',
            'Non_unique' => 0,
            'Key_name' => 'PRIMARY',
            'Seq_in_index' => 1,
            'Column_name' => 'version',
            'Collation' => 'A',
            'Sub_part' => NULL,
            'Packed' => NULL,
            'Null' => '',
            'Index_type' => 'BTREE',
            'Comment' => '',
            'Index_comment' => '',
          ),
        ),
      ),
      'foreign_keys' => NULL,
    ),
  ),
);