CREATE TABLE `phinx_migration_log` (`version` <PERSON><PERSON><PERSON><PERSON>GER NOT NULL, `migration_name` VARCHAR(100) NULL, `start_time` TIMESTAMP_TEXT NULL, `end_time` TIMESTAMP_TEXT NULL, `breakpoint` BOOLEAN_INTEGER NOT NULL DEFAULT 0, PRIMARY KEY (`version`));
CREATE TABLE `users` (`id` UUID_TEXT NOT NULL, `email` VARCHAR(255) NOT NULL, `username` VARCHAR(50) NOT NULL, `password` VARCHAR(255) NOT NULL, `first_name` VARCHAR(100) NULL, `last_name` VARCHAR(100) NULL, `is_active` BOOLEAN_INTEGER NOT NULL DEFAULT 1, `created_at` DATETIME_TEXT NOT NULL, `updated_at` DATETIME_TEXT NULL, PRIMARY KEY (`id`));
CREATE UNIQUE INDEX `users_email_index` ON `users` (`email` ASC);
CREATE UNIQUE INDEX `users_username_index` ON `users` (`username` ASC);
CREATE TABLE `articles` (`id` UUID_TEXT NOT NULL, `title` VARCHAR(255) NOT NULL, `slug` VARCHAR(255) NOT NULL, `excerpt` TEXT NULL, `content` TEXT NULL, `image` VARCHAR(500) NULL, `author_id` UUID_TEXT NULL, `product_type` VARCHAR(20) NOT NULL DEFAULT 'article', `article_type` VARCHAR(20) NULL DEFAULT 'blog_post', `product_subtype` VARCHAR(100) NULL, `sku` VARCHAR(100) NULL, `price` DECIMAL(10,2) NULL, `currency` VARCHAR(3) NOT NULL DEFAULT 'EUR', `weight` DECIMAL(8,3) NULL, `length` DECIMAL(8,2) NULL, `width` DECIMAL(8,2) NULL, `height` DECIMAL(8,2) NULL, `file_size` VARCHAR(50) NULL, `download_link` VARCHAR(500) NULL, `license_key` VARCHAR(255) NULL, `duration_minutes` INTEGER NULL, `service_location` VARCHAR(255) NULL, `bundle_items` JSON_TEXT NULL, `status` VARCHAR(20) NOT NULL DEFAULT 'draft', `is_featured` BOOLEAN_INTEGER NOT NULL DEFAULT 0, `is_public` BOOLEAN_INTEGER NOT NULL DEFAULT 1, `meta_title` VARCHAR(255) NULL, `meta_description` TEXT NULL, `meta_keywords` TEXT NULL, `published_at` DATETIME_TEXT NULL, `created_at` DATETIME_TEXT NOT NULL, `updated_at` DATETIME_TEXT NULL, `deleted_at` DATETIME_TEXT NULL, PRIMARY KEY (`id`));
CREATE UNIQUE INDEX `articles_slug_index` ON `articles` (`slug` ASC);
CREATE UNIQUE INDEX `articles_sku_index` ON `articles` (`sku` ASC);
CREATE INDEX `articles_product_type_index` ON `articles` (`product_type` ASC);
CREATE INDEX `articles_article_type_index` ON `articles` (`article_type` ASC);
CREATE INDEX `articles_product_subtype_index` ON `articles` (`product_subtype` ASC);
CREATE INDEX `articles_status_index` ON `articles` (`status` ASC);
CREATE INDEX `articles_is_featured_index` ON `articles` (`is_featured` ASC);
CREATE INDEX `articles_is_public_index` ON `articles` (`is_public` ASC);
CREATE INDEX `articles_published_at_index` ON `articles` (`published_at` ASC);
CREATE INDEX `articles_created_at_index` ON `articles` (`created_at` ASC);
