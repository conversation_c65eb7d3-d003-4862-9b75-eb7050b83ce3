<?php

use Phinx\Seed\AbstractSeed;

/**
 * Documentation: https://samuel-gfeller.ch/docs/Database-Migrations#seeding
 */
class UserSeeder extends AbstractSeed
{

    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeders is available here:
     * https://book.cakephp.org/phinx/0/en/seeding.html
     */
    public function run(): void
    {
        $userRows = [
            [
                'id' => 1,
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
            ],
            [
                'id' => 2,
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
            ],
            [
                'id' => 3,
                'first_name' => '<PERSON>',
                'last_name' => 'Hager',
                'email' => '<EMAIL>',
            ],
            [
                'id' => 4,
                'first_name' => '<PERSON><PERSON>',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
            ],
        ];

        $table = $this->table('user');
        $table->insert($userRows)->saveData();
    }
}
