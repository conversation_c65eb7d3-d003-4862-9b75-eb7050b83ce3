/*mobile first min-width sets base and content is adapted to computers.*/
@media (min-width: 100px) {
    main {
        padding-bottom: 4rem;
    }

    main h1 {
        margin-bottom: 0;
        text-align: center;
        margin-top: 1rem;
    }

    #settings-form {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;
        /*padding: 1.5rem 2rem 2rem;*/
        gap: 1rem;
        width: 100%;
        /*border-radius: 40px;*/
        /*box-shadow: 0 2px 10px rgba(255, 255, 255, .1);*/
    }

    .main-settings-container {
        display: flex;
    }

    #permanent-settings-container {
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        width: 100%;
        align-items: center;
        /*gap: min(20%, 5rem);*/
        /*row-gap: 2rem;*/
    }

    #collapsible-settings-container {
        display: grid;
        grid-template-rows: 0fr; /* Start collapsed (0 fraction unit height) */
        overflow: hidden; /* Still needed to hide content when height is 0 */
        transition: grid-template-rows 0.3s ease-out, padding 0.3s ease-out; /* Transition grid-template-rows */
        padding-top: 0;
        padding-bottom: 0;
    }

    #collapsible-settings-container > * {
        min-height: 0;
    }

    #collapsible-settings-container.expanded {
        grid-template-rows: 1fr; /* Expand to fill available space (1 fraction unit height) */
        padding-bottom: 1rem;
    }

    #collapsible-settings-div {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-evenly;
        gap: 2rem;
    }

    #more-settings-btn {
        display: flex;
        align-items: center;
        gap: 12px;
        padding-top: 10px;
        font-size: 0.9rem;
        color: var(--subdued-text-color);
        cursor: pointer;
    }

    #more-settings-btn:hover {
        filter: brightness(110%);
    }

    #more-settings-btn img {
        transition: transform 0.3s ease-in-out;
        width: 12px;
    }
    #more-settings-btn img.open {
        /* rotate 180 deg 0.3s */
        transform: rotate(180deg);
    }

    #settings-form .form-input-div {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0;
    }

    #settings-form .form-input-div input {
        width: 4rem;
    }

    #settings-form .form-input-div label {
        padding: 0;
    }

    .main-settings-container {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        gap: min(10%, 4rem);
        row-gap: 2rem;
        justify-content: center;
    }

    #scale-degree-input-group.hidden {
        display: none;
    }
    .button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .scale-degree-button, #chord-scale-degree-input {
        padding: 4px 8px;
        font-size: 1rem;
        border: 1px solid var(--border-accent-1-color);
        border-radius: 8px;
        background-color: var(--background-accent-2-color);
        cursor: pointer;
        transition: background-color 0.2s ease;
        min-width: 35px;
        max-width: 40px;
        min-height: 30px;
    }

    .scale-degree-button:hover {
        background-color: var(--background-accent-3-color);
    }

    .scale-degree-button.selected {
        background-color: var(--background-accent-5-color);
        color: var(--black-white-text-color);
        border-color: var(--border-accent-1-color);
    }

    .scale-degree-button.min {
        color: var(--note-color-minor);
    }

    .scale-degree-button.maj {
        color: var(--note-color-major);
    }

    .scale-degree-button.dim {
        color: var(--note-color-diminished);
    }

    .scale-degree-button.aug {
        color: var(--note-color-augmented);
    }

    #color-settings-button-container {
        position: relative;
        width: auto;
    }

    #color-settings-button-container #color-settings-toggle {
        min-width: auto;
    }

    #color-settings-panel {
        position: absolute;
        top: 100%; /* Position below the button */
        left: 0;
        z-index: 10; /* Ensure it appears above other content */
        margin-top: 10px; /* Add space between button and panel */
        display: none;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        gap: 20px;
        width: 200px;
        border: 1px solid var(--border-accent-2-color, #444);
        padding: 1rem;
        border-radius: 20px;
        background-color: var(--modal-box-background, #333);
        max-width: 300px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    #color-settings-panel.active-panel {
        display: flex;
    }

    #color-settings-panel label {
        font-size: 0.8rem;
    }
}

/*portrait tablets, portrait iPad, landscape e-readers, landscape 800x480 or 854x480 phones*/
@media (min-width: 641px) {

}

/*At this breakpoint it looks good to have two columns*/
@media (min-width: 755px) {

}

/*tablet, landscape iPad, lo-res laptops ands desktops*/
@media (min-width: 961px) {
}