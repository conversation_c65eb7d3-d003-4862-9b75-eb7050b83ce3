/* Toggle Switch Styles */
.toggle-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-label {
    font-weight: 500;
    margin: 0 10px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: auto;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 50%; /* Center vertically */
    transform: translateY(-50%); /* Center vertically */
    height: 7px; /* Explicitly set height to 10px */
    left: 2px;
    right: 0;
    bottom: 0;
    background-color: var(--subdued-text-color);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    bottom: 0px;
    left: -3px;
    background-color: var(--primary-color);
    transition: .4s;
    top: 50%; /* Center vertically */
    transform: translateY(-50%); /* Center vertically */
}

/*input:checked + .slider {*/
/*    background-color: #2196F3;*/
/*}*/

/*input:focus + .slider {*/
/*    box-shadow: 0 0 1px #2196F3;*/
/*}*/

input:checked + .slider:before {
    left: 0;
    transform: translateX(35px) translateY(-50%); /* Keep vertical centering while moving */

}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}
