:root {
    --string-color: #676767;
    --note-color-default: #a64dff;
    --note-color-major: #ee2929;
    --note-color-minor: #2e8bff;
    --note-color-augmented: #ff9d00;
    --note-color-diminished: #12c712;
    --note-text-color: var(--black-white-text-color)
}

[data-theme="dark"] {
    --string-color: #c1c1c1;
}

@media (min-width: 100px) {
    #virtual-fretboard {
        display: flex;
        flex-direction: row;
        width: 100%;
        /*padding-bottom: 50px;
        padding-right: 40px;*/

    }

    #fretboard-for-pattern {
        display: flex;
        flex-direction: row;
        /*min-width: 300px;*/
        width: auto;
        /*padding-right: 40px;*/
        padding-top: 60px;
    }

    .string {
        display: flex;
        flex-direction: column;
        width: 3rem;
        align-items: center; /* This will vertically center the <hr> line */
        justify-content: space-between;
        position: relative;
        /*height: 30px;*/
        border-top: 3px solid var(--string-color);
    }

    .string::after {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        height: 100%;
        width: 1px;
        background-color: var(--string-color);
        z-index: 0;
        transform: translateX(-50%);
    }

    .fret-position {
        flex-grow: 1;
        /*height: 100%;*/
        border-top: 1px solid var(--string-color);
        border-bottom: 1px solid var(--string-color);
        position: relative;
        z-index: 1;
        width: 100%;
        height: 66px;
    }

    .fret-position:hover, .string-name:hover {
        /*background-color: rgba(255, 255, 255, 0.1);*/
    }

    .string-name {
        position: absolute;
        top: -30px;
        font-size: 20px;
        color: #d0d0d0;
        width: 40px;
        line-height: 30px;
    }

    .fret-helper {
        top: -10px;
        /*left: 50%;*/
        max-width: 10px;
        height: 10px;
        width: 10px;
        background-color: #868686;
        border-radius: 50%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }

    .key-note-indicator {
        width: 10px;
        height: 10px;
        background-color: #005400;
        border: 1px solid #129b00;
        /*border-radius: 50%;*/
        bottom: 50%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%) translateY(50%);
        z-index: 2;
    }

    .highlighted-note-number, .string-name span {
        width: 32px;
        height: 32px;
        background-color: var(--background-color);
        /*color: #ff7a1c;*/
        font-weight: bold;
        bottom: 50%;
        position: absolute;
        left: 50%;
        transform: translateX(-50%) translateY(50%);
        z-index: 2;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: normal;
        color: #12c712;
    }

    .string-name span {
        color: var(--primary-text-color);
    }

    .highlighted-note-number {
        border-radius: 50%;
        /*border: 1px solid var(--border-accent-2-color);*/
    }

    span.highlighted-note-number.root {
        /*Changed in ColorSettingController depending on brightness*/
        outline: 2px dashed var(--black-white-text-color);

    }

    .major-span {
    }

    /* Default color for others */
    span.highlighted-note-number {
        color: var(--note-text-color);
        background-color: var(--note-color-default);
    }

    span.highlighted-note-number.major,
    span.highlighted-note-number.maj {
        color: var(--note-text-color);
        background-color: var(--note-color-major);
    }

    span.highlighted-note-number.minor,
    span.highlighted-note-number.min {
        color: var(--note-text-color);
        background-color: var(--note-color-minor);
    }

    span.highlighted-note-number.augmented,
    span.highlighted-note-number.aug {
        color: var(--note-text-color);
        background-color: var(--note-color-augmented);
    }

    span.highlighted-note-number.diminished,
    span.highlighted-note-number.dim {
        color: var(--note-text-color);
        background-color: var(--note-color-diminished);
    }



}

@media (min-width: 961px) {
    #virtual-fretboard {
        width: 80%;
    }
}
