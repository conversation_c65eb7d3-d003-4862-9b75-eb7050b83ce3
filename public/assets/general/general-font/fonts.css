@font-face {
    font-family: SF-Pro-Text;
    src: url(../general-font/SF-Pro/SF-Pro.ttf) format('truetype');
    font-weight: normal;
}

@font-face {
    font-family: SF-Pro-Text;
    src: url(../general-font/SF-Pro/SF-Pro-Text-Regular.otf) ;
    font-weight: normal;
}

@font-face {
    font-family: SF-Pro-Text;
    src: url(../general-font/SF-Pro/SF-Pro-Text-Bold.otf);
    font-weight: bold;
}

@font-face {
    font-family: SF-Pro-Text;
    src: url(../general-font/SF-Pro/SF-Pro-Text-Medium.otf);
    font-weight: 500;
}

@font-face {
    font-family: SF-Pro-Text;
    src: url(../general-font/SF-Pro/SF-Pro-Text-Thin.otf);
    font-weight: 300;
}

@font-face {
    font-family: SF-Pro Display;
    src: url(../general-font/SF-Pro/SF-Pro-Display-Heavy.otf);
    font-weight: 800;
}

@font-face {
    font-family: SF-Pro Display;
    src: url(../general-font/SF-Pro/SF-Pro-Display-Bold.otf);
    font-weight: bold;
}

@font-face {
    font-family: SF-Pro Display;
    src: url(../general-font/SF-Pro/SF-Pro-Display-Semibold.otf);
    font-weight: 500;
}

@font-face {
    font-family: SF-Pro Display;
    src: url(../general-font/SF-Pro/SF-Pro-Display-Medium.otf);
    font-weight: 400;
}

@font-face {
    font-family: SF-Pro Display;
    src: url(../general-font/SF-Pro/SF-Pro-Display-Regular.otf);
    font-weight: 300;
}


@font-face {
    font-family: DM-Sans;
    src: url(../general-font/DMSans-Regular.ttf);
}

@font-face {
    font-family: VarelaRound;
    src: url(../general-font/VarelaRound-Regular.ttf);
    font-weight: 400;
}