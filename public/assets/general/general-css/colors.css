/*This file contains the colors for the light and dark theme*/
/*Colors for light mode*/
:root {
    /*Main colors*/
    --primary-color: #282A36;
    --accent-color: #719E40;

    /*Background colors*/
    --background-color: #d0d0d0;
    /*Accents darker than bright background*/
    --background-accent-1-color: #c4c4c4;
    --background-accent-2-color: #b6b6b6;
    --background-accent-3-color: #989898;
    --background-accent-4-color: #989898;
    --background-accent-5-color: #989898;
    /*Accents lighter than bright background*/
    --background-accent-lighter-1-color: #e3e3e3;

    --modal-box-background: #dcdcdc;

    /*Border*/
    --border-accent-1-color: #c6c6c6;
    --border-accent-2-color: #bfbebe;
    --border-accent-3-color: #bfbfbf;

    /*Text*/
    --primary-text-color: #2e3e50;
    --text-accent-color: #5f8535;
    --subdued-text-color: #616161;
    --title-color: #282A36;
    --black-white-text-color: black;
    /*Text color when the background of that text is the primary (darker than normal background) color*/
    --text-color-on-primary-color-background: #c4c4c4;
    --accent-text-color-on-primary-color-background: whitesmoke;

    /*Filters*/
    /*Style the black svg icons to the primary color https://angel-rs.github.io/css-color-filter-generator/*/
    --primary-color-filter: brightness(0) saturate(100%) invert(23%) sepia(11%) saturate(890%) hue-rotate(193deg) brightness(89%) contrast(96%);
    --accent-color-filter: brightness(0) saturate(100%) invert(52%) sepia(55%) saturate(438%) hue-rotate(46deg) brightness(97%) contrast(87%);
    /*Less bright version of the accent color*/
    --accent-darker-color-filter: brightness(0) saturate(100%) invert(54%) sepia(78%) saturate(312%) hue-rotate(46deg) brightness(70%) contrast(89%);
    /*Icon on the dark primary color background*/
    --icon-filter-dark-background: brightness(0) saturate(100%) invert(68%) sepia(3%) saturate(260%) hue-rotate(155deg) brightness(98%) contrast(91%);

    /*Buttons*/
    --btn-color: #f5f5f5;
    --btn-orange-color: #ffc081;
    --btn-red-color: #ff8d81;

    /*Black or white rgba*/
    --bw-r: 0;
    --bw-g: 0;
    --bw-b: 0;
}

[data-theme="dark"] {
    /*Main colors*/
    --primary-color: #3f495b;
    --accent-color: #719E40;

    /*Background colors*/
    --background-color: #101213;
    /*Accents brighter than dark background but darker than white background on light mode*/
    --background-accent-1-color: #2c343d;
    --background-accent-2-color: #22262c;
    --background-accent-3-color: #2c3239;
    --background-accent-4-color: #2c343d;
    --background-accent-5-color: #3c4652;
    --background-accent-lighter-1-color: var(--background-accent-2-color);

    --modal-box-background: #262b31;

    /*Text*/
    --primary-text-color: #c3cad0;
    --text-accent-color: var(--accent-color);
    --title-color: #c3cad0;
    --black-white-text-color: white;
    --subdued-text-color: #a6a6a6;
    /*Text color when the background of that text is the primary color*/
    --text-color-on-primary-color-background: #9b9b9b;
    --accent-text-color-on-primary-color-background: whitesmoke;

    /*Border*/
    --border-accent-1-color: #3c4143;
    --border-accent-2-color: #474d50;
    --border-accent-3-color: #444;

    /*Filters*/
    /*Style the black svg icons to the primary color https://angel-rs.github.io/css-color-filter-generator*/
    --primary-color-filter: brightness(0) saturate(100%) invert(72%) sepia(0%) saturate(1%) hue-rotate(321deg) brightness(85%) contrast(88%);
    --accent-color-filter: brightness(0) saturate(100%) invert(54%) sepia(78%) saturate(312%) hue-rotate(46deg) brightness(65%) contrast(89%);
    --accent-darker-color-filter: brightness(0) saturate(100%) invert(54%) sepia(78%) saturate(312%) hue-rotate(46deg) brightness(65%) contrast(89%);
    --icon-filter-dark-background: var(--primary-color-filter);

    /*Buttons*/
    --btn-color: #464f5d;
    --btn-orange-color: #c4884e;
    --btn-red-color: #da5f52;

    /*Black or white rgba*/
    --bw-r: 255;
    --bw-g: 255;
    --bw-b: 255;
}

