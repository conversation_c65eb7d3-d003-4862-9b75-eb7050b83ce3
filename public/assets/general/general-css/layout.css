/*This file contains CSS rules that define the general layout such as styles for the positioning of elements on the page
especially elements from the layout.html.php*/

/*mobile first min-width sets base and content is adapted to computers.*/
@media (min-width: 100px) {
    /*Default styles for common HTML elements (tag name as selectors)*/
    body, html {
        min-height: 100vh;
        margin: 0;
        padding: 0;
        overflow-x: hidden; /*Needed for flash messages sliding in*/
        background: var(--background-color);
    }

    * {
        /*border-box specifies that the borders and padding should be attributed to element's width
        and height and not overflowing to the parent.
        https://developer.mozilla.org/en-US/docs/Web/CSS/box-sizing*/
        box-sizing: border-box;
        font-family: SF-Pro-Text, CenturyGothic, Geneva, AppleGothic, sans-serif;
        transition: background-color 250ms, color 250ms, border-color 250ms;
    }

    a {
        color: var(--text-accent-color);
        text-decoration: none;
    }

    a:hover:not(a.currently-editable):not(nav a):not(a.btn) {
        text-decoration: underline;
        text-decoration-style: dotted;
    }

    h2, h1, h3 {
        color: var(--title-color);
        font-family: VarelaRound, Helvetica, sans-serif;
        font-weight: normal;
    }

    h1 {
        font-size: 2em;
    }

    h2 {
        font-size: 2em;
    }

    h3 {
        font-size: 1.5em;
    }

    #wrapper {
        display: flex;
        flex-direction: column;
        /*Using new dvh unit for dynamic viewport height. svh (small vh) does not work as the footer would not stick
        to the bottom when scrolling the mobile navbar height up, it has to reposition itself to the bottom
        which works with dvh IF the navbar actually disappears on scrolling.
        Previously used a js hack: https://css-tricks.com/the-trick-to-viewport-units-on-mobile*/
        height: 100dvh;
    }

    header {
        flex: none;
        margin: 1em 0 1em 0;
    }

    main {
        /*width: 100%;*/
        padding: 1em 0.5em 1em 0.5em; /*Not margin, it creates a scrollbar because of wrapper 100vh*/
        /*Prevent Browser from letting flex items shrink to smaller than their content's default minimum size.*/
        display: flex;
        flex-direction: column;
        flex: 1 0 auto;
        border-radius: 20px;
        align-items: center;
        /*font-size: 18px;*/
        gap: 2rem;
    }

    main *:where(:not(h1):not(h2):not(h3):not(a:not(.no-style-a))) {
        color: var(--primary-text-color);
    }

    .no-style-a, .no-style-a:hover {
        font-weight: normal;
        text-decoration: none !important;
    }

    footer {
        flex: none;
        /*Prevent Browser from letting flex items shrink to smaller than their content's default minimum size.*/
        flex-shrink: 0;
        background: var(--primary-color);
        padding: 10px;
        text-align: center;
        color: #9b9b9b;
        font-size: 0.9em;
        margin-top: 1em;
        border-radius: 999px 999px 0 0;
    }

    footer a {
        color: var(--text-color-on-primary-color-background);
    }

    footer a:hover {
        color: var(--accent-text-color-on-primary-color-background);
    }

    .footer-icon {
        width: 1em;
        vertical-align: middle;
    }

    #github-icon {
        width: 1.3em;
    }

    .footer-icon:hover {
        filter: brightness(140%);
    }

    address {
        font-style: normal;
    }
}

@media (min-width: 400px) {
    main {
        padding: 3em 1em 1em 1em; /*Not margin, it creates a scrollbar because of wrapper 100vh*/
    }
}

@media (min-width: 641px) {
    /*portrait tablets, portrait iPad, landscape e-readers, landscape 800x480 or 854x480 phones*/
    #wrapper {
        margin: 0 2em;
    }
}

@media (min-width: 961px) {
    /*tablet, landscape iPad, lo-res laptops ands desktops*/
    #wrapper {
        height: 100vh;
        width: 80%;
        margin: auto;
    }

    header {
        margin: 20px 0 1em 0;
    }
}

@media (min-width: 1025px) {
    /*big landscape tablets, laptops, and desktops*/

}

@media (min-width: 1281px) {
    /*hi-res laptops and desktops*/
}
