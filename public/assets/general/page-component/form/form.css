:root {
    --form-input-background-color: rgba(255, 255, 255, 0.6);
    /*dark pink #660066*/
}

[data-theme="dark"] {
    --form-input-background-color: var(--background-accent-5-color);
}

/*mobile first min-width sets base and content is adapted to computers.*/
@media (min-width: 100px) {
    .page-form-container {
        width: 100%;
        margin: 30px 0;
        border-radius: 37px;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1), 0 6px 20px 0 rgba(0, 0, 0, 0.1);
        position: relative;
        font-size: 17px;
        max-width: 625px;
        padding: 20px 15px;
    }

    .form-input-div {
        position: relative;
        margin-bottom: 20px;
    }

    .form-input-div:first-of-type {
        padding-top: 0;
    }

    .form-input-div:last-of-type {
        padding-bottom: 0;
    }

    .form-input-div label {
        color: var(--black-white-text-color);
    }

    .form-input-div label:not(.form-radio-input) {
        padding: 6px 15px;
        display: inline-block; /*For label to take into account padding*/
        letter-spacing: 0.02em;
    }

    /*Radio button styling https://moderncss.dev/pure-css-custom-styled-radio-buttons/*/
    .form-input-div label.form-radio-input, .form-radio-input {
        display: grid;
        grid-template-columns: 1em auto;
        gap: 0.5em;
        margin-top: 10px;
        margin-left: 7px;
        cursor: pointer;
    }

    .form-radio-input input[type="radio"] {
        appearance: none;
        background-color: var(--accent-color);
        margin: 0;
        font: inherit; /*Inherit font size*/
        width: 1.15em;
        height: 1.15em;
        border: 0.15em solid var(--primary-color);
        border-radius: 50%;
        /*adjustments so the input stays horizontally centered in relation to the first line of the label text*/
        /*transform: translateY(-0.075em);*/
        display: grid;
        place-content: center;
        cursor: pointer;
    }

    .form-radio-input input[type="radio"]:before {
        content: "";
        width: 0.65em;
        height: 0.65em;
        border-radius: 50%;
        transform: scale(0);
        transition: 120ms transform ease-in-out;
        box-shadow: inset 1em 1em var(--primary-color);
    }

    .form-radio-input input[type="radio"]:checked:before {
        transform: scale(1);

    }

    .form-input-div input::placeholder, .form-input-div textarea::placeholder {
        /*color: darkgrey;*/
    }

    .form-input-div input:not(input[type="radio"], input[type="color"]), .form-input:not(input[type="radio"], input[type="color"]),
    .form-input-div textarea, textarea.form-input, .form-input-div select, select.form-input {
        background-color: var(--form-input-background-color);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.13);
        border: none;
        font-size: 17px;
        width: 100%; /*Container should reduce width*/
        display: block;
        box-sizing: border-box;
        padding: 10px 15px;
        border-radius: 60px;
        color: var(--black-white-text-color);
        letter-spacing: 0.02em;
        position: relative;
        z-index: 1;
    }

    .color-picker-group {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 0.5rem;
        cursor: pointer;
    }

    .color-picker-group input[type="color"] {
        padding: 0;
        border-radius: 20px;
        background: none;
        border: none;
    }

    .form-input-div input:not(input[type="radio"]):hover, .form-input:not(input[type="radio"]):hover,
    .form-input-div textarea:hover, textarea.form-input:hover, .form-input-div select:hover, select.form-input:hover {
        /*Keep the same box shadow on hover*/
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.13);
        filter: brightness(110%);
    }

    .form-input-div input:not(input[type="radio"]):focus, .form-input:not(input[type="radio"]):focus {
        filter: brightness(120%);
        border-bottom: none;
    }

    .form-input:disabled, .form-select:disabled, .form-radio-input input:disabled,
    .form-input-div input:disabled, .form-input-div select:disabled, .form-input-div input[type="radio"]:disabled {
        /*Important needed as specificity is higher when setting the non-disabled value*/
        background-color: var(--background-accent-2-color) !important;
        color: grey;
    }

    .form-input-div select, .form-select {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        font-size: 15px;
        width: 100%;
        display: block;
        padding: 10px 15px;
        border-radius: 60px;
        letter-spacing: 0.01em;
    }

    .form-input-div textarea, textarea.form-input {
        border-radius: 20px;
        padding: 15px;
    }

    .form-input-div input[type=submit], .submit-btn, .form-btn {
        width: fit-content;
        min-width: 160px;
        float: right;
        color: var(--black-white-text-color);
        border-radius: 99px;
        padding: 12px 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        background-color: var(--form-input-background-color);
        letter-spacing: 0.05em;
        border: none;
        cursor: pointer;
        font-size: 100%;
        white-space: normal; /*Break when not enough space*/
        transition: filter 250ms;
    }

    .modal-form input[type=submit]:hover:enabled, .submit-btn:hover:enabled {
        filter: brightness(90%);
    }

    .modal-form input[type=submit]:focus:enabled, .submit-btn:focus:enabled {
        filter: brightness(80%) saturate(140%);
    }

    .form input[type=submit]:disabled, .submit-btn:disabled {
        background: var(--background-accent-2-color);
        color: grey;
        cursor: default;
    }

    /*When form input group is faulty after submit (server response)*/
    .input-group-error label, label.invalid-input {
        font-weight: bold;
    }

    .input-group-error input, input.invalid-input, textarea.invalid-input {
        border-bottom: 2px solid #c0000a !important; /*Important as form is overwritten by template specific css*/
    }

    .err-msg {
        padding-top: 10px;
        display: block;
        color: #c0000a !important;
        font-size: 15px;
    }

    form .err-msg {
        padding-left: 15px;
    }

    #form-general-error-msg {
        margin-bottom: 20px;
    }

    .success-msg {
        font-weight: bold;
    }

    .input-warning {
        font-weight: 400;
        margin-top: 10px;
        display: inline-block;
        color: #d66500;
    }

    .content-below-input {
        margin-left: 15px;
    }
}

@media (min-width: 641px) {
    /*portrait tablets, portrait iPad, landscape e-readers, landscape 800x480 or 854x480 phones*/
    .page-form-container {
        width: 90%;
        padding: 25px 25px;
    }
}

@media (min-width: 961px) {
    /*tablet, landscape iPad, lo-res laptops ands desktops*/
    .page-form-container {
        width: 60%;
        /*box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);*/
    }
}

@media (min-width: 1025px) {
    /*big landscape tablets, laptops, and desktops*/

}

@media (min-width: 1281px) {
    /*hi-res laptops and desktops*/
}
