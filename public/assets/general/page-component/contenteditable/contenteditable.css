/*mobile first min-width sets base and content is adapted to computers.*/
@media (min-width: 100px) {
    [contenteditable="true"] {
        cursor: text;
    }

    #outer-contenteditable-heading-container {
        margin-bottom: 25px;
    }

    #outer-contenteditable-heading-container h1 {
        display: inline-block;
        /*Remove bottom margin on h1 and put it on h1 container in case first and last name wrap*/
        margin-bottom: 0;
        padding: 5px 5px 5px 3px;
        overflow-wrap: anywhere;
        white-space: break-spaces;
    }

    #outer-contenteditable-heading-container[data-deleted="1"] h1 {
        color: orangered;
    }

    /*Clear float*/
    #outer-contenteditable-heading-container::after {
        content: "";
        clear: both;
        display: table;
    }

    /*Div containing first or last name header*/
    .inner-contenteditable-heading-div {
        float: left; /*Prevent not hoverable whitespace between partial header divs*/
    }

    .contenteditable-field-container {
        position: relative;
        display: inline-block;
        padding-right: 15px;
    }


    .contenteditable-edit-icon, .contenteditable-save-icon {
        display: none;
        position: absolute;
        width: 20px;
        padding: 2px;
        border-radius: 99px;
        border: 1px solid black; /*The actual color is set by the filter*/
        /*The filter here is so that the background is always correct (even if there is no filter otherwise)*/
        filter: var(--primary-color-filter);
        background: rgba(93, 87, 29, 0.18); /*This is a recreation of this color #d8dee8; with the filter*/
        right: -7px;
        top: -3px;
        z-index: 1;
    }

    .contenteditable-field-container:hover .contenteditable-edit-icon, .always-displayed-icon {
        display: inline-block;
    }

    /*Style next sibling https://stackoverflow.com/a/12574836/9013718 (~ works better than + actually as it doesn't
        have to be immediate next sibling. LanguageTool extension puts a <lt-highlighter> element before h1)*/
    /*Display outline on h1 when hover on edit icon and when contenteditable is true*/
    .contenteditable-edit-icon:hover ~ h1, .inner-contenteditable-heading-div h1[contenteditable="true"] {
        outline: 3px solid var(--primary-color);
        border-radius: 10px;
        background: var(--background-accent-1-color);
    }

    /*Display outline on span element*/
    .contenteditable-edit-icon:hover ~ span, .contenteditable-field-container span[contenteditable="true"] {
        outline: 2px solid var(--primary-color);
        border-radius: 5px;
        background: var(--background-accent-1-color);
    }

    .contenteditable-placeholder[contenteditable=true]:empty:before {
        content: attr(data-placeholder);
        color: gray;
    }
}

/*portrait tablets, portrait iPad, landscape e-readers, landscape 800x480 or 854x480 phones*/
@media (min-width: 641px) {

}