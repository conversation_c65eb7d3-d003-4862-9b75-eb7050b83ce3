/* empty css     */import{c as h,l as u,m as w}from"./core-lNA10VOR.js";import{A as p,N as y,T as v}from"./modules-ui-C0SaV7HS.js";import{F as L}from"./modules-forms-C85FcS_6.js";import{g as r}from"./vendor-gsap-D3jsOSc0.js";import{M as S,D as T,T as A,a as b}from"./components-BQ0rsSo9.js";import{m as g}from"./vendor-alpine-Cm4okMZt.js";class E{constructor(){this.isInitialized=!1,this.animations=[]}init(){this.isInitialized||(this.setupHeroAnimations(),this.setupFeatureCards(),this.setupStatsCounter(),this.setupTestimonials(),this.setupCTA(),this.isInitialized=!0)}setupHeroAnimations(){const e=document.querySelector(".hero-section");if(!e)return;const t=e.querySelector("h1");if(t){const a=r.fromTo(t,{opacity:0,y:50},{opacity:1,y:0,duration:1,ease:"power2.out",delay:.2});this.animations.push(a)}const i=e.querySelector(".hero-subtitle");if(i){const a=r.fromTo(i,{opacity:0,y:30},{opacity:1,y:0,duration:.8,ease:"power2.out",delay:.4});this.animations.push(a)}const o=e.querySelectorAll(".hero-cta");if(o.length){const a=r.fromTo(o,{opacity:0,y:20},{opacity:1,y:0,duration:.6,ease:"power2.out",delay:.6,stagger:.1});this.animations.push(a)}const s=e.querySelector(".hero-image");if(s){const a=r.fromTo(s,{opacity:0,scale:.8},{opacity:1,scale:1,duration:1.2,ease:"power2.out",delay:.3});this.animations.push(a)}}setupFeatureCards(){const e=document.querySelectorAll(".feature-card");e.length&&e.forEach((t,i)=>{const o=r.fromTo(t,{opacity:0,y:50},{opacity:1,y:0,duration:.8,ease:"power2.out",scrollTrigger:{trigger:t,start:"top 80%",end:"bottom 20%",toggleActions:"play none none reverse"},delay:i*.1});this.animations.push(o),t.addEventListener("mouseenter",()=>{r.to(t,{duration:.3,y:-8,scale:1.02,ease:"power2.out"})}),t.addEventListener("mouseleave",()=>{r.to(t,{duration:.3,y:0,scale:1,ease:"power2.out"})})})}setupStatsCounter(){const e=document.querySelector(".stats-section");if(!e)return;e.querySelectorAll(".stat-number").forEach(i=>{const o=parseInt(i.textContent.replace(/[^\d]/g,"")),s=i.textContent.replace(/[\d]/g,""),a=r.fromTo(i,{textContent:0},{textContent:o,duration:2,ease:"power2.out",snap:{textContent:1},scrollTrigger:{trigger:i,start:"top 80%",toggleActions:"play none none none"},onUpdate:function(){i.textContent=Math.ceil(this.targets()[0].textContent)+s}});this.animations.push(a)})}setupTestimonials(){const e=document.querySelector(".testimonials-carousel");if(!e)return;const t=e.querySelectorAll(".testimonial-slide"),i=e.querySelectorAll(".testimonial-dot");if(!t.length)return;let o=0;const s=t.length,a=()=>{o=(o+1)%s,this.showTestimonial(o,t,i)},c=setInterval(a,5e3);e.addEventListener("mouseenter",()=>{clearInterval(c)}),e.addEventListener("mouseleave",()=>{setInterval(a,5e3)}),i.forEach((l,d)=>{l.addEventListener("click",()=>{o=d,this.showTestimonial(o,t,i)})}),this.showTestimonial(0,t,i)}showTestimonial(e,t,i){t.forEach(o=>{r.to(o,{opacity:0,x:-50,duration:.3,ease:"power2.out"})}),r.to(t[e],{opacity:1,x:0,duration:.5,ease:"power2.out",delay:.2}),i.forEach((o,s)=>{s===e?o.classList.add("active"):o.classList.remove("active")})}setupCTA(){const e=document.querySelector(".cta-section");if(!e)return;const t=r.fromTo(e,{opacity:0,y:50},{opacity:1,y:0,duration:1,ease:"power2.out",scrollTrigger:{trigger:e,start:"top 80%",toggleActions:"play none none reverse"}});this.animations.push(t);const i=e.querySelector(".cta-button");i&&r.to(i,{scale:1.05,duration:1,ease:"power2.inOut",yoyo:!0,repeat:-1,delay:2})}destroy(){this.animations.forEach(e=>{e.kill&&e.kill()}),this.animations=[],this.isInitialized=!1}}class M{constructor(){this.isInitialized=!1,this.loginForm=null,this.passwordToggle=null}init(){this.isInitialized||(this.setupLoginForm(),this.setupPasswordToggle(),this.setupSocialLogin(),this.setupDemoCredentials(),this.isInitialized=!0)}setupLoginForm(){this.loginForm=document.getElementById("loginForm"),this.loginForm&&this.loginForm.addEventListener("submit",async e=>{e.preventDefault(),await this.handleLogin(e.target)})}async handleLogin(e){const t=new FormData(e),i=t.get("email"),o=t.get("password");t.get("remember-me");const s=e.querySelector("#loginButton"),a=e.querySelector("#loginButtonText"),c=e.querySelector("#loginSpinner");try{if(this.setLoadingState(s,a,c,!0),i==="<EMAIL>"&&o==="password")await new Promise(l=>setTimeout(l,2e3)),window.showNotification&&window.showNotification("Login successful! Welcome back.","success"),setTimeout(()=>{window.location.href="/dashboard"},1500);else throw new Error("Invalid credentials")}catch(l){if(window.showNotification){const d=l.message.includes("Invalid")?"Invalid email or password. Please try again.":"Login failed. Please try again.";window.showNotification(d,"error")}}finally{this.setLoadingState(s,a,c,!1)}}setLoadingState(e,t,i,o){!e||!t||!i||(o?(e.disabled=!0,t.classList.add("hidden"),i.classList.remove("hidden")):(e.disabled=!1,t.classList.remove("hidden"),i.classList.add("hidden")))}setupPasswordToggle(){const e=document.getElementById("password"),t=document.querySelector('[onclick="togglePassword()"]');!e||!t||(t.removeAttribute("onclick"),t.addEventListener("click",()=>{this.togglePasswordVisibility(e,t)}),window.togglePassword=()=>{this.togglePasswordVisibility(e,t)})}togglePasswordVisibility(e,t){const i=t.querySelector("svg");e.type==="password"?(e.type="text",i.innerHTML=`
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
      `):(e.type="password",i.innerHTML=`
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      `)}setupSocialLogin(){const e=document.querySelector('[onclick="loginWithGoogle()"]');e&&(e.removeAttribute("onclick"),e.addEventListener("click",()=>{this.handleSocialLogin("google")}));const t=document.querySelector('[onclick="loginWithGitHub()"]');t&&(t.removeAttribute("onclick"),t.addEventListener("click",()=>{this.handleSocialLogin("github")})),window.loginWithGoogle=()=>this.handleSocialLogin("google"),window.loginWithGitHub=()=>this.handleSocialLogin("github")}handleSocialLogin(e){if(window.showNotification){const t=e.charAt(0).toUpperCase()+e.slice(1);window.showNotification(`${t} login coming soon!`,"info")}}setupDemoCredentials(){setTimeout(()=>{window.showNotification&&window.showNotification('Demo: Use email "<EMAIL>" and password "password"',"info",8e3)},1e3)}fillDemoCredentials(){const e=document.getElementById("email"),t=document.getElementById("password");e&&t&&(e.value="<EMAIL>",t.value="password",window.showNotification&&window.showNotification("Demo credentials filled","info",3e3))}setupKeyboardShortcuts(){document.addEventListener("keydown",e=>{(e.ctrlKey||e.metaKey)&&e.key==="Enter"&&this.loginForm&&this.loginForm.dispatchEvent(new Event("submit")),e.altKey&&e.key})}destroy(){delete window.togglePassword,delete window.loginWithGoogle,delete window.loginWithGitHub,this.isInitialized=!1}}function P(n,e={}){const t=document.querySelector(n);t&&(t.setAttribute("data-editor","tinymce"),Object.keys(e).forEach(i=>{t.setAttribute(`data-${i}`,e[i])}))}function f(n,e="info",t=5e3){window.modernThemeApp&&window.modernThemeApp.showNotification?window.modernThemeApp.showNotification(n,e,t):console.log(`[${e.toUpperCase()}] ${n}`)}function C(n,e="fadeIn"){window.demoAnimations&&window.demoAnimations.triggerAnimation&&window.demoAnimations.triggerAnimation(n,e)}function k(){document.querySelectorAll("a[data-confirm]").forEach(n=>{n.addEventListener("click",e=>{e.preventDefault();const t=n.getAttribute("data-confirm");confirm(t)&&(window.location.href=n.href)})})}function I(n){const e=document.querySelector(n);e&&e.addEventListener("submit",t=>{const i=e.querySelectorAll("[required]");let o=!0;i.forEach(s=>{s.value.trim()?s.classList.remove("border-red-500"):(s.classList.add("border-red-500"),o=!1)}),o||(t.preventDefault(),f("Prosím vyplňte všetky povinné polia","error"))})}window.templateHelpers={initTinyMCE:P,showNotification:f,triggerAnimation:C,setupConfirmDialogs:k,setupFormValidation:I};window.Alpine=g;g.start();class m{constructor(){this.isInitialized=!1,this.modules=new Map,this.currentPage=null,this.animationManager=new p,this.notificationManager=new y,this.themeManager=new v,this.formManager=new L,this.modules.set("animations",this.animationManager),this.modules.set("notifications",this.notificationManager),this.modules.set("theme",this.themeManager),this.modules.set("forms",this.formManager),this.init()}async init(){if(!this.isInitialized)try{await w("theme-initialization",async()=>{this.animationManager.init(),this.themeManager.init(),this.formManager.init(),this.initializePage(),this.initializeLegacyComponents(),this.isInitialized=!0,u("info","🎨 Modern Theme initialized successfully")})}catch{}}initializePage(){const e=window.location.pathname;e==="/"||e==="/home"?(this.currentPage=new E,this.currentPage.init()):(e==="/login"||e==="/auth/login")&&(this.currentPage=new M,this.currentPage.init())}initializeLegacyComponents(){try{this.markPanel=new S,this.demoAnimations=new T,this.tinyMCEEditor=new A,this.devTools=new b,h.debug.enabled,u("debug","Legacy components initialized")}catch{}}getModule(e){return this.modules.get(e)}getCurrentPage(){return this.currentPage}refresh(){this.modules.forEach(e=>{e.refresh&&e.refresh()}),this.currentPage&&this.currentPage.refresh&&this.currentPage.refresh()}destroy(){this.currentPage&&this.currentPage.destroy&&this.currentPage.destroy(),this.modules.forEach(e=>{e.destroy&&e.destroy()}),this.modules.clear(),this.currentPage=null,this.isInitialized=!1}}document.addEventListener("DOMContentLoaded",async()=>{try{const n=new m;window.modernThemeApp=n,window.ModernTheme=m,h.debug.enabled}catch{}});window.addEventListener("beforeunload",()=>{window.modernThemeApp&&window.modernThemeApp.destroy()});
