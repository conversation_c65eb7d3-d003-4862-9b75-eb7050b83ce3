const u={animations:{defaultDuration:.6,defaultEase:"power2.out",pageTransition:{duration:.8,ease:"power2.out"},navigation:{duration:1,delay:.2,ease:"power2.out"},scroll:{duration:1,ease:"power2.out",trigger:{start:"top 80%",end:"bottom 20%",toggleActions:"play none none reverse"}},hover:{duration:.3,ease:"power2.out"},stagger:{duration:.6,stagger:.1,ease:"power2.out"}},notifications:{defaultDuration:5e3,position:"top-right",maxVisible:5,animations:{enter:{x:400,opacity:0},enterActive:{x:0,opacity:1,duration:.5,ease:"power2.out"},exit:{x:400,opacity:0,duration:.3,ease:"power2.in"}}},theme:{defaultMode:"light",storageKey:"darkMode",transitionDuration:300},api:{baseUrl:"/api",timeout:parseInt(void 0)||1e4,retries:3},debug:{enabled:!1,logLevel:"info",showPerformance:!1},features:{pageTransitions:!0,scrollAnimations:!0,hoverEffects:!0,notifications:!0,darkMode:!0,devTools:!1},selectors:{pageTransition:".page-transition",animateOnScroll:".animate-on-scroll",parallax:".parallax",staggerChildren:".stagger-children",buttons:".btn",cards:".card-hover",navLinks:".nav-link",notifications:".notification"}};function m(r,e,t){let a;return function(...n){const i=()=>{a=null,r(...n)};clearTimeout(a),a=setTimeout(i,e)}}function f(r="id"){return`${r}-${Math.random().toString(36).substr(2,9)}`}function g(r,e,t=null){}async function p(r,e){return await e()}class d{constructor(e=u.api.baseUrl){this.baseUrl=e,this.timeout=u.api.timeout,this.retries=u.api.retries}async request(e,t={}){var c;const a=`${this.baseUrl}${e}`,n={...{headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:this.timeout},...t},i=(c=document.querySelector('meta[name="csrf-token"]'))==null?void 0:c.getAttribute("content");i&&(n.headers["X-CSRF-TOKEN"]=i),`${n.method||"GET"}${a}`;try{const o=await this.fetchWithTimeout(a,n);if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const l=await o.json();return`${a}`,l}catch(o){throw o}}async fetchWithTimeout(e,t){const a=new AbortController,s=setTimeout(()=>a.abort(),t.timeout);try{const n=await fetch(e,{...t,signal:a.signal});return clearTimeout(s),n}catch(n){throw clearTimeout(s),n.name==="AbortError"?new Error("Request timeout"):n}}async get(e,t={}){const a=new URLSearchParams(t).toString(),s=a?`${e}?${a}`:e;return this.request(s,{method:"GET"})}async post(e,t={}){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t={}){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}async upload(e,t){return this.request(e,{method:"POST",body:t,headers:{}})}}const h=new d,y={handleError(r,e){let t="An unexpected error occurred";r.message.includes("timeout")?t="Request timeout. Please try again.":r.message.includes("404")?t="Resource not found.":r.message.includes("401")?t="Unauthorized. Please log in.":r.message.includes("403")?t="Access denied.":r.message.includes("500")&&(t="Server error. Please try again later."),e&&e(t,"error")},async retry(r,e=u.api.retries,t=1e3){try{return await r()}catch(a){if(e>0)return await new Promise(s=>setTimeout(s,t)),this.retry(r,e-1,t*2);throw a}}};export{h as a,y as b,u as c,m as d,f as g,g as l,p as m};
