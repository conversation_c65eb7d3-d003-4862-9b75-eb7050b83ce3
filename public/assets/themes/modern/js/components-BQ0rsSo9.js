class s{constructor(){this.init()}init(){this.isMarkPage()&&(this.setupSidebarAutoClose(),this.setupKeyboardShortcuts(),this.setupConfirmDialogs(),console.log("🔧 Mark Panel component initialized"))}isMarkPage(){return document.querySelector(".mark-sidebar")!==null}setupSidebarAutoClose(){document.querySelectorAll(".mark-sidebar a").forEach(e=>{e.addEventListener("click",()=>{if(window.innerWidth<1024&&window.Alpine){const o=Alpine.$data(document.querySelector('[x-data*="sidebarOpen"]'));o&&(o.sidebarOpen=!1)}})})}setupKeyboardShortcuts(){document.addEventListener("keydown",e=>{e.altKey&&e.key==="d"&&(e.preventDefault(),window.location.href="/mark"),e.altKey&&e.key==="u"&&(e.preventDefault(),window.location.href="/mark/users"),e.altKey&&e.key==="s"&&(e.preventDefault(),window.location.href="/mark/settings"),e.altKey&&e.key==="c"&&(e.preventDefault(),window.location.href="/mark/pages")}),console.log("⌨️ Mark keyboard shortcuts: Alt+D (Dashboard), Alt+U (Users), Alt+S (Settings), Alt+C (Content)")}setupConfirmDialogs(){document.querySelectorAll('a[onclick*="confirm"]').forEach(e=>{e.addEventListener("click",o=>{var n;o.preventDefault();const t=((n=e.getAttribute("onclick").match(/confirm\('([^']+)'\)/))==null?void 0:n[1])||"Naozaj chcete pokračovať?";this.showConfirmDialog(t)&&(window.location.href=e.href)}),e.removeAttribute("onclick")})}showConfirmDialog(e){return confirm(e)}showNotification(e,o="info"){window.modernThemeApp&&window.modernThemeApp.showNotification&&window.modernThemeApp.showNotification(e,o)}}class a{constructor(){this.init()}init(){this.setupWelcomeNotification(),this.setupTechBadgeAnimations(),this.setupInteractiveElements(),console.log("🎭 Demo Animations component initialized")}setupWelcomeNotification(){this.isHomePage()&&setTimeout(()=>{window.modernThemeApp&&window.modernThemeApp.showNotification&&window.modernThemeApp.showNotification("Vitajte v Modern Theme! 🎉","success",3e3)},1e3)}setupTechBadgeAnimations(){typeof gsap<"u"&&document.querySelector(".tech-badge")&&gsap.from(".tech-badge",{duration:.8,y:20,opacity:0,stagger:.1,delay:1,ease:"power2.out"})}setupInteractiveElements(){document.querySelectorAll(".card-hover").forEach(e=>{e.addEventListener("mouseenter",()=>{typeof gsap<"u"&&gsap.to(e,{duration:.3,scale:1.02,ease:"power2.out"})}),e.addEventListener("mouseleave",()=>{typeof gsap<"u"&&gsap.to(e,{duration:.3,scale:1,ease:"power2.out"})})}),document.querySelectorAll(".btn").forEach(e=>{e.addEventListener("click",o=>{typeof gsap<"u"&&gsap.fromTo(e,{scale:1},{scale:.95,duration:.1,ease:"power2.out",yoyo:!0,repeat:1})})})}isHomePage(){return window.location.pathname==="/"||document.querySelector(".hero-section")!==null}triggerAnimation(e,o="fadeIn"){if(typeof gsap>"u")return;const t=document.querySelectorAll(e);if(t.length!==0)switch(o){case"fadeIn":gsap.from(t,{duration:.6,opacity:0,y:20,stagger:.1,ease:"power2.out"});break;case"slideIn":gsap.from(t,{duration:.8,x:-50,opacity:0,stagger:.1,ease:"power2.out"});break;case"scaleIn":gsap.from(t,{duration:.6,scale:.8,opacity:0,stagger:.1,ease:"back.out(1.7)"});break}}}class l{constructor(){this.defaultConfig={height:500,menubar:!0,plugins:["advlist autolink lists link image charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime media table paste code help wordcount"],toolbar:"undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help",content_style:"body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",skin:"oxide",content_css:"default"},this.init()}init(){this.hasTinyMCEElements()&&this.loadTinyMCE().then(()=>{this.initializeEditors(),console.log("📝 TinyMCE Editor component initialized")})}hasTinyMCEElements(){return document.querySelector('textarea[data-editor="tinymce"]')!==null||document.querySelector("#content")!==null}async loadTinyMCE(){return typeof tinymce<"u"?Promise.resolve():new Promise((e,o)=>{const t=document.createElement("script");t.src="https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js",t.referrerPolicy="origin",t.onload=()=>e(),t.onerror=()=>o(new Error("Failed to load TinyMCE")),document.head.appendChild(t)})}initializeEditors(){document.querySelector("#content")&&this.initEditor("#content",this.defaultConfig),document.querySelectorAll('textarea[data-editor="tinymce"]').forEach(e=>{const o=this.getCustomConfig(e);this.initEditor(`#${e.id}`,o)})}initEditor(e,o={}){const t={selector:e,...this.defaultConfig,...o,setup:n=>{n.on("init",()=>{console.log(`📝 TinyMCE editor initialized for ${e}`)}),n.on("change",()=>{this.handleEditorChange(n)}),o.setup&&o.setup(n)}};tinymce.init(t)}getCustomConfig(e){const o={};return e.dataset.height&&(o.height=parseInt(e.dataset.height)),e.dataset.plugins&&(o.plugins=e.dataset.plugins.split(",")),e.dataset.toolbar&&(o.toolbar=e.dataset.toolbar),e.dataset.simple==="true"&&(o.plugins=["lists","link","paste"],o.toolbar="bold italic | bullist numlist | link",o.menubar=!1),o}handleEditorChange(e){const o=new CustomEvent("tinymce-change",{detail:{editor:e,content:e.getContent()}});document.dispatchEvent(o)}getEditorContent(e){const o=tinymce.get(e.replace("#",""));return o?o.getContent():null}setEditorContent(e,o){const t=tinymce.get(e.replace("#",""));t&&t.setContent(o)}destroyEditor(e){const o=tinymce.get(e.replace("#",""));o&&o.destroy()}}class d{constructor(){this.isDevelopment=this.checkDevelopmentMode(),this.init()}init(){this.isDevelopment&&(this.logEnvironmentInfo(),this.setupDevConsole(),this.setupPerformanceMonitoring(),this.setupErrorHandling(),console.log("🛠️ Development Tools component initialized"))}checkDevelopmentMode(){return document.body.dataset.env==="development"||window.location.hostname==="localhost"||window.location.hostname.includes("dev")||document.querySelector('script[data-dev="true"]')!==null}logEnvironmentInfo(){console.group("🎨 Modern Theme - Development Mode"),typeof gsap<"u"&&console.log("GSAP version:",gsap.version),typeof Alpine<"u"&&Alpine.version&&console.log("Alpine.js version:",Alpine.version),typeof tinymce<"u"&&console.log("TinyMCE version:",tinymce.majorVersion+"."+tinymce.minorVersion),console.log("Theme:","Modern Theme"),console.log("Build time:",new Date().toISOString()),console.log("User Agent:",navigator.userAgent),console.groupEnd()}setupDevConsole(){window.devTools={theme:{showNotification:(e,o="info")=>{window.modernThemeApp&&window.modernThemeApp.showNotification(e,o)},triggerAnimation:(e,o="fadeIn")=>{window.demoAnimations&&window.demoAnimations.triggerAnimation(e,o)},reloadCSS:()=>{document.querySelectorAll('link[rel="stylesheet"]').forEach(o=>{const t=o.href;o.href=t+(t.includes("?")?"&":"?")+"reload="+Date.now()}),console.log("🎨 CSS reloaded")}},performance:{measure:(e,o)=>{const t=performance.now(),n=o(),r=performance.now();return console.log(`⏱️ ${e}: ${(r-t).toFixed(2)}ms`),n},logMemory:()=>{performance.memory&&console.table({"Used JS Heap Size":(performance.memory.usedJSHeapSize/1048576).toFixed(2)+" MB","Total JS Heap Size":(performance.memory.totalJSHeapSize/1048576).toFixed(2)+" MB","JS Heap Size Limit":(performance.memory.jsHeapSizeLimit/1048576).toFixed(2)+" MB"})}},debug:{logElements:e=>{const o=document.querySelectorAll(e);return console.log(`Found ${o.length} elements for "${e}":`,o),o},highlightElement:e=>{document.querySelectorAll(e).forEach(t=>{t.style.outline="2px solid red",setTimeout(()=>{t.style.outline=""},3e3)})},logEventListeners:e=>{console.log("Event listeners for element:",e)}}},console.log("🛠️ Development tools available in window.devTools")}setupPerformanceMonitoring(){window.addEventListener("load",()=>{setTimeout(()=>{const e=performance.getEntriesByType("navigation")[0];console.group("📊 Performance Metrics"),console.log("DOM Content Loaded:",e.domContentLoadedEventEnd-e.domContentLoadedEventStart+"ms"),console.log("Load Complete:",e.loadEventEnd-e.loadEventStart+"ms"),console.log("Total Load Time:",e.loadEventEnd-e.fetchStart+"ms"),console.groupEnd()},1e3)}),typeof gsap<"u"&&gsap.ticker.add(()=>{})}setupErrorHandling(){window.addEventListener("error",e=>{console.group("❌ JavaScript Error"),console.error("Message:",e.message),console.error("File:",e.filename),console.error("Line:",e.lineno),console.error("Column:",e.colno),console.error("Error object:",e.error),console.groupEnd()}),window.addEventListener("unhandledrejection",e=>{console.group("❌ Unhandled Promise Rejection"),console.error("Reason:",e.reason),console.groupEnd()})}addTool(e,o){this.isDevelopment&&window.devTools&&(window.devTools[e]=o,console.log(`🛠️ Added dev tool: ${e}`))}}export{a as D,s as M,l as T,d as a};
