var Ie=!1,$e=!1,N=[],Pe=-1;function qn(e){Wn(e)}function Wn(e){N.includes(e)||N.push(e),Jn()}function Un(e){let t=N.indexOf(e);t!==-1&&t>Pe&&N.splice(t,1)}function Jn(){!$e&&!Ie&&(Ie=!0,queueMicrotask(Vn))}function Vn(){Ie=!1,$e=!0;for(let e=0;e<N.length;e++)N[e](),Pe=e;N.length=0,Pe=-1,$e=!1}var q,B,W,Tt,Re=!0;function Yn(e){Re=!1,e(),Re=!0}function Gn(e){q=e.reactive,W=e.release,B=t=>e.effect(t,{scheduler:n=>{Re?qn(n):n()}}),Tt=e.raw}function ht(e){B=e}function Xn(e){let t=()=>{};return[r=>{let i=B(r);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(o=>o())}),e._x_effects.add(i),t=()=>{i!==void 0&&(e._x_effects.delete(i),W(i))},i},()=>{t()}]}function It(e,t){let n=!0,r,i=B(()=>{let o=e();JSON.stringify(o),n?r=o:queueMicrotask(()=>{t(o,r),r=o}),n=!1});return()=>W(i)}var $t=[],Pt=[],Rt=[];function Zn(e){Rt.push(e)}function Je(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,Pt.push(t))}function jt(e){$t.push(e)}function Nt(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function Lt(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([n,r])=>{(t===void 0||t.includes(n))&&(r.forEach(i=>i()),delete e._x_attributeCleanups[n])})}function Qn(e){var t,n;for((t=e._x_effects)==null||t.forEach(Un);(n=e._x_cleanups)!=null&&n.length;)e._x_cleanups.pop()()}var Ve=new MutationObserver(Ze),Ye=!1;function Ge(){Ve.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Ye=!0}function Ft(){er(),Ve.disconnect(),Ye=!1}var G=[];function er(){let e=Ve.takeRecords();G.push(()=>e.length>0&&Ze(e));let t=G.length;queueMicrotask(()=>{if(G.length===t)for(;G.length>0;)G.shift()()})}function g(e){if(!Ye)return e();Ft();let t=e();return Ge(),t}var Xe=!1,_e=[];function tr(){Xe=!0}function nr(){Xe=!1,Ze(_e),_e=[]}function Ze(e){if(Xe){_e=_e.concat(e);return}let t=[],n=new Set,r=new Map,i=new Map;for(let o=0;o<e.length;o++)if(!e[o].target._x_ignoreMutationObserver&&(e[o].type==="childList"&&(e[o].removedNodes.forEach(s=>{s.nodeType===1&&s._x_marker&&n.add(s)}),e[o].addedNodes.forEach(s=>{if(s.nodeType===1){if(n.has(s)){n.delete(s);return}s._x_marker||t.push(s)}})),e[o].type==="attributes")){let s=e[o].target,a=e[o].attributeName,u=e[o].oldValue,c=()=>{r.has(s)||r.set(s,[]),r.get(s).push({name:a,value:s.getAttribute(a)})},l=()=>{i.has(s)||i.set(s,[]),i.get(s).push(a)};s.hasAttribute(a)&&u===null?c():s.hasAttribute(a)?(l(),c()):l()}i.forEach((o,s)=>{Lt(s,o)}),r.forEach((o,s)=>{$t.forEach(a=>a(s,o))});for(let o of n)t.some(s=>s.contains(o))||Pt.forEach(s=>s(o));for(let o of t)o.isConnected&&Rt.forEach(s=>s(o));t=null,n=null,r=null,i=null}function kt(e){return ie(z(e))}function re(e,t,n){return e._x_dataStack=[t,...z(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(r=>r!==t)}}function z(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?z(e.host):e.parentNode?z(e.parentNode):[]}function ie(e){return new Proxy({objects:e},rr)}var rr={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(n=>Object.prototype.hasOwnProperty.call(n,t)||Reflect.has(n,t))},get({objects:e},t,n){return t=="toJSON"?ir:Reflect.get(e.find(r=>Reflect.has(r,t))||{},t,n)},set({objects:e},t,n,r){const i=e.find(s=>Object.prototype.hasOwnProperty.call(s,t))||e[e.length-1],o=Object.getOwnPropertyDescriptor(i,t);return o!=null&&o.set&&(o!=null&&o.get)?o.set.call(r,n)||!0:Reflect.set(i,t,n)}};function ir(){return Reflect.ownKeys(this).reduce((t,n)=>(t[n]=Reflect.get(this,n),t),{})}function Dt(e){let t=r=>typeof r=="object"&&!Array.isArray(r)&&r!==null,n=(r,i="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([o,{value:s,enumerable:a}])=>{if(a===!1||s===void 0||typeof s=="object"&&s!==null&&s.__v_skip)return;let u=i===""?o:`${i}.${o}`;typeof s=="object"&&s!==null&&s._x_interceptor?r[o]=s.initialize(e,u,o):t(s)&&s!==r&&!(s instanceof Element)&&n(s,u)})};return n(e)}function Bt(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:!0,initialize(r,i,o){return e(this.initialValue,()=>or(r,i),s=>je(r,i,s),i,o)}};return t(n),r=>{if(typeof r=="object"&&r!==null&&r._x_interceptor){let i=n.initialize.bind(n);n.initialize=(o,s,a)=>{let u=r.initialize(o,s,a);return n.initialValue=u,i(o,s,a)}}else n.initialValue=r;return n}}function or(e,t){return t.split(".").reduce((n,r)=>n[r],e)}function je(e,t,n){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=n;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),je(e[t[0]],t.slice(1),n)}}var Kt={};function A(e,t){Kt[e]=t}function Ne(e,t){let n=sr(t);return Object.entries(Kt).forEach(([r,i])=>{Object.defineProperty(e,`$${r}`,{get(){return i(t,n)},enumerable:!1})}),e}function sr(e){let[t,n]=Jt(e),r={interceptor:Bt,...t};return Je(e,n),r}function ar(e,t,n,...r){try{return n(...r)}catch(i){ne(i,e,t)}}function ne(e,t,n=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

${n?'Expression: "'+n+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var de=!0;function zt(e){let t=de;de=!1;let n=e();return de=t,n}function L(e,t,n={}){let r;return m(e,t)(i=>r=i,n),r}function m(...e){return Ht(...e)}var Ht=qt;function ur(e){Ht=e}function qt(e,t){let n={};Ne(n,e);let r=[n,...z(e)],i=typeof t=="function"?cr(r,t):fr(r,t,e);return ar.bind(null,e,t,i)}function cr(e,t){return(n=()=>{},{scope:r={},params:i=[]}={})=>{let o=t.apply(ie([r,...e]),i);he(n,o)}}var Ae={};function lr(e,t){if(Ae[e])return Ae[e];let n=Object.getPrototypeOf(async function(){}).constructor,r=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,o=(()=>{try{let s=new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`);return Object.defineProperty(s,"name",{value:`[Alpine] ${e}`}),s}catch(s){return ne(s,t,e),Promise.resolve()}})();return Ae[e]=o,o}function fr(e,t,n){let r=lr(t,n);return(i=()=>{},{scope:o={},params:s=[]}={})=>{r.result=void 0,r.finished=!1;let a=ie([o,...e]);if(typeof r=="function"){let u=r(r,a).catch(c=>ne(c,n,t));r.finished?(he(i,r.result,a,s,n),r.result=void 0):u.then(c=>{he(i,c,a,s,n)}).catch(c=>ne(c,n,t)).finally(()=>r.result=void 0)}}}function he(e,t,n,r,i){if(de&&typeof t=="function"){let o=t.apply(n,r);o instanceof Promise?o.then(s=>he(e,s,n,r)).catch(s=>ne(s,i,t)):e(o)}else typeof t=="object"&&t instanceof Promise?t.then(o=>e(o)):e(t)}var Qe="x-";function U(e=""){return Qe+e}function dr(e){Qe=e}var ge={};function y(e,t){return ge[e]=t,{before(n){if(!ge[n]){console.warn(String.raw`Cannot find directive \`${n}\`. \`${e}\` will use the default order of execution`);return}const r=j.indexOf(n);j.splice(r>=0?r:j.indexOf("DEFAULT"),0,e)}}}function pr(e){return Object.keys(ge).includes(e)}function et(e,t,n){if(t=Array.from(t),e._x_virtualDirectives){let o=Object.entries(e._x_virtualDirectives).map(([a,u])=>({name:a,value:u})),s=Wt(o);o=o.map(a=>s.find(u=>u.name===a.name)?{name:`x-bind:${a.name}`,value:`"${a.value}"`}:a),t=t.concat(o)}let r={};return t.map(Gt((o,s)=>r[o]=s)).filter(Zt).map(gr(r,n)).sort(xr).map(o=>hr(e,o))}function Wt(e){return Array.from(e).map(Gt()).filter(t=>!Zt(t))}var Le=!1,Q=new Map,Ut=Symbol();function _r(e){Le=!0;let t=Symbol();Ut=t,Q.set(t,[]);let n=()=>{for(;Q.get(t).length;)Q.get(t).shift()();Q.delete(t)},r=()=>{Le=!1,n()};e(n),r()}function Jt(e){let t=[],n=a=>t.push(a),[r,i]=Xn(e);return t.push(i),[{Alpine:oe,effect:r,cleanup:n,evaluateLater:m.bind(m,e),evaluate:L.bind(L,e)},()=>t.forEach(a=>a())]}function hr(e,t){let n=()=>{},r=ge[t.type]||n,[i,o]=Jt(e);Nt(e,t.original,o);let s=()=>{e._x_ignore||e._x_ignoreSelf||(r.inline&&r.inline(e,t,i),r=r.bind(r,e,t,i),Le?Q.get(Ut).push(r):r())};return s.runCleanups=o,s}var Vt=(e,t)=>({name:n,value:r})=>(n.startsWith(e)&&(n=n.replace(e,t)),{name:n,value:r}),Yt=e=>e;function Gt(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:i}=Xt.reduce((o,s)=>s(o),{name:t,value:n});return r!==t&&e(r,t),{name:r,value:i}}}var Xt=[];function tt(e){Xt.push(e)}function Zt({name:e}){return Qt().test(e)}var Qt=()=>new RegExp(`^${Qe}([^:^.]+)\\b`);function gr(e,t){return({name:n,value:r})=>{let i=n.match(Qt()),o=n.match(/:([a-zA-Z0-9\-_:]+)/),s=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],a=t||e[n]||n;return{type:i?i[1]:null,value:o?o[1]:null,modifiers:s.map(u=>u.replace(".","")),expression:r,original:a}}}var Fe="DEFAULT",j=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",Fe,"teleport"];function xr(e,t){let n=j.indexOf(e.type)===-1?Fe:e.type,r=j.indexOf(t.type)===-1?Fe:t.type;return j.indexOf(n)-j.indexOf(r)}function ee(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}function D(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(i=>D(i,t));return}let n=!1;if(t(e,()=>n=!0),n)return;let r=e.firstElementChild;for(;r;)D(r,t),r=r.nextElementSibling}function E(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var gt=!1;function yr(){gt&&E("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),gt=!0,document.body||E("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),ee(document,"alpine:init"),ee(document,"alpine:initializing"),Ge(),Zn(t=>C(t,D)),Je(t=>V(t)),jt((t,n)=>{et(t,n).forEach(r=>r())});let e=t=>!ye(t.parentElement,!0);Array.from(document.querySelectorAll(nn().join(","))).filter(e).forEach(t=>{C(t)}),ee(document,"alpine:initialized"),setTimeout(()=>{wr()})}var nt=[],en=[];function tn(){return nt.map(e=>e())}function nn(){return nt.concat(en).map(e=>e())}function rn(e){nt.push(e)}function on(e){en.push(e)}function ye(e,t=!1){return J(e,n=>{if((t?nn():tn()).some(i=>n.matches(i)))return!0})}function J(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return J(e.parentElement,t)}}function vr(e){return tn().some(t=>e.matches(t))}var sn=[];function br(e){sn.push(e)}var mr=1;function C(e,t=D,n=()=>{}){J(e,r=>r._x_ignore)||_r(()=>{t(e,(r,i)=>{r._x_marker||(n(r,i),sn.forEach(o=>o(r,i)),et(r,r.attributes).forEach(o=>o()),r._x_ignore||(r._x_marker=mr++),r._x_ignore&&i())})})}function V(e,t=D){t(e,n=>{Qn(n),Lt(n),delete n._x_marker})}function wr(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,n,r])=>{pr(n)||r.some(i=>{if(document.querySelector(i))return E(`found "${i}", but missing ${t} plugin`),!0})})}var ke=[],rt=!1;function it(e=()=>{}){return queueMicrotask(()=>{rt||setTimeout(()=>{De()})}),new Promise(t=>{ke.push(()=>{e(),t()})})}function De(){for(rt=!1;ke.length;)ke.shift()()}function Er(){rt=!0}function ot(e,t){return Array.isArray(t)?xt(e,t.join(" ")):typeof t=="object"&&t!==null?Sr(e,t):typeof t=="function"?ot(e,t()):xt(e,t)}function xt(e,t){let n=i=>i.split(" ").filter(o=>!e.classList.contains(o)).filter(Boolean),r=i=>(e.classList.add(...i),()=>{e.classList.remove(...i)});return t=t===!0?t="":t||"",r(n(t))}function Sr(e,t){let n=a=>a.split(" ").filter(Boolean),r=Object.entries(t).flatMap(([a,u])=>u?n(a):!1).filter(Boolean),i=Object.entries(t).flatMap(([a,u])=>u?!1:n(a)).filter(Boolean),o=[],s=[];return i.forEach(a=>{e.classList.contains(a)&&(e.classList.remove(a),s.push(a))}),r.forEach(a=>{e.classList.contains(a)||(e.classList.add(a),o.push(a))}),()=>{s.forEach(a=>e.classList.add(a)),o.forEach(a=>e.classList.remove(a))}}function ve(e,t){return typeof t=="object"&&t!==null?Ar(e,t):Or(e,t)}function Ar(e,t){let n={};return Object.entries(t).forEach(([r,i])=>{n[r]=e.style[r],r.startsWith("--")||(r=Cr(r)),e.style.setProperty(r,i)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{ve(e,n)}}function Or(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function Cr(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function Be(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}y("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:i})=>{typeof r=="function"&&(r=i(r)),r!==!1&&(!r||typeof r=="boolean"?Tr(e,n,t):Mr(e,r,t))});function Mr(e,t,n){an(e,ot,""),{enter:i=>{e._x_transition.enter.during=i},"enter-start":i=>{e._x_transition.enter.start=i},"enter-end":i=>{e._x_transition.enter.end=i},leave:i=>{e._x_transition.leave.during=i},"leave-start":i=>{e._x_transition.leave.start=i},"leave-end":i=>{e._x_transition.leave.end=i}}[n](t)}function Tr(e,t,n){an(e,ve);let r=!t.includes("in")&&!t.includes("out")&&!n,i=r||t.includes("in")||["enter"].includes(n),o=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter((h,x)=>x<t.indexOf("out"))),t.includes("out")&&!r&&(t=t.filter((h,x)=>x>t.indexOf("out")));let s=!t.includes("opacity")&&!t.includes("scale"),a=s||t.includes("opacity"),u=s||t.includes("scale"),c=a?0:1,l=u?X(t,"scale",95)/100:1,d=X(t,"delay",0)/1e3,p=X(t,"origin","center"),v="opacity, transform",M=X(t,"duration",150)/1e3,se=X(t,"duration",75)/1e3,f="cubic-bezier(0.4, 0.0, 0.2, 1)";i&&(e._x_transition.enter.during={transformOrigin:p,transitionDelay:`${d}s`,transitionProperty:v,transitionDuration:`${M}s`,transitionTimingFunction:f},e._x_transition.enter.start={opacity:c,transform:`scale(${l})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),o&&(e._x_transition.leave.during={transformOrigin:p,transitionDelay:`${d}s`,transitionProperty:v,transitionDuration:`${se}s`,transitionTimingFunction:f},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:c,transform:`scale(${l})`})}function an(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(r=()=>{},i=()=>{}){Ke(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},r,i)},out(r=()=>{},i=()=>{}){Ke(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},r,i)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){const i=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let o=()=>i(n);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):o():e._x_transition?e._x_transition.in(n):o();return}e._x_hidePromise=e._x_transition?new Promise((s,a)=>{e._x_transition.out(()=>{},()=>s(r)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>a({isFromCancelledTransition:!0}))}):Promise.resolve(r),queueMicrotask(()=>{let s=un(e);s?(s._x_hideChildren||(s._x_hideChildren=[]),s._x_hideChildren.push(e)):i(()=>{let a=u=>{let c=Promise.all([u._x_hidePromise,...(u._x_hideChildren||[]).map(a)]).then(([l])=>l==null?void 0:l());return delete u._x_hidePromise,delete u._x_hideChildren,c};a(e).catch(u=>{if(!u.isFromCancelledTransition)throw u})})})};function un(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:un(t)}function Ke(e,t,{during:n,start:r,end:i}={},o=()=>{},s=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(i).length===0){o(),s();return}let a,u,c;Ir(e,{start(){a=t(e,r)},during(){u=t(e,n)},before:o,end(){a(),c=t(e,i)},after:s,cleanup(){u(),c()}})}function Ir(e,t){let n,r,i,o=Be(()=>{g(()=>{n=!0,r||t.before(),i||(t.end(),De()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(s){this.beforeCancels.push(s)},cancel:Be(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();o()}),finish:o},g(()=>{t.start(),t.during()}),Er(),requestAnimationFrame(()=>{if(n)return;let s=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,a=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;s===0&&(s=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),g(()=>{t.before()}),r=!0,requestAnimationFrame(()=>{n||(g(()=>{t.end()}),De(),setTimeout(e._x_transitioning.finish,s+a),i=!0)})})}function X(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r||t==="scale"&&isNaN(r))return n;if(t==="duration"||t==="delay"){let i=r.match(/([0-9]+)ms/);if(i)return i[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var I=!1;function P(e,t=()=>{}){return(...n)=>I?t(...n):e(...n)}function $r(e){return(...t)=>I&&e(...t)}var cn=[];function be(e){cn.push(e)}function Pr(e,t){cn.forEach(n=>n(e,t)),I=!0,ln(()=>{C(t,(n,r)=>{r(n,()=>{})})}),I=!1}var ze=!1;function Rr(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),I=!0,ze=!0,ln(()=>{jr(t)}),I=!1,ze=!1}function jr(e){let t=!1;C(e,(r,i)=>{D(r,(o,s)=>{if(t&&vr(o))return s();t=!0,i(o,s)})})}function ln(e){let t=B;ht((n,r)=>{let i=t(n);return W(i),()=>{}}),e(),ht(t)}function fn(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=q({})),e._x_bindings[t]=n,t=r.includes("camel")?zr(t):t,t){case"value":Nr(e,n);break;case"style":Fr(e,n);break;case"class":Lr(e,n);break;case"selected":case"checked":kr(e,t,n);break;default:dn(e,t,n);break}}function Nr(e,t){if(hn(e))e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=pe(e.value)===t:e.checked=yt(e.value,t));else if(st(e))Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(n=>yt(n,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")Kr(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function Lr(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=ot(e,t)}function Fr(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=ve(e,t)}function kr(e,t,n){dn(e,t,n),Br(e,t,n)}function dn(e,t,n){[null,void 0,!1].includes(n)&&qr(t)?e.removeAttribute(t):(pn(t)&&(n=t),Dr(e,t,n))}function Dr(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function Br(e,t,n){e[t]!==n&&(e[t]=n)}function Kr(e,t){const n=[].concat(t).map(r=>r+"");Array.from(e.options).forEach(r=>{r.selected=n.includes(r.value)})}function zr(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function yt(e,t){return e==t}function pe(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}var Hr=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function pn(e){return Hr.has(e)}function qr(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function Wr(e,t,n){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:_n(e,t,n)}function Ur(e,t,n,r=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let i=e._x_inlineBindings[t];return i.extract=r,zt(()=>L(e,i.expression))}return _n(e,t,n)}function _n(e,t,n){let r=e.getAttribute(t);return r===null?typeof n=="function"?n():n:r===""?!0:pn(t)?!![t,"true"].includes(r):r}function st(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function hn(e){return e.type==="radio"||e.localName==="ui-radio"}function gn(e,t){var n;return function(){var r=this,i=arguments,o=function(){n=null,e.apply(r,i)};clearTimeout(n),n=setTimeout(o,t)}}function xn(e,t){let n;return function(){let r=this,i=arguments;n||(e.apply(r,i),n=!0,setTimeout(()=>n=!1,t))}}function yn({get:e,set:t},{get:n,set:r}){let i=!0,o,s=B(()=>{let a=e(),u=n();if(i)r(Oe(a)),i=!1;else{let c=JSON.stringify(a),l=JSON.stringify(u);c!==o?r(Oe(a)):c!==l&&t(Oe(u))}o=JSON.stringify(e()),JSON.stringify(n())});return()=>{W(s)}}function Oe(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function Jr(e){(Array.isArray(e)?e:[e]).forEach(n=>n(oe))}var R={},vt=!1;function Vr(e,t){if(vt||(R=q(R),vt=!0),t===void 0)return R[e];R[e]=t,Dt(R[e]),typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&R[e].init()}function Yr(){return R}var vn={};function Gr(e,t){let n=typeof t!="function"?()=>t:t;return e instanceof Element?bn(e,n()):(vn[e]=n,()=>{})}function Xr(e){return Object.entries(vn).forEach(([t,n])=>{Object.defineProperty(e,t,{get(){return(...r)=>n(...r)}})}),e}function bn(e,t,n){let r=[];for(;r.length;)r.pop()();let i=Object.entries(t).map(([s,a])=>({name:s,value:a})),o=Wt(i);return i=i.map(s=>o.find(a=>a.name===s.name)?{name:`x-bind:${s.name}`,value:`"${s.value}"`}:s),et(e,i,n).map(s=>{r.push(s.runCleanups),s()}),()=>{for(;r.length;)r.pop()()}}var mn={};function Zr(e,t){mn[e]=t}function Qr(e,t){return Object.entries(mn).forEach(([n,r])=>{Object.defineProperty(e,n,{get(){return(...i)=>r.bind(t)(...i)},enumerable:!1})}),e}var ei={get reactive(){return q},get release(){return W},get effect(){return B},get raw(){return Tt},version:"3.14.9",flushAndStopDeferringMutations:nr,dontAutoEvaluateFunctions:zt,disableEffectScheduling:Yn,startObservingMutations:Ge,stopObservingMutations:Ft,setReactivityEngine:Gn,onAttributeRemoved:Nt,onAttributesAdded:jt,closestDataStack:z,skipDuringClone:P,onlyDuringClone:$r,addRootSelector:rn,addInitSelector:on,interceptClone:be,addScopeToNode:re,deferMutations:tr,mapAttributes:tt,evaluateLater:m,interceptInit:br,setEvaluator:ur,mergeProxies:ie,extractProp:Ur,findClosest:J,onElRemoved:Je,closestRoot:ye,destroyTree:V,interceptor:Bt,transition:Ke,setStyles:ve,mutateDom:g,directive:y,entangle:yn,throttle:xn,debounce:gn,evaluate:L,initTree:C,nextTick:it,prefixed:U,prefix:dr,plugin:Jr,magic:A,store:Vr,start:yr,clone:Rr,cloneNode:Pr,bound:Wr,$data:kt,watch:It,walk:D,data:Zr,bind:Gr},oe=ei;function ti(e,t){const n=Object.create(null),r=e.split(",");for(let i=0;i<r.length;i++)n[r[i]]=!0;return i=>!!n[i]}var ni=Object.freeze({}),ri=Object.prototype.hasOwnProperty,me=(e,t)=>ri.call(e,t),F=Array.isArray,te=e=>wn(e)==="[object Map]",ii=e=>typeof e=="string",at=e=>typeof e=="symbol",we=e=>e!==null&&typeof e=="object",oi=Object.prototype.toString,wn=e=>oi.call(e),En=e=>wn(e).slice(8,-1),ut=e=>ii(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,si=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ai=si(e=>e.charAt(0).toUpperCase()+e.slice(1)),Sn=(e,t)=>e!==t&&(e===e||t===t),He=new WeakMap,Z=[],O,k=Symbol("iterate"),qe=Symbol("Map key iterate");function ui(e){return e&&e._isEffect===!0}function ci(e,t=ni){ui(e)&&(e=e.raw);const n=di(e,t);return t.lazy||n(),n}function li(e){e.active&&(An(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var fi=0;function di(e,t){const n=function(){if(!n.active)return e();if(!Z.includes(n)){An(n);try{return _i(),Z.push(n),O=n,e()}finally{Z.pop(),On(),O=Z[Z.length-1]}}};return n.id=fi++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}function An(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var H=!0,ct=[];function pi(){ct.push(H),H=!1}function _i(){ct.push(H),H=!0}function On(){const e=ct.pop();H=e===void 0?!0:e}function S(e,t,n){if(!H||O===void 0)return;let r=He.get(e);r||He.set(e,r=new Map);let i=r.get(n);i||r.set(n,i=new Set),i.has(O)||(i.add(O),O.deps.push(i),O.options.onTrack&&O.options.onTrack({effect:O,target:e,type:t,key:n}))}function $(e,t,n,r,i,o){const s=He.get(e);if(!s)return;const a=new Set,u=l=>{l&&l.forEach(d=>{(d!==O||d.allowRecurse)&&a.add(d)})};if(t==="clear")s.forEach(u);else if(n==="length"&&F(e))s.forEach((l,d)=>{(d==="length"||d>=r)&&u(l)});else switch(n!==void 0&&u(s.get(n)),t){case"add":F(e)?ut(n)&&u(s.get("length")):(u(s.get(k)),te(e)&&u(s.get(qe)));break;case"delete":F(e)||(u(s.get(k)),te(e)&&u(s.get(qe)));break;case"set":te(e)&&u(s.get(k));break}const c=l=>{l.options.onTrigger&&l.options.onTrigger({effect:l,target:e,key:n,type:t,newValue:r,oldValue:i,oldTarget:o}),l.options.scheduler?l.options.scheduler(l):l()};a.forEach(c)}var hi=ti("__proto__,__v_isRef,__isVue"),Cn=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(at)),gi=Mn(),xi=Mn(!0),bt=yi();function yi(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=_(this);for(let o=0,s=this.length;o<s;o++)S(r,"get",o+"");const i=r[t](...n);return i===-1||i===!1?r[t](...n.map(_)):i}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){pi();const r=_(this)[t].apply(this,n);return On(),r}}),e}function Mn(e=!1,t=!1){return function(r,i,o){if(i==="__v_isReactive")return!e;if(i==="__v_isReadonly")return e;if(i==="__v_raw"&&o===(e?t?Pi:Pn:t?$i:$n).get(r))return r;const s=F(r);if(!e&&s&&me(bt,i))return Reflect.get(bt,i,o);const a=Reflect.get(r,i,o);return(at(i)?Cn.has(i):hi(i))||(e||S(r,"get",i),t)?a:We(a)?!s||!ut(i)?a.value:a:we(a)?e?Rn(a):pt(a):a}}var vi=bi();function bi(e=!1){return function(n,r,i,o){let s=n[r];if(!e&&(i=_(i),s=_(s),!F(n)&&We(s)&&!We(i)))return s.value=i,!0;const a=F(n)&&ut(r)?Number(r)<n.length:me(n,r),u=Reflect.set(n,r,i,o);return n===_(o)&&(a?Sn(i,s)&&$(n,"set",r,i,s):$(n,"add",r,i)),u}}function mi(e,t){const n=me(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&$(e,"delete",t,void 0,r),i}function wi(e,t){const n=Reflect.has(e,t);return(!at(t)||!Cn.has(t))&&S(e,"has",t),n}function Ei(e){return S(e,"iterate",F(e)?"length":k),Reflect.ownKeys(e)}var Si={get:gi,set:vi,deleteProperty:mi,has:wi,ownKeys:Ei},Ai={get:xi,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},lt=e=>we(e)?pt(e):e,ft=e=>we(e)?Rn(e):e,dt=e=>e,Ee=e=>Reflect.getPrototypeOf(e);function ae(e,t,n=!1,r=!1){e=e.__v_raw;const i=_(e),o=_(t);t!==o&&!n&&S(i,"get",t),!n&&S(i,"get",o);const{has:s}=Ee(i),a=r?dt:n?ft:lt;if(s.call(i,t))return a(e.get(t));if(s.call(i,o))return a(e.get(o));e!==i&&e.get(t)}function ue(e,t=!1){const n=this.__v_raw,r=_(n),i=_(e);return e!==i&&!t&&S(r,"has",e),!t&&S(r,"has",i),e===i?n.has(e):n.has(e)||n.has(i)}function ce(e,t=!1){return e=e.__v_raw,!t&&S(_(e),"iterate",k),Reflect.get(e,"size",e)}function mt(e){e=_(e);const t=_(this);return Ee(t).has.call(t,e)||(t.add(e),$(t,"add",e,e)),this}function wt(e,t){t=_(t);const n=_(this),{has:r,get:i}=Ee(n);let o=r.call(n,e);o?In(n,r,e):(e=_(e),o=r.call(n,e));const s=i.call(n,e);return n.set(e,t),o?Sn(t,s)&&$(n,"set",e,t,s):$(n,"add",e,t),this}function Et(e){const t=_(this),{has:n,get:r}=Ee(t);let i=n.call(t,e);i?In(t,n,e):(e=_(e),i=n.call(t,e));const o=r?r.call(t,e):void 0,s=t.delete(e);return i&&$(t,"delete",e,void 0,o),s}function St(){const e=_(this),t=e.size!==0,n=te(e)?new Map(e):new Set(e),r=e.clear();return t&&$(e,"clear",void 0,void 0,n),r}function le(e,t){return function(r,i){const o=this,s=o.__v_raw,a=_(s),u=t?dt:e?ft:lt;return!e&&S(a,"iterate",k),s.forEach((c,l)=>r.call(i,u(c),u(l),o))}}function fe(e,t,n){return function(...r){const i=this.__v_raw,o=_(i),s=te(o),a=e==="entries"||e===Symbol.iterator&&s,u=e==="keys"&&s,c=i[e](...r),l=n?dt:t?ft:lt;return!t&&S(o,"iterate",u?qe:k),{next(){const{value:d,done:p}=c.next();return p?{value:d,done:p}:{value:a?[l(d[0]),l(d[1])]:l(d),done:p}},[Symbol.iterator](){return this}}}}function T(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${ai(e)} operation ${n}failed: target is readonly.`,_(this))}return e==="delete"?!1:this}}function Oi(){const e={get(o){return ae(this,o)},get size(){return ce(this)},has:ue,add:mt,set:wt,delete:Et,clear:St,forEach:le(!1,!1)},t={get(o){return ae(this,o,!1,!0)},get size(){return ce(this)},has:ue,add:mt,set:wt,delete:Et,clear:St,forEach:le(!1,!0)},n={get(o){return ae(this,o,!0)},get size(){return ce(this,!0)},has(o){return ue.call(this,o,!0)},add:T("add"),set:T("set"),delete:T("delete"),clear:T("clear"),forEach:le(!0,!1)},r={get(o){return ae(this,o,!0,!0)},get size(){return ce(this,!0)},has(o){return ue.call(this,o,!0)},add:T("add"),set:T("set"),delete:T("delete"),clear:T("clear"),forEach:le(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=fe(o,!1,!1),n[o]=fe(o,!0,!1),t[o]=fe(o,!1,!0),r[o]=fe(o,!0,!0)}),[e,n,t,r]}var[Ci,Mi,Qi,eo]=Oi();function Tn(e,t){const n=e?Mi:Ci;return(r,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get(me(n,i)&&i in r?n:r,i,o)}var Ti={get:Tn(!1)},Ii={get:Tn(!0)};function In(e,t,n){const r=_(n);if(r!==n&&t.call(e,r)){const i=En(e);console.warn(`Reactive ${i} contains both the raw and reactive versions of the same object${i==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var $n=new WeakMap,$i=new WeakMap,Pn=new WeakMap,Pi=new WeakMap;function Ri(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ji(e){return e.__v_skip||!Object.isExtensible(e)?0:Ri(En(e))}function pt(e){return e&&e.__v_isReadonly?e:jn(e,!1,Si,Ti,$n)}function Rn(e){return jn(e,!0,Ai,Ii,Pn)}function jn(e,t,n,r,i){if(!we(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=i.get(e);if(o)return o;const s=ji(e);if(s===0)return e;const a=new Proxy(e,s===2?r:n);return i.set(e,a),a}function _(e){return e&&_(e.__v_raw)||e}function We(e){return!!(e&&e.__v_isRef===!0)}A("nextTick",()=>it);A("dispatch",e=>ee.bind(ee,e));A("watch",(e,{evaluateLater:t,cleanup:n})=>(r,i)=>{let o=t(r),a=It(()=>{let u;return o(c=>u=c),u},i);n(a)});A("store",Yr);A("data",e=>kt(e));A("root",e=>ye(e));A("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=ie(Ni(e))),e._x_refs_proxy));function Ni(e){let t=[];return J(e,n=>{n._x_refs&&t.push(n._x_refs)}),t}var Ce={};function Nn(e){return Ce[e]||(Ce[e]=0),++Ce[e]}function Li(e,t){return J(e,n=>{if(n._x_ids&&n._x_ids[t])return!0})}function Fi(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=Nn(t))}A("id",(e,{cleanup:t})=>(n,r=null)=>{let i=`${n}${r?`-${r}`:""}`;return ki(e,i,t,()=>{let o=Li(e,n),s=o?o._x_ids[n]:Nn(n);return r?`${n}-${s}-${r}`:`${n}-${s}`})});be((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function ki(e,t,n,r){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let i=r();return e._x_id[t]=i,n(()=>{delete e._x_id[t]}),i}A("el",e=>e);Ln("Focus","focus","focus");Ln("Persist","persist","persist");function Ln(e,t,n){A(t,r=>E(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}y("modelable",(e,{expression:t},{effect:n,evaluateLater:r,cleanup:i})=>{let o=r(t),s=()=>{let l;return o(d=>l=d),l},a=r(`${t} = __placeholder`),u=l=>a(()=>{},{scope:{__placeholder:l}}),c=s();u(c),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let l=e._x_model.get,d=e._x_model.set,p=yn({get(){return l()},set(v){d(v)}},{get(){return s()},set(v){u(v)}});i(p)})});y("teleport",(e,{modifiers:t,expression:n},{cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&E("x-teleport can only be used on a <template> tag",e);let i=At(n),o=e.content.cloneNode(!0).firstElementChild;e._x_teleport=o,o._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),o.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(a=>{o.addEventListener(a,u=>{u.stopPropagation(),e.dispatchEvent(new u.constructor(u.type,u))})}),re(o,{},e);let s=(a,u,c)=>{c.includes("prepend")?u.parentNode.insertBefore(a,u):c.includes("append")?u.parentNode.insertBefore(a,u.nextSibling):u.appendChild(a)};g(()=>{s(o,i,t),P(()=>{C(o)})()}),e._x_teleportPutBack=()=>{let a=At(n);g(()=>{s(e._x_teleport,a,t)})},r(()=>g(()=>{o.remove(),V(o)}))});var Di=document.createElement("div");function At(e){let t=P(()=>document.querySelector(e),()=>Di)();return t||E(`Cannot find x-teleport element for selector: "${e}"`),t}var Fn=()=>{};Fn.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};y("ignore",Fn);y("effect",P((e,{expression:t},{effect:n})=>{n(m(e,t))}));function Ue(e,t,n,r){let i=e,o=u=>r(u),s={},a=(u,c)=>l=>c(u,l);if(n.includes("dot")&&(t=Bi(t)),n.includes("camel")&&(t=Ki(t)),n.includes("passive")&&(s.passive=!0),n.includes("capture")&&(s.capture=!0),n.includes("window")&&(i=window),n.includes("document")&&(i=document),n.includes("debounce")){let u=n[n.indexOf("debounce")+1]||"invalid-wait",c=xe(u.split("ms")[0])?Number(u.split("ms")[0]):250;o=gn(o,c)}if(n.includes("throttle")){let u=n[n.indexOf("throttle")+1]||"invalid-wait",c=xe(u.split("ms")[0])?Number(u.split("ms")[0]):250;o=xn(o,c)}return n.includes("prevent")&&(o=a(o,(u,c)=>{c.preventDefault(),u(c)})),n.includes("stop")&&(o=a(o,(u,c)=>{c.stopPropagation(),u(c)})),n.includes("once")&&(o=a(o,(u,c)=>{u(c),i.removeEventListener(t,o,s)})),(n.includes("away")||n.includes("outside"))&&(i=document,o=a(o,(u,c)=>{e.contains(c.target)||c.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&u(c))})),n.includes("self")&&(o=a(o,(u,c)=>{c.target===e&&u(c)})),(Hi(t)||kn(t))&&(o=a(o,(u,c)=>{qi(c,n)||u(c)})),i.addEventListener(t,o,s),()=>{i.removeEventListener(t,o,s)}}function Bi(e){return e.replace(/-/g,".")}function Ki(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function xe(e){return!Array.isArray(e)&&!isNaN(e)}function zi(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function Hi(e){return["keydown","keyup"].includes(e)}function kn(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function qi(e,t){let n=t.filter(o=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(o));if(n.includes("debounce")){let o=n.indexOf("debounce");n.splice(o,xe((n[o+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.includes("throttle")){let o=n.indexOf("throttle");n.splice(o,xe((n[o+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0||n.length===1&&Ot(e.key).includes(n[0]))return!1;const i=["ctrl","shift","alt","meta","cmd","super"].filter(o=>n.includes(o));return n=n.filter(o=>!i.includes(o)),!(i.length>0&&i.filter(s=>((s==="cmd"||s==="super")&&(s="meta"),e[`${s}Key`])).length===i.length&&(kn(e.type)||Ot(e.key).includes(n[0])))}function Ot(e){if(!e)return[];e=zi(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(n=>{if(t[n]===e)return n}).filter(n=>n)}y("model",(e,{modifiers:t,expression:n},{effect:r,cleanup:i})=>{let o=e;t.includes("parent")&&(o=e.parentNode);let s=m(o,n),a;typeof n=="string"?a=m(o,`${n} = __placeholder`):typeof n=="function"&&typeof n()=="string"?a=m(o,`${n()} = __placeholder`):a=()=>{};let u=()=>{let p;return s(v=>p=v),Ct(p)?p.get():p},c=p=>{let v;s(M=>v=M),Ct(v)?v.set(p):a(()=>{},{scope:{__placeholder:p}})};typeof n=="string"&&e.type==="radio"&&g(()=>{e.hasAttribute("name")||e.setAttribute("name",n)});var l=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let d=I?()=>{}:Ue(e,l,t,p=>{c(Me(e,t,p,u()))});if(t.includes("fill")&&([void 0,null,""].includes(u())||st(e)&&Array.isArray(u())||e.tagName.toLowerCase()==="select"&&e.multiple)&&c(Me(e,t,{target:e},u())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=d,i(()=>e._x_removeModelListeners.default()),e.form){let p=Ue(e.form,"reset",[],v=>{it(()=>e._x_model&&e._x_model.set(Me(e,t,{target:e},u())))});i(()=>p())}e._x_model={get(){return u()},set(p){c(p)}},e._x_forceModelUpdate=p=>{p===void 0&&typeof n=="string"&&n.match(/\./)&&(p=""),window.fromModel=!0,g(()=>fn(e,"value",p)),delete window.fromModel},r(()=>{let p=u();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(p)})});function Me(e,t,n,r){return g(()=>{if(n instanceof CustomEvent&&n.detail!==void 0)return n.detail!==null&&n.detail!==void 0?n.detail:n.target.value;if(st(e))if(Array.isArray(r)){let i=null;return t.includes("number")?i=Te(n.target.value):t.includes("boolean")?i=pe(n.target.value):i=n.target.value,n.target.checked?r.includes(i)?r:r.concat([i]):r.filter(o=>!Wi(o,i))}else return n.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(n.target.selectedOptions).map(i=>{let o=i.value||i.text;return Te(o)}):t.includes("boolean")?Array.from(n.target.selectedOptions).map(i=>{let o=i.value||i.text;return pe(o)}):Array.from(n.target.selectedOptions).map(i=>i.value||i.text);{let i;return hn(e)?n.target.checked?i=n.target.value:i=r:i=n.target.value,t.includes("number")?Te(i):t.includes("boolean")?pe(i):t.includes("trim")?i.trim():i}}})}function Te(e){let t=e?parseFloat(e):null;return Ui(t)?t:e}function Wi(e,t){return e==t}function Ui(e){return!Array.isArray(e)&&!isNaN(e)}function Ct(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}y("cloak",e=>queueMicrotask(()=>g(()=>e.removeAttribute(U("cloak")))));on(()=>`[${U("init")}]`);y("init",P((e,{expression:t},{evaluate:n})=>typeof t=="string"?!!t.trim()&&n(t,{},!1):n(t,{},!1)));y("text",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(o=>{g(()=>{e.textContent=o})})})});y("html",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(o=>{g(()=>{e.innerHTML=o,e._x_ignoreSelf=!0,C(e),delete e._x_ignoreSelf})})})});tt(Vt(":",Yt(U("bind:"))));var Dn=(e,{value:t,modifiers:n,expression:r,original:i},{effect:o,cleanup:s})=>{if(!t){let u={};Xr(u),m(e,r)(l=>{bn(e,l,i)},{scope:u});return}if(t==="key")return Ji(e,r);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let a=m(e,r);o(()=>a(u=>{u===void 0&&typeof r=="string"&&r.match(/\./)&&(u=""),g(()=>fn(e,t,u,n))})),s(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};Dn.inline=(e,{value:t,modifiers:n,expression:r})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:r,extract:!1})};y("bind",Dn);function Ji(e,t){e._x_keyExpression=t}rn(()=>`[${U("data")}]`);y("data",(e,{expression:t},{cleanup:n})=>{if(Vi(e))return;t=t===""?"{}":t;let r={};Ne(r,e);let i={};Qr(i,r);let o=L(e,t,{scope:i});(o===void 0||o===!0)&&(o={}),Ne(o,e);let s=q(o);Dt(s);let a=re(e,s);s.init&&L(e,s.init),n(()=>{s.destroy&&L(e,s.destroy),a()})});be((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function Vi(e){return I?ze?!0:e.hasAttribute("data-has-alpine-state"):!1}y("show",(e,{modifiers:t,expression:n},{effect:r})=>{let i=m(e,n);e._x_doHide||(e._x_doHide=()=>{g(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{g(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let o=()=>{e._x_doHide(),e._x_isShown=!1},s=()=>{e._x_doShow(),e._x_isShown=!0},a=()=>setTimeout(s),u=Be(d=>d?s():o(),d=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,d,s,o):d?a():o()}),c,l=!0;r(()=>i(d=>{!l&&d===c||(t.includes("immediate")&&(d?a():o()),u(d),c=d,l=!1)}))});y("for",(e,{expression:t},{effect:n,cleanup:r})=>{let i=Gi(t),o=m(e,i.items),s=m(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>Yi(e,i,o,s)),r(()=>{Object.values(e._x_lookup).forEach(a=>g(()=>{V(a),a.remove()})),delete e._x_prevKeys,delete e._x_lookup})});function Yi(e,t,n,r){let i=s=>typeof s=="object"&&!Array.isArray(s),o=e;n(s=>{Xi(s)&&s>=0&&(s=Array.from(Array(s).keys(),f=>f+1)),s===void 0&&(s=[]);let a=e._x_lookup,u=e._x_prevKeys,c=[],l=[];if(i(s))s=Object.entries(s).map(([f,h])=>{let x=Mt(t,h,f,s);r(b=>{l.includes(b)&&E("Duplicate key on x-for",e),l.push(b)},{scope:{index:f,...x}}),c.push(x)});else for(let f=0;f<s.length;f++){let h=Mt(t,s[f],f,s);r(x=>{l.includes(x)&&E("Duplicate key on x-for",e),l.push(x)},{scope:{index:f,...h}}),c.push(h)}let d=[],p=[],v=[],M=[];for(let f=0;f<u.length;f++){let h=u[f];l.indexOf(h)===-1&&v.push(h)}u=u.filter(f=>!v.includes(f));let se="template";for(let f=0;f<l.length;f++){let h=l[f],x=u.indexOf(h);if(x===-1)u.splice(f,0,h),d.push([se,f]);else if(x!==f){let b=u.splice(f,1)[0],w=u.splice(x-1,1)[0];u.splice(f,0,w),u.splice(x,0,b),p.push([b,w])}else M.push(h);se=h}for(let f=0;f<v.length;f++){let h=v[f];h in a&&(g(()=>{V(a[h]),a[h].remove()}),delete a[h])}for(let f=0;f<p.length;f++){let[h,x]=p[f],b=a[h],w=a[x],K=document.createElement("div");g(()=>{w||E('x-for ":key" is undefined or invalid',o,x,a),w.after(K),b.after(w),w._x_currentIfEl&&w.after(w._x_currentIfEl),K.before(b),b._x_currentIfEl&&b.after(b._x_currentIfEl),K.remove()}),w._x_refreshXForScope(c[l.indexOf(x)])}for(let f=0;f<d.length;f++){let[h,x]=d[f],b=h==="template"?o:a[h];b._x_currentIfEl&&(b=b._x_currentIfEl);let w=c[x],K=l[x],Y=document.importNode(o.content,!0).firstElementChild,_t=q(w);re(Y,_t,o),Y._x_refreshXForScope=Kn=>{Object.entries(Kn).forEach(([zn,Hn])=>{_t[zn]=Hn})},g(()=>{b.after(Y),P(()=>C(Y))()}),typeof K=="object"&&E("x-for key cannot be an object, it must be a string or an integer",o),a[K]=Y}for(let f=0;f<M.length;f++)a[M[f]]._x_refreshXForScope(c[l.indexOf(M[f])]);o._x_prevKeys=l})}function Gi(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,n=/^\s*\(|\)\s*$/g,r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,i=e.match(r);if(!i)return;let o={};o.items=i[2].trim();let s=i[1].replace(n,"").trim(),a=s.match(t);return a?(o.item=s.replace(t,"").trim(),o.index=a[1].trim(),a[2]&&(o.collection=a[2].trim())):o.item=s,o}function Mt(e,t,n,r){let i={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(s=>s.trim()).forEach((s,a)=>{i[s]=t[a]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(s=>s.trim()).forEach(s=>{i[s]=t[s]}):i[e.item]=t,e.index&&(i[e.index]=n),e.collection&&(i[e.collection]=r),i}function Xi(e){return!Array.isArray(e)&&!isNaN(e)}function Bn(){}Bn.inline=(e,{expression:t},{cleanup:n})=>{let r=ye(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])};y("ref",Bn);y("if",(e,{expression:t},{effect:n,cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&E("x-if can only be used on a <template> tag",e);let i=m(e,t),o=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let a=e.content.cloneNode(!0).firstElementChild;return re(a,{},e),g(()=>{e.after(a),P(()=>C(a))()}),e._x_currentIfEl=a,e._x_undoIf=()=>{g(()=>{V(a),a.remove()}),delete e._x_currentIfEl},a},s=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};n(()=>i(a=>{a?o():s()})),r(()=>e._x_undoIf&&e._x_undoIf())});y("id",(e,{expression:t},{evaluate:n})=>{n(t).forEach(i=>Fi(e,i))});be((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});tt(Vt("@",Yt(U("on:"))));y("on",P((e,{value:t,modifiers:n,expression:r},{cleanup:i})=>{let o=r?m(e,r):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let s=Ue(e,t,n,a=>{o(()=>{},{scope:{$event:a},params:[a]})});i(()=>s())}));Se("Collapse","collapse","collapse");Se("Intersect","intersect","intersect");Se("Focus","trap","focus");Se("Mask","mask","mask");function Se(e,t,n){y(t,r=>E(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}oe.setEvaluator(qt);oe.setReactivityEngine({reactive:pt,effect:ci,release:li,raw:_});var Zi=oe,to=Zi;export{to as m};
