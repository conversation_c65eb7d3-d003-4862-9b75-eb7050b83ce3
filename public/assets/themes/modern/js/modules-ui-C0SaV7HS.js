import{g as ne}from"./vendor-gsap-D3jsOSc0.js";import{c as b,g as wi,l as xi}from"./core-lNA10VOR.js";function bi(o,e){for(var n=0;n<e.length;n++){var t=e[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(o,t.key,t)}}function Ti(o,e,n){return e&&bi(o.prototype,e),o}/*!
 * Observer 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: <PERSON>, <EMAIL>
*/var ye,Vr,Ke,Rt,Ot,or,Qn,Xt,wr,Zn,bt,at,Jn,jn=function(){return ye||typeof window<"u"&&(ye=window.gsap)&&ye.registerPlugin&&ye},ei=1,ir=[],P=[],pt=[],xr=Date.now,pn=function(e,n){return n},ki=function(){var e=wr.core,n=e.bridge||{},t=e._scrollers,r=e._proxies;t.push.apply(t,P),r.push.apply(r,pt),P=t,pt=r,pn=function(l,a){return n[l](a)}},Lt=function(e,n){return~pt.indexOf(e)&&pt[pt.indexOf(e)+1][n]},br=function(e){return!!~Zn.indexOf(e)},Le=function(e,n,t,r,i){return e.addEventListener(n,t,{passive:r!==!1,capture:!!i})},Oe=function(e,n,t,r){return e.removeEventListener(n,t,!!r)},Lr="scrollLeft",Ir="scrollTop",hn=function(){return bt&&bt.isPressed||P.cache++},Jr=function(e,n){var t=function r(i){if(i||i===0){ei&&(Ke.history.scrollRestoration="manual");var l=bt&&bt.isPressed;i=r.v=Math.round(i)||(bt&&bt.iOS?1:0),e(i),r.cacheID=P.cache,l&&pn("ss",i)}else(n||P.cache!==r.cacheID||pn("ref"))&&(r.cacheID=P.cache,r.v=e());return r.v+r.offset};return t.offset=0,e&&t},Be={s:Lr,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:Jr(function(o){return arguments.length?Ke.scrollTo(o,ue.sc()):Ke.pageXOffset||Rt[Lr]||Ot[Lr]||or[Lr]||0})},ue={s:Ir,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:Be,sc:Jr(function(o){return arguments.length?Ke.scrollTo(Be.sc(),o):Ke.pageYOffset||Rt[Ir]||Ot[Ir]||or[Ir]||0})},Xe=function(e,n){return(n&&n._ctx&&n._ctx.selector||ye.utils.toArray)(e)[0]||(typeof e=="string"&&ye.config().nullTargetWarn!==!1?console.warn("Element not found:",e):null)},Ci=function(e,n){for(var t=n.length;t--;)if(n[t]===e||n[t].contains(e))return!0;return!1},It=function(e,n){var t=n.s,r=n.sc;br(e)&&(e=Rt.scrollingElement||Ot);var i=P.indexOf(e),l=r===ue.sc?1:2;!~i&&(i=P.push(e)-1),P[i+l]||Le(e,"scroll",hn);var a=P[i+l],d=a||(P[i+l]=Jr(Lt(e,t),!0)||(br(e)?r:Jr(function(y){return arguments.length?e[t]=y:e[t]})));return d.target=e,a||(d.smooth=ye.getProperty(e,"scrollBehavior")==="smooth"),d},gn=function(e,n,t){var r=e,i=e,l=xr(),a=l,d=n||50,y=Math.max(500,d*3),I=function(_,V){var N=xr();V||N-l>d?(i=r,r=_,a=l,l=N):t?r+=_:r=i+(_-i)/(N-a)*(l-a)},D=function(){i=r=t?0:r,a=l=0},g=function(_){var V=a,N=i,oe=xr();return(_||_===0)&&_!==r&&I(_),l===a||oe-a>y?0:(r+(t?N:-N))/((t?oe:l)-V)*1e3};return{update:I,reset:D,getVelocity:g}},pr=function(e,n){return n&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e},Rn=function(e){var n=Math.max.apply(Math,e),t=Math.min.apply(Math,e);return Math.abs(n)>=Math.abs(t)?n:t},ti=function(){wr=ye.core.globals().ScrollTrigger,wr&&wr.core&&ki()},ri=function(e){return ye=e||jn(),!Vr&&ye&&typeof document<"u"&&document.body&&(Ke=window,Rt=document,Ot=Rt.documentElement,or=Rt.body,Zn=[Ke,Rt,Ot,or],ye.utils.clamp,Jn=ye.core.context||function(){},Xt="onpointerenter"in or?"pointer":"mouse",Qn=J.isTouch=Ke.matchMedia&&Ke.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in Ke||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,at=J.eventTypes=("ontouchstart"in Ot?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in Ot?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return ei=0},500),ti(),Vr=1),Vr};Be.op=ue;P.cache=0;var J=function(){function o(n){this.init(n)}var e=o.prototype;return e.init=function(t){Vr||ri(ye)||console.warn("Please gsap.registerPlugin(Observer)"),wr||ti();var r=t.tolerance,i=t.dragMinimum,l=t.type,a=t.target,d=t.lineHeight,y=t.debounce,I=t.preventDefault,D=t.onStop,g=t.onStopDelay,u=t.ignore,_=t.wheelSpeed,V=t.event,N=t.onDragStart,oe=t.onDragEnd,$=t.onDrag,me=t.onPress,S=t.onRelease,Qe=t.onRight,X=t.onLeft,x=t.onUp,Me=t.onDown,Fe=t.onChangeX,h=t.onChangeY,fe=t.onChange,w=t.onToggleX,ht=t.onToggleY,se=t.onHover,Ae=t.onHoverEnd,Pe=t.onMove,B=t.ignoreCheck,j=t.isNormalizer,ee=t.onGestureStart,s=t.onGestureEnd,ae=t.onWheel,zt=t.onEnable,kt=t.onDisable,Ze=t.onClick,gt=t.scrollSpeed,we=t.capture,te=t.allowClicks,De=t.lockAxis,xe=t.onLockAxis;this.target=a=Xe(a)||Ot,this.vars=t,u&&(u=ye.utils.toArray(u)),r=r||1e-9,i=i||0,_=_||1,gt=gt||1,l=l||"wheel,touch,pointer",y=y!==!1,d||(d=parseFloat(Ke.getComputedStyle(or).lineHeight)||22);var Ct,Re,Ne,L,K,He,We,c=this,$e=0,mt=0,St=t.passive||!I&&t.passive!==!1,q=It(a,Be),_t=It(a,ue),Et=q(),Yt=_t(),de=~l.indexOf("touch")&&!~l.indexOf("pointer")&&at[0]==="pointerdown",Mt=br(a),Q=a.ownerDocument||Rt,rt=[0,0,0],Je=[0,0,0],vt=0,cr=function(){return vt=xr()},re=function(v,z){return(c.event=v)&&u&&Ci(v.target,u)||z&&de&&v.pointerType!=="touch"||B&&B(v,z)},Dr=function(){c._vx.reset(),c._vy.reset(),Re.pause(),D&&D(c)},yt=function(){var v=c.deltaX=Rn(rt),z=c.deltaY=Rn(Je),f=Math.abs(v)>=r,T=Math.abs(z)>=r;fe&&(f||T)&&fe(c,v,z,rt,Je),f&&(Qe&&c.deltaX>0&&Qe(c),X&&c.deltaX<0&&X(c),Fe&&Fe(c),w&&c.deltaX<0!=$e<0&&w(c),$e=c.deltaX,rt[0]=rt[1]=rt[2]=0),T&&(Me&&c.deltaY>0&&Me(c),x&&c.deltaY<0&&x(c),h&&h(c),ht&&c.deltaY<0!=mt<0&&ht(c),mt=c.deltaY,Je[0]=Je[1]=Je[2]=0),(L||Ne)&&(Pe&&Pe(c),Ne&&(N&&Ne===1&&N(c),$&&$(c),Ne=0),L=!1),He&&!(He=!1)&&xe&&xe(c),K&&(ae(c),K=!1),Ct=0},Zt=function(v,z,f){rt[f]+=v,Je[f]+=z,c._vx.update(v),c._vy.update(z),y?Ct||(Ct=requestAnimationFrame(yt)):yt()},Jt=function(v,z){De&&!We&&(c.axis=We=Math.abs(v)>Math.abs(z)?"x":"y",He=!0),We!=="y"&&(rt[2]+=v,c._vx.update(v,!0)),We!=="x"&&(Je[2]+=z,c._vy.update(z,!0)),y?Ct||(Ct=requestAnimationFrame(yt)):yt()},At=function(v){if(!re(v,1)){v=pr(v,I);var z=v.clientX,f=v.clientY,T=z-c.x,m=f-c.y,k=c.isDragging;c.x=z,c.y=f,(k||(T||m)&&(Math.abs(c.startX-z)>=i||Math.abs(c.startY-f)>=i))&&(Ne=k?2:1,k||(c.isDragging=!0),Jt(T,m))}},Bt=c.onPress=function(C){re(C,1)||C&&C.button||(c.axis=We=null,Re.pause(),c.isPressed=!0,C=pr(C),$e=mt=0,c.startX=c.x=C.clientX,c.startY=c.y=C.clientY,c._vx.reset(),c._vy.reset(),Le(j?a:Q,at[1],At,St,!0),c.deltaX=c.deltaY=0,me&&me(c))},R=c.onRelease=function(C){if(!re(C,1)){Oe(j?a:Q,at[1],At,!0);var v=!isNaN(c.y-c.startY),z=c.isDragging,f=z&&(Math.abs(c.x-c.startX)>3||Math.abs(c.y-c.startY)>3),T=pr(C);!f&&v&&(c._vx.reset(),c._vy.reset(),I&&te&&ye.delayedCall(.08,function(){if(xr()-vt>300&&!C.defaultPrevented){if(C.target.click)C.target.click();else if(Q.createEvent){var m=Q.createEvent("MouseEvents");m.initMouseEvent("click",!0,!0,Ke,1,T.screenX,T.screenY,T.clientX,T.clientY,!1,!1,!1,!1,0,null),C.target.dispatchEvent(m)}}})),c.isDragging=c.isGesturing=c.isPressed=!1,D&&z&&!j&&Re.restart(!0),Ne&&yt(),oe&&z&&oe(c),S&&S(c,f)}},Ft=function(v){return v.touches&&v.touches.length>1&&(c.isGesturing=!0)&&ee(v,c.isDragging)},nt=function(){return(c.isGesturing=!1)||s(c)},it=function(v){if(!re(v)){var z=q(),f=_t();Zt((z-Et)*gt,(f-Yt)*gt,1),Et=z,Yt=f,D&&Re.restart(!0)}},ot=function(v){if(!re(v)){v=pr(v,I),ae&&(K=!0);var z=(v.deltaMode===1?d:v.deltaMode===2?Ke.innerHeight:1)*_;Zt(v.deltaX*z,v.deltaY*z,0),D&&!j&&Re.restart(!0)}},Nt=function(v){if(!re(v)){var z=v.clientX,f=v.clientY,T=z-c.x,m=f-c.y;c.x=z,c.y=f,L=!0,D&&Re.restart(!0),(T||m)&&Jt(T,m)}},jt=function(v){c.event=v,se(c)},wt=function(v){c.event=v,Ae(c)},ur=function(v){return re(v)||pr(v,I)&&Ze(c)};Re=c._dc=ye.delayedCall(g||.25,Dr).pause(),c.deltaX=c.deltaY=0,c._vx=gn(0,50,!0),c._vy=gn(0,50,!0),c.scrollX=q,c.scrollY=_t,c.isDragging=c.isGesturing=c.isPressed=!1,Jn(this),c.enable=function(C){return c.isEnabled||(Le(Mt?Q:a,"scroll",hn),l.indexOf("scroll")>=0&&Le(Mt?Q:a,"scroll",it,St,we),l.indexOf("wheel")>=0&&Le(a,"wheel",ot,St,we),(l.indexOf("touch")>=0&&Qn||l.indexOf("pointer")>=0)&&(Le(a,at[0],Bt,St,we),Le(Q,at[2],R),Le(Q,at[3],R),te&&Le(a,"click",cr,!0,!0),Ze&&Le(a,"click",ur),ee&&Le(Q,"gesturestart",Ft),s&&Le(Q,"gestureend",nt),se&&Le(a,Xt+"enter",jt),Ae&&Le(a,Xt+"leave",wt),Pe&&Le(a,Xt+"move",Nt)),c.isEnabled=!0,c.isDragging=c.isGesturing=c.isPressed=L=Ne=!1,c._vx.reset(),c._vy.reset(),Et=q(),Yt=_t(),C&&C.type&&Bt(C),zt&&zt(c)),c},c.disable=function(){c.isEnabled&&(ir.filter(function(C){return C!==c&&br(C.target)}).length||Oe(Mt?Q:a,"scroll",hn),c.isPressed&&(c._vx.reset(),c._vy.reset(),Oe(j?a:Q,at[1],At,!0)),Oe(Mt?Q:a,"scroll",it,we),Oe(a,"wheel",ot,we),Oe(a,at[0],Bt,we),Oe(Q,at[2],R),Oe(Q,at[3],R),Oe(a,"click",cr,!0),Oe(a,"click",ur),Oe(Q,"gesturestart",Ft),Oe(Q,"gestureend",nt),Oe(a,Xt+"enter",jt),Oe(a,Xt+"leave",wt),Oe(a,Xt+"move",Nt),c.isEnabled=c.isPressed=c.isDragging=!1,kt&&kt(c))},c.kill=c.revert=function(){c.disable();var C=ir.indexOf(c);C>=0&&ir.splice(C,1),bt===c&&(bt=0)},ir.push(c),j&&br(a)&&(bt=c),c.enable(V)},Ti(o,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),o}();J.version="3.13.0";J.create=function(o){return new J(o)};J.register=ri;J.getAll=function(){return ir.slice()};J.getById=function(o){return ir.filter(function(e){return e.vars.id===o})[0]};jn()&&ye.registerPlugin(J);/*!
 * ScrollTrigger 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var p,rr,A,H,Ue,Y,Tn,jr,Ar,Tr,gr,zr,Ce,nn,mn,ze,On,Ln,nr,ni,sn,ii,Ie,_n,oi,si,Dt,vn,kn,sr,Cn,en,yn,an,Yr=1,Se=Date.now,ln=Se(),tt=0,mr=0,In=function(e,n,t){var r=qe(e)&&(e.substr(0,6)==="clamp("||e.indexOf("max")>-1);return t["_"+n+"Clamp"]=r,r?e.substr(6,e.length-7):e},zn=function(e,n){return n&&(!qe(e)||e.substr(0,6)!=="clamp(")?"clamp("+e+")":e},Si=function o(){return mr&&requestAnimationFrame(o)},Yn=function(){return nn=1},Bn=function(){return nn=0},ft=function(e){return e},_r=function(e){return Math.round(e*1e5)/1e5||0},ai=function(){return typeof window<"u"},li=function(){return p||ai()&&(p=window.gsap)&&p.registerPlugin&&p},Ut=function(e){return!!~Tn.indexOf(e)},ci=function(e){return(e==="Height"?Cn:A["inner"+e])||Ue["client"+e]||Y["client"+e]},ui=function(e){return Lt(e,"getBoundingClientRect")||(Ut(e)?function(){return Zr.width=A.innerWidth,Zr.height=Cn,Zr}:function(){return xt(e)})},Ei=function(e,n,t){var r=t.d,i=t.d2,l=t.a;return(l=Lt(e,"getBoundingClientRect"))?function(){return l()[r]}:function(){return(n?ci(i):e["client"+i])||0}},Mi=function(e,n){return!n||~pt.indexOf(e)?ui(e):function(){return Zr}},dt=function(e,n){var t=n.s,r=n.d2,i=n.d,l=n.a;return Math.max(0,(t="scroll"+r)&&(l=Lt(e,t))?l()-ui(e)()[i]:Ut(e)?(Ue[t]||Y[t])-ci(r):e[t]-e["offset"+r])},Br=function(e,n){for(var t=0;t<nr.length;t+=3)(!n||~n.indexOf(nr[t+1]))&&e(nr[t],nr[t+1],nr[t+2])},qe=function(e){return typeof e=="string"},Ee=function(e){return typeof e=="function"},vr=function(e){return typeof e=="number"},Wt=function(e){return typeof e=="object"},hr=function(e,n,t){return e&&e.progress(n?0:1)&&t&&e.pause()},cn=function(e,n){if(e.enabled){var t=e._ctx?e._ctx.add(function(){return n(e)}):n(e);t&&t.totalTime&&(e.callbackAnimation=t)}},er=Math.abs,fi="left",di="top",Sn="right",En="bottom",Gt="width",Vt="height",kr="Right",Cr="Left",Sr="Top",Er="Bottom",ie="padding",je="margin",lr="Width",Mn="Height",ce="px",et=function(e){return A.getComputedStyle(e)},Ai=function(e){var n=et(e).position;e.style.position=n==="absolute"||n==="fixed"?n:"relative"},Fn=function(e,n){for(var t in n)t in e||(e[t]=n[t]);return e},xt=function(e,n){var t=n&&et(e)[mn]!=="matrix(1, 0, 0, 1, 0, 0)"&&p.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),r=e.getBoundingClientRect();return t&&t.progress(0).kill(),r},tn=function(e,n){var t=n.d2;return e["offset"+t]||e["client"+t]||0},pi=function(e){var n=[],t=e.labels,r=e.duration(),i;for(i in t)n.push(t[i]/r);return n},Pi=function(e){return function(n){return p.utils.snap(pi(e),n)}},An=function(e){var n=p.utils.snap(e),t=Array.isArray(e)&&e.slice(0).sort(function(r,i){return r-i});return t?function(r,i,l){l===void 0&&(l=.001);var a;if(!i)return n(r);if(i>0){for(r-=l,a=0;a<t.length;a++)if(t[a]>=r)return t[a];return t[a-1]}else for(a=t.length,r+=l;a--;)if(t[a]<=r)return t[a];return t[0]}:function(r,i,l){l===void 0&&(l=.001);var a=n(r);return!i||Math.abs(a-r)<l||a-r<0==i<0?a:n(i<0?r-e:r+e)}},Di=function(e){return function(n,t){return An(pi(e))(n,t.direction)}},Fr=function(e,n,t,r){return t.split(",").forEach(function(i){return e(n,i,r)})},ge=function(e,n,t,r,i){return e.addEventListener(n,t,{passive:!r,capture:!!i})},he=function(e,n,t,r){return e.removeEventListener(n,t,!!r)},Nr=function(e,n,t){t=t&&t.wheelHandler,t&&(e(n,"wheel",t),e(n,"touchmove",t))},Nn={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Hr={toggleActions:"play",anticipatePin:0},rn={top:0,left:0,center:.5,bottom:1,right:1},qr=function(e,n){if(qe(e)){var t=e.indexOf("="),r=~t?+(e.charAt(t-1)+1)*parseFloat(e.substr(t+1)):0;~t&&(e.indexOf("%")>t&&(r*=n/100),e=e.substr(0,t-1)),e=r+(e in rn?rn[e]*n:~e.indexOf("%")?parseFloat(e)*n/100:parseFloat(e)||0)}return e},Xr=function(e,n,t,r,i,l,a,d){var y=i.startColor,I=i.endColor,D=i.fontSize,g=i.indent,u=i.fontWeight,_=H.createElement("div"),V=Ut(t)||Lt(t,"pinType")==="fixed",N=e.indexOf("scroller")!==-1,oe=V?Y:t,$=e.indexOf("start")!==-1,me=$?y:I,S="border-color:"+me+";font-size:"+D+";color:"+me+";font-weight:"+u+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return S+="position:"+((N||d)&&V?"fixed;":"absolute;"),(N||d||!V)&&(S+=(r===ue?Sn:En)+":"+(l+parseFloat(g))+"px;"),a&&(S+="box-sizing:border-box;text-align:left;width:"+a.offsetWidth+"px;"),_._isStart=$,_.setAttribute("class","gsap-marker-"+e+(n?" marker-"+n:"")),_.style.cssText=S,_.innerText=n||n===0?e+"-"+n:e,oe.children[0]?oe.insertBefore(_,oe.children[0]):oe.appendChild(_),_._offset=_["offset"+r.op.d2],Ur(_,0,r,$),_},Ur=function(e,n,t,r){var i={display:"block"},l=t[r?"os2":"p2"],a=t[r?"p2":"os2"];e._isFlipped=r,i[t.a+"Percent"]=r?-100:0,i[t.a]=r?"1px":0,i["border"+l+lr]=1,i["border"+a+lr]=0,i[t.p]=n+"px",p.set(e,i)},E=[],wn={},Pr,Hn=function(){return Se()-tt>34&&(Pr||(Pr=requestAnimationFrame(Tt)))},tr=function(){(!Ie||!Ie.isPressed||Ie.startX>Y.clientWidth)&&(P.cache++,Ie?Pr||(Pr=requestAnimationFrame(Tt)):Tt(),tt||Qt("scrollStart"),tt=Se())},un=function(){si=A.innerWidth,oi=A.innerHeight},yr=function(e){P.cache++,(e===!0||!Ce&&!ii&&!H.fullscreenElement&&!H.webkitFullscreenElement&&(!_n||si!==A.innerWidth||Math.abs(A.innerHeight-oi)>A.innerHeight*.25))&&jr.restart(!0)},Kt={},Ri=[],hi=function o(){return he(M,"scrollEnd",o)||$t(!0)},Qt=function(e){return Kt[e]&&Kt[e].map(function(n){return n()})||Ri},Ve=[],gi=function(e){for(var n=0;n<Ve.length;n+=5)(!e||Ve[n+4]&&Ve[n+4].query===e)&&(Ve[n].style.cssText=Ve[n+1],Ve[n].getBBox&&Ve[n].setAttribute("transform",Ve[n+2]||""),Ve[n+3].uncache=1)},Pn=function(e,n){var t;for(ze=0;ze<E.length;ze++)t=E[ze],t&&(!n||t._ctx===n)&&(e?t.kill(1):t.revert(!0,!0));en=!0,n&&gi(n),n||Qt("revert")},mi=function(e,n){P.cache++,(n||!Ye)&&P.forEach(function(t){return Ee(t)&&t.cacheID++&&(t.rec=0)}),qe(e)&&(A.history.scrollRestoration=kn=e)},Ye,qt=0,Xn,Oi=function(){if(Xn!==qt){var e=Xn=qt;requestAnimationFrame(function(){return e===qt&&$t(!0)})}},_i=function(){Y.appendChild(sr),Cn=!Ie&&sr.offsetHeight||A.innerHeight,Y.removeChild(sr)},Wn=function(e){return Ar(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(n){return n.style.display=e?"none":"block"})},$t=function(e,n){if(Ue=H.documentElement,Y=H.body,Tn=[A,H,Ue,Y],tt&&!e&&!en){ge(M,"scrollEnd",hi);return}_i(),Ye=M.isRefreshing=!0,P.forEach(function(r){return Ee(r)&&++r.cacheID&&(r.rec=r())});var t=Qt("refreshInit");ni&&M.sort(),n||Pn(),P.forEach(function(r){Ee(r)&&(r.smooth&&(r.target.style.scrollBehavior="auto"),r(0))}),E.slice(0).forEach(function(r){return r.refresh()}),en=!1,E.forEach(function(r){if(r._subPinOffset&&r.pin){var i=r.vars.horizontal?"offsetWidth":"offsetHeight",l=r.pin[i];r.revert(!0,1),r.adjustPinSpacing(r.pin[i]-l),r.refresh()}}),yn=1,Wn(!0),E.forEach(function(r){var i=dt(r.scroller,r._dir),l=r.vars.end==="max"||r._endClamp&&r.end>i,a=r._startClamp&&r.start>=i;(l||a)&&r.setPositions(a?i-1:r.start,l?Math.max(a?i:r.start+1,i):r.end,!0)}),Wn(!1),yn=0,t.forEach(function(r){return r&&r.render&&r.render(-1)}),P.forEach(function(r){Ee(r)&&(r.smooth&&requestAnimationFrame(function(){return r.target.style.scrollBehavior="smooth"}),r.rec&&r(r.rec))}),mi(kn,1),jr.pause(),qt++,Ye=2,Tt(2),E.forEach(function(r){return Ee(r.vars.onRefresh)&&r.vars.onRefresh(r)}),Ye=M.isRefreshing=!1,Qt("refresh")},xn=0,Kr=1,Mr,Tt=function(e){if(e===2||!Ye&&!en){M.isUpdating=!0,Mr&&Mr.update(0);var n=E.length,t=Se(),r=t-ln>=50,i=n&&E[0].scroll();if(Kr=xn>i?-1:1,Ye||(xn=i),r&&(tt&&!nn&&t-tt>200&&(tt=0,Qt("scrollEnd")),gr=ln,ln=t),Kr<0){for(ze=n;ze-- >0;)E[ze]&&E[ze].update(0,r);Kr=1}else for(ze=0;ze<n;ze++)E[ze]&&E[ze].update(0,r);M.isUpdating=!1}Pr=0},bn=[fi,di,En,Sn,je+Er,je+kr,je+Sr,je+Cr,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Qr=bn.concat([Gt,Vt,"boxSizing","max"+lr,"max"+Mn,"position",je,ie,ie+Sr,ie+kr,ie+Er,ie+Cr]),Li=function(e,n,t){ar(t);var r=e._gsap;if(r.spacerIsNative)ar(r.spacerState);else if(e._gsap.swappedIn){var i=n.parentNode;i&&(i.insertBefore(e,n),i.removeChild(n))}e._gsap.swappedIn=!1},fn=function(e,n,t,r){if(!e._gsap.swappedIn){for(var i=bn.length,l=n.style,a=e.style,d;i--;)d=bn[i],l[d]=t[d];l.position=t.position==="absolute"?"absolute":"relative",t.display==="inline"&&(l.display="inline-block"),a[En]=a[Sn]="auto",l.flexBasis=t.flexBasis||"auto",l.overflow="visible",l.boxSizing="border-box",l[Gt]=tn(e,Be)+ce,l[Vt]=tn(e,ue)+ce,l[ie]=a[je]=a[di]=a[fi]="0",ar(r),a[Gt]=a["max"+lr]=t[Gt],a[Vt]=a["max"+Mn]=t[Vt],a[ie]=t[ie],e.parentNode!==n&&(e.parentNode.insertBefore(n,e),n.appendChild(e)),e._gsap.swappedIn=!0}},Ii=/([A-Z])/g,ar=function(e){if(e){var n=e.t.style,t=e.length,r=0,i,l;for((e.t._gsap||p.core.getCache(e.t)).uncache=1;r<t;r+=2)l=e[r+1],i=e[r],l?n[i]=l:n[i]&&n.removeProperty(i.replace(Ii,"-$1").toLowerCase())}},Wr=function(e){for(var n=Qr.length,t=e.style,r=[],i=0;i<n;i++)r.push(Qr[i],t[Qr[i]]);return r.t=e,r},zi=function(e,n,t){for(var r=[],i=e.length,l=t?8:0,a;l<i;l+=2)a=e[l],r.push(a,a in n?n[a]:e[l+1]);return r.t=e.t,r},Zr={left:0,top:0},$n=function(e,n,t,r,i,l,a,d,y,I,D,g,u,_){Ee(e)&&(e=e(d)),qe(e)&&e.substr(0,3)==="max"&&(e=g+(e.charAt(4)==="="?qr("0"+e.substr(3),t):0));var V=u?u.time():0,N,oe,$;if(u&&u.seek(0),isNaN(e)||(e=+e),vr(e))u&&(e=p.utils.mapRange(u.scrollTrigger.start,u.scrollTrigger.end,0,g,e)),a&&Ur(a,t,r,!0);else{Ee(n)&&(n=n(d));var me=(e||"0").split(" "),S,Qe,X,x;$=Xe(n,d)||Y,S=xt($)||{},(!S||!S.left&&!S.top)&&et($).display==="none"&&(x=$.style.display,$.style.display="block",S=xt($),x?$.style.display=x:$.style.removeProperty("display")),Qe=qr(me[0],S[r.d]),X=qr(me[1]||"0",t),e=S[r.p]-y[r.p]-I+Qe+i-X,a&&Ur(a,X,r,t-X<20||a._isStart&&X>20),t-=t-X}if(_&&(d[_]=e||-.001,e<0&&(e=0)),l){var Me=e+t,Fe=l._isStart;N="scroll"+r.d2,Ur(l,Me,r,Fe&&Me>20||!Fe&&(D?Math.max(Y[N],Ue[N]):l.parentNode[N])<=Me+1),D&&(y=xt(a),D&&(l.style[r.op.p]=y[r.op.p]-r.op.m-l._offset+ce))}return u&&$&&(N=xt($),u.seek(g),oe=xt($),u._caScrollDist=N[r.p]-oe[r.p],e=e/u._caScrollDist*g),u&&u.seek(V),u?e:Math.round(e)},Yi=/(webkit|moz|length|cssText|inset)/i,Gn=function(e,n,t,r){if(e.parentNode!==n){var i=e.style,l,a;if(n===Y){e._stOrig=i.cssText,a=et(e);for(l in a)!+l&&!Yi.test(l)&&a[l]&&typeof i[l]=="string"&&l!=="0"&&(i[l]=a[l]);i.top=t,i.left=r}else i.cssText=e._stOrig;p.core.getCache(e).uncache=1,n.appendChild(e)}},vi=function(e,n,t){var r=n,i=r;return function(l){var a=Math.round(e());return a!==r&&a!==i&&Math.abs(a-r)>3&&Math.abs(a-i)>3&&(l=a,t&&t()),i=r,r=Math.round(l),r}},$r=function(e,n,t){var r={};r[n.p]="+="+t,p.set(e,r)},Vn=function(e,n){var t=It(e,n),r="_scroll"+n.p2,i=function l(a,d,y,I,D){var g=l.tween,u=d.onComplete,_={};y=y||t();var V=vi(t,y,function(){g.kill(),l.tween=0});return D=I&&D||0,I=I||a-y,g&&g.kill(),d[r]=a,d.inherit=!1,d.modifiers=_,_[r]=function(){return V(y+I*g.ratio+D*g.ratio*g.ratio)},d.onUpdate=function(){P.cache++,l.tween&&Tt()},d.onComplete=function(){l.tween=0,u&&u.call(g)},g=l.tween=p.to(e,d),g};return e[r]=t,t.wheelHandler=function(){return i.tween&&i.tween.kill()&&(i.tween=0)},ge(e,"wheel",t.wheelHandler),M.isTouch&&ge(e,"touchmove",t.wheelHandler),i},M=function(){function o(n,t){rr||o.register(p)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),vn(this),this.init(n,t)}var e=o.prototype;return e.init=function(t,r){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!mr){this.update=this.refresh=this.kill=ft;return}t=Fn(qe(t)||vr(t)||t.nodeType?{trigger:t}:t,Hr);var i=t,l=i.onUpdate,a=i.toggleClass,d=i.id,y=i.onToggle,I=i.onRefresh,D=i.scrub,g=i.trigger,u=i.pin,_=i.pinSpacing,V=i.invalidateOnRefresh,N=i.anticipatePin,oe=i.onScrubComplete,$=i.onSnapComplete,me=i.once,S=i.snap,Qe=i.pinReparent,X=i.pinSpacer,x=i.containerAnimation,Me=i.fastScrollEnd,Fe=i.preventOverlaps,h=t.horizontal||t.containerAnimation&&t.horizontal!==!1?Be:ue,fe=!D&&D!==0,w=Xe(t.scroller||A),ht=p.core.getCache(w),se=Ut(w),Ae=("pinType"in t?t.pinType:Lt(w,"pinType")||se&&"fixed")==="fixed",Pe=[t.onEnter,t.onLeave,t.onEnterBack,t.onLeaveBack],B=fe&&t.toggleActions.split(" "),j="markers"in t?t.markers:Hr.markers,ee=se?0:parseFloat(et(w)["border"+h.p2+lr])||0,s=this,ae=t.onRefreshInit&&function(){return t.onRefreshInit(s)},zt=Ei(w,se,h),kt=Mi(w,se),Ze=0,gt=0,we=0,te=It(w,h),De,xe,Ct,Re,Ne,L,K,He,We,c,$e,mt,St,q,_t,Et,Yt,de,Mt,Q,rt,Je,vt,cr,re,Dr,yt,Zt,Jt,At,Bt,R,Ft,nt,it,ot,Nt,jt,wt;if(s._startClamp=s._endClamp=!1,s._dir=h,N*=45,s.scroller=w,s.scroll=x?x.time.bind(x):te,Re=te(),s.vars=t,r=r||t.animation,"refreshPriority"in t&&(ni=1,t.refreshPriority===-9999&&(Mr=s)),ht.tweenScroll=ht.tweenScroll||{top:Vn(w,ue),left:Vn(w,Be)},s.tweenTo=De=ht.tweenScroll[h.p],s.scrubDuration=function(f){Ft=vr(f)&&f,Ft?R?R.duration(f):R=p.to(r,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:Ft,paused:!0,onComplete:function(){return oe&&oe(s)}}):(R&&R.progress(1).kill(),R=0)},r&&(r.vars.lazy=!1,r._initted&&!s.isReverted||r.vars.immediateRender!==!1&&t.immediateRender!==!1&&r.duration()&&r.render(0,!0,!0),s.animation=r.pause(),r.scrollTrigger=s,s.scrubDuration(D),At=0,d||(d=r.vars.id)),S&&((!Wt(S)||S.push)&&(S={snapTo:S}),"scrollBehavior"in Y.style&&p.set(se?[Y,Ue]:w,{scrollBehavior:"auto"}),P.forEach(function(f){return Ee(f)&&f.target===(se?H.scrollingElement||Ue:w)&&(f.smooth=!1)}),Ct=Ee(S.snapTo)?S.snapTo:S.snapTo==="labels"?Pi(r):S.snapTo==="labelsDirectional"?Di(r):S.directional!==!1?function(f,T){return An(S.snapTo)(f,Se()-gt<500?0:T.direction)}:p.utils.snap(S.snapTo),nt=S.duration||{min:.1,max:2},nt=Wt(nt)?Tr(nt.min,nt.max):Tr(nt,nt),it=p.delayedCall(S.delay||Ft/2||.1,function(){var f=te(),T=Se()-gt<500,m=De.tween;if((T||Math.abs(s.getVelocity())<10)&&!m&&!nn&&Ze!==f){var k=(f-L)/q,pe=r&&!fe?r.totalProgress():k,O=T?0:(pe-Bt)/(Se()-gr)*1e3||0,Z=p.utils.clamp(-k,1-k,er(O/2)*O/.185),be=k+(S.inertia===!1?0:Z),U,W,F=S,st=F.onStart,G=F.onInterrupt,Ge=F.onComplete;if(U=Ct(be,s),vr(U)||(U=be),W=Math.max(0,Math.round(L+U*q)),f<=K&&f>=L&&W!==f){if(m&&!m._initted&&m.data<=er(W-f))return;S.inertia===!1&&(Z=U-k),De(W,{duration:nt(er(Math.max(er(be-pe),er(U-pe))*.185/O/.05||0)),ease:S.ease||"power3",data:er(W-f),onInterrupt:function(){return it.restart(!0)&&G&&G(s)},onComplete:function(){s.update(),Ze=te(),r&&!fe&&(R?R.resetTo("totalProgress",U,r._tTime/r._tDur):r.progress(U)),At=Bt=r&&!fe?r.totalProgress():s.progress,$&&$(s),Ge&&Ge(s)}},f,Z*q,W-f-Z*q),st&&st(s,De.tween)}}else s.isActive&&Ze!==f&&it.restart(!0)}).pause()),d&&(wn[d]=s),g=s.trigger=Xe(g||u!==!0&&u),wt=g&&g._gsap&&g._gsap.stRevert,wt&&(wt=wt(s)),u=u===!0?g:Xe(u),qe(a)&&(a={targets:g,className:a}),u&&(_===!1||_===je||(_=!_&&u.parentNode&&u.parentNode.style&&et(u.parentNode).display==="flex"?!1:ie),s.pin=u,xe=p.core.getCache(u),xe.spacer?_t=xe.pinState:(X&&(X=Xe(X),X&&!X.nodeType&&(X=X.current||X.nativeElement),xe.spacerIsNative=!!X,X&&(xe.spacerState=Wr(X))),xe.spacer=de=X||H.createElement("div"),de.classList.add("pin-spacer"),d&&de.classList.add("pin-spacer-"+d),xe.pinState=_t=Wr(u)),t.force3D!==!1&&p.set(u,{force3D:!0}),s.spacer=de=xe.spacer,Jt=et(u),cr=Jt[_+h.os2],Q=p.getProperty(u),rt=p.quickSetter(u,h.a,ce),fn(u,de,Jt),Yt=Wr(u)),j){mt=Wt(j)?Fn(j,Nn):Nn,c=Xr("scroller-start",d,w,h,mt,0),$e=Xr("scroller-end",d,w,h,mt,0,c),Mt=c["offset"+h.op.d2];var ur=Xe(Lt(w,"content")||w);He=this.markerStart=Xr("start",d,ur,h,mt,Mt,0,x),We=this.markerEnd=Xr("end",d,ur,h,mt,Mt,0,x),x&&(jt=p.quickSetter([He,We],h.a,ce)),!Ae&&!(pt.length&&Lt(w,"fixedMarkers")===!0)&&(Ai(se?Y:w),p.set([c,$e],{force3D:!0}),Dr=p.quickSetter(c,h.a,ce),Zt=p.quickSetter($e,h.a,ce))}if(x){var C=x.vars.onUpdate,v=x.vars.onUpdateParams;x.eventCallback("onUpdate",function(){s.update(0,0,1),C&&C.apply(x,v||[])})}if(s.previous=function(){return E[E.indexOf(s)-1]},s.next=function(){return E[E.indexOf(s)+1]},s.revert=function(f,T){if(!T)return s.kill(!0);var m=f!==!1||!s.enabled,k=Ce;m!==s.isReverted&&(m&&(ot=Math.max(te(),s.scroll.rec||0),we=s.progress,Nt=r&&r.progress()),He&&[He,We,c,$e].forEach(function(pe){return pe.style.display=m?"none":"block"}),m&&(Ce=s,s.update(m)),u&&(!Qe||!s.isActive)&&(m?Li(u,de,_t):fn(u,de,et(u),re)),m||s.update(m),Ce=k,s.isReverted=m)},s.refresh=function(f,T,m,k){if(!((Ce||!s.enabled)&&!T)){if(u&&f&&tt){ge(o,"scrollEnd",hi);return}!Ye&&ae&&ae(s),Ce=s,De.tween&&!m&&(De.tween.kill(),De.tween=0),R&&R.pause(),V&&r&&(r.revert({kill:!1}).invalidate(),r.getChildren&&r.getChildren(!0,!0,!1).forEach(function(Pt){return Pt.vars.immediateRender&&Pt.render(0,!0,!0)})),s.isReverted||s.revert(!0,!0),s._subPinOffset=!1;var pe=zt(),O=kt(),Z=x?x.duration():dt(w,h),be=q<=.01||!q,U=0,W=k||0,F=Wt(m)?m.end:t.end,st=t.endTrigger||g,G=Wt(m)?m.start:t.start||(t.start===0||!g?0:u?"0 0":"0 100%"),Ge=s.pinnedContainer=t.pinnedContainer&&Xe(t.pinnedContainer,s),lt=g&&Math.max(0,E.indexOf(s))||0,_e=lt,ve,Te,Ht,Rr,ke,le,ct,on,Dn,fr,ut,dr,Or;for(j&&Wt(m)&&(dr=p.getProperty(c,h.p),Or=p.getProperty($e,h.p));_e-- >0;)le=E[_e],le.end||le.refresh(0,1)||(Ce=s),ct=le.pin,ct&&(ct===g||ct===u||ct===Ge)&&!le.isReverted&&(fr||(fr=[]),fr.unshift(le),le.revert(!0,!0)),le!==E[_e]&&(lt--,_e--);for(Ee(G)&&(G=G(s)),G=In(G,"start",s),L=$n(G,g,pe,h,te(),He,c,s,O,ee,Ae,Z,x,s._startClamp&&"_startClamp")||(u?-.001:0),Ee(F)&&(F=F(s)),qe(F)&&!F.indexOf("+=")&&(~F.indexOf(" ")?F=(qe(G)?G.split(" ")[0]:"")+F:(U=qr(F.substr(2),pe),F=qe(G)?G:(x?p.utils.mapRange(0,x.duration(),x.scrollTrigger.start,x.scrollTrigger.end,L):L)+U,st=g)),F=In(F,"end",s),K=Math.max(L,$n(F||(st?"100% 0":Z),st,pe,h,te()+U,We,$e,s,O,ee,Ae,Z,x,s._endClamp&&"_endClamp"))||-.001,U=0,_e=lt;_e--;)le=E[_e],ct=le.pin,ct&&le.start-le._pinPush<=L&&!x&&le.end>0&&(ve=le.end-(s._startClamp?Math.max(0,le.start):le.start),(ct===g&&le.start-le._pinPush<L||ct===Ge)&&isNaN(G)&&(U+=ve*(1-le.progress)),ct===u&&(W+=ve));if(L+=U,K+=U,s._startClamp&&(s._startClamp+=U),s._endClamp&&!Ye&&(s._endClamp=K||-.001,K=Math.min(K,dt(w,h))),q=K-L||(L-=.01)&&.001,be&&(we=p.utils.clamp(0,1,p.utils.normalize(L,K,ot))),s._pinPush=W,He&&U&&(ve={},ve[h.a]="+="+U,Ge&&(ve[h.p]="-="+te()),p.set([He,We],ve)),u&&!(yn&&s.end>=dt(w,h)))ve=et(u),Rr=h===ue,Ht=te(),Je=parseFloat(Q(h.a))+W,!Z&&K>1&&(ut=(se?H.scrollingElement||Ue:w).style,ut={style:ut,value:ut["overflow"+h.a.toUpperCase()]},se&&et(Y)["overflow"+h.a.toUpperCase()]!=="scroll"&&(ut.style["overflow"+h.a.toUpperCase()]="scroll")),fn(u,de,ve),Yt=Wr(u),Te=xt(u,!0),on=Ae&&It(w,Rr?Be:ue)(),_?(re=[_+h.os2,q+W+ce],re.t=de,_e=_===ie?tn(u,h)+q+W:0,_e&&(re.push(h.d,_e+ce),de.style.flexBasis!=="auto"&&(de.style.flexBasis=_e+ce)),ar(re),Ge&&E.forEach(function(Pt){Pt.pin===Ge&&Pt.vars.pinSpacing!==!1&&(Pt._subPinOffset=!0)}),Ae&&te(ot)):(_e=tn(u,h),_e&&de.style.flexBasis!=="auto"&&(de.style.flexBasis=_e+ce)),Ae&&(ke={top:Te.top+(Rr?Ht-L:on)+ce,left:Te.left+(Rr?on:Ht-L)+ce,boxSizing:"border-box",position:"fixed"},ke[Gt]=ke["max"+lr]=Math.ceil(Te.width)+ce,ke[Vt]=ke["max"+Mn]=Math.ceil(Te.height)+ce,ke[je]=ke[je+Sr]=ke[je+kr]=ke[je+Er]=ke[je+Cr]="0",ke[ie]=ve[ie],ke[ie+Sr]=ve[ie+Sr],ke[ie+kr]=ve[ie+kr],ke[ie+Er]=ve[ie+Er],ke[ie+Cr]=ve[ie+Cr],Et=zi(_t,ke,Qe),Ye&&te(0)),r?(Dn=r._initted,sn(1),r.render(r.duration(),!0,!0),vt=Q(h.a)-Je+q+W,yt=Math.abs(q-vt)>1,Ae&&yt&&Et.splice(Et.length-2,2),r.render(0,!0,!0),Dn||r.invalidate(!0),r.parent||r.totalTime(r.totalTime()),sn(0)):vt=q,ut&&(ut.value?ut.style["overflow"+h.a.toUpperCase()]=ut.value:ut.style.removeProperty("overflow-"+h.a));else if(g&&te()&&!x)for(Te=g.parentNode;Te&&Te!==Y;)Te._pinOffset&&(L-=Te._pinOffset,K-=Te._pinOffset),Te=Te.parentNode;fr&&fr.forEach(function(Pt){return Pt.revert(!1,!0)}),s.start=L,s.end=K,Re=Ne=Ye?ot:te(),!x&&!Ye&&(Re<ot&&te(ot),s.scroll.rec=0),s.revert(!1,!0),gt=Se(),it&&(Ze=-1,it.restart(!0)),Ce=0,r&&fe&&(r._initted||Nt)&&r.progress()!==Nt&&r.progress(Nt||0,!0).render(r.time(),!0,!0),(be||we!==s.progress||x||V||r&&!r._initted)&&(r&&!fe&&(r._initted||we||r.vars.immediateRender!==!1)&&r.totalProgress(x&&L<-.001&&!we?p.utils.normalize(L,K,0):we,!0),s.progress=be||(Re-L)/q===we?0:we),u&&_&&(de._pinOffset=Math.round(s.progress*vt)),R&&R.invalidate(),isNaN(dr)||(dr-=p.getProperty(c,h.p),Or-=p.getProperty($e,h.p),$r(c,h,dr),$r(He,h,dr-(k||0)),$r($e,h,Or),$r(We,h,Or-(k||0))),be&&!Ye&&s.update(),I&&!Ye&&!St&&(St=!0,I(s),St=!1)}},s.getVelocity=function(){return(te()-Ne)/(Se()-gr)*1e3||0},s.endAnimation=function(){hr(s.callbackAnimation),r&&(R?R.progress(1):r.paused()?fe||hr(r,s.direction<0,1):hr(r,r.reversed()))},s.labelToScroll=function(f){return r&&r.labels&&(L||s.refresh()||L)+r.labels[f]/r.duration()*q||0},s.getTrailing=function(f){var T=E.indexOf(s),m=s.direction>0?E.slice(0,T).reverse():E.slice(T+1);return(qe(f)?m.filter(function(k){return k.vars.preventOverlaps===f}):m).filter(function(k){return s.direction>0?k.end<=L:k.start>=K})},s.update=function(f,T,m){if(!(x&&!m&&!f)){var k=Ye===!0?ot:s.scroll(),pe=f?0:(k-L)/q,O=pe<0?0:pe>1?1:pe||0,Z=s.progress,be,U,W,F,st,G,Ge,lt;if(T&&(Ne=Re,Re=x?te():k,S&&(Bt=At,At=r&&!fe?r.totalProgress():O)),N&&u&&!Ce&&!Yr&&tt&&(!O&&L<k+(k-Ne)/(Se()-gr)*N?O=1e-4:O===1&&K>k+(k-Ne)/(Se()-gr)*N&&(O=.9999)),O!==Z&&s.enabled){if(be=s.isActive=!!O&&O<1,U=!!Z&&Z<1,G=be!==U,st=G||!!O!=!!Z,s.direction=O>Z?1:-1,s.progress=O,st&&!Ce&&(W=O&&!Z?0:O===1?1:Z===1?2:3,fe&&(F=!G&&B[W+1]!=="none"&&B[W+1]||B[W],lt=r&&(F==="complete"||F==="reset"||F in r))),Fe&&(G||lt)&&(lt||D||!r)&&(Ee(Fe)?Fe(s):s.getTrailing(Fe).forEach(function(Ht){return Ht.endAnimation()})),fe||(R&&!Ce&&!Yr?(R._dp._time-R._start!==R._time&&R.render(R._dp._time-R._start),R.resetTo?R.resetTo("totalProgress",O,r._tTime/r._tDur):(R.vars.totalProgress=O,R.invalidate().restart())):r&&r.totalProgress(O,!!(Ce&&(gt||f)))),u){if(f&&_&&(de.style[_+h.os2]=cr),!Ae)rt(_r(Je+vt*O));else if(st){if(Ge=!f&&O>Z&&K+1>k&&k+1>=dt(w,h),Qe)if(!f&&(be||Ge)){var _e=xt(u,!0),ve=k-L;Gn(u,Y,_e.top+(h===ue?ve:0)+ce,_e.left+(h===ue?0:ve)+ce)}else Gn(u,de);ar(be||Ge?Et:Yt),yt&&O<1&&be||rt(Je+(O===1&&!Ge?vt:0))}}S&&!De.tween&&!Ce&&!Yr&&it.restart(!0),a&&(G||me&&O&&(O<1||!an))&&Ar(a.targets).forEach(function(Ht){return Ht.classList[be||me?"add":"remove"](a.className)}),l&&!fe&&!f&&l(s),st&&!Ce?(fe&&(lt&&(F==="complete"?r.pause().totalProgress(1):F==="reset"?r.restart(!0).pause():F==="restart"?r.restart(!0):r[F]()),l&&l(s)),(G||!an)&&(y&&G&&cn(s,y),Pe[W]&&cn(s,Pe[W]),me&&(O===1?s.kill(!1,1):Pe[W]=0),G||(W=O===1?1:3,Pe[W]&&cn(s,Pe[W]))),Me&&!be&&Math.abs(s.getVelocity())>(vr(Me)?Me:2500)&&(hr(s.callbackAnimation),R?R.progress(1):hr(r,F==="reverse"?1:!O,1))):fe&&l&&!Ce&&l(s)}if(Zt){var Te=x?k/x.duration()*(x._caScrollDist||0):k;Dr(Te+(c._isFlipped?1:0)),Zt(Te)}jt&&jt(-k/x.duration()*(x._caScrollDist||0))}},s.enable=function(f,T){s.enabled||(s.enabled=!0,ge(w,"resize",yr),se||ge(w,"scroll",tr),ae&&ge(o,"refreshInit",ae),f!==!1&&(s.progress=we=0,Re=Ne=Ze=te()),T!==!1&&s.refresh())},s.getTween=function(f){return f&&De?De.tween:R},s.setPositions=function(f,T,m,k){if(x){var pe=x.scrollTrigger,O=x.duration(),Z=pe.end-pe.start;f=pe.start+Z*f/O,T=pe.start+Z*T/O}s.refresh(!1,!1,{start:zn(f,m&&!!s._startClamp),end:zn(T,m&&!!s._endClamp)},k),s.update()},s.adjustPinSpacing=function(f){if(re&&f){var T=re.indexOf(h.d)+1;re[T]=parseFloat(re[T])+f+ce,re[1]=parseFloat(re[1])+f+ce,ar(re)}},s.disable=function(f,T){if(s.enabled&&(f!==!1&&s.revert(!0,!0),s.enabled=s.isActive=!1,T||R&&R.pause(),ot=0,xe&&(xe.uncache=1),ae&&he(o,"refreshInit",ae),it&&(it.pause(),De.tween&&De.tween.kill()&&(De.tween=0)),!se)){for(var m=E.length;m--;)if(E[m].scroller===w&&E[m]!==s)return;he(w,"resize",yr),se||he(w,"scroll",tr)}},s.kill=function(f,T){s.disable(f,T),R&&!T&&R.kill(),d&&delete wn[d];var m=E.indexOf(s);m>=0&&E.splice(m,1),m===ze&&Kr>0&&ze--,m=0,E.forEach(function(k){return k.scroller===s.scroller&&(m=1)}),m||Ye||(s.scroll.rec=0),r&&(r.scrollTrigger=null,f&&r.revert({kill:!1}),T||r.kill()),He&&[He,We,c,$e].forEach(function(k){return k.parentNode&&k.parentNode.removeChild(k)}),Mr===s&&(Mr=0),u&&(xe&&(xe.uncache=1),m=0,E.forEach(function(k){return k.pin===u&&m++}),m||(xe.spacer=0)),t.onKill&&t.onKill(s)},E.push(s),s.enable(!1,!1),wt&&wt(s),r&&r.add&&!q){var z=s.update;s.update=function(){s.update=z,P.cache++,L||K||s.refresh()},p.delayedCall(.01,s.update),q=.01,L=K=0}else s.refresh();u&&Oi()},o.register=function(t){return rr||(p=t||li(),ai()&&window.document&&o.enable(),rr=mr),rr},o.defaults=function(t){if(t)for(var r in t)Hr[r]=t[r];return Hr},o.disable=function(t,r){mr=0,E.forEach(function(l){return l[r?"kill":"disable"](t)}),he(A,"wheel",tr),he(H,"scroll",tr),clearInterval(zr),he(H,"touchcancel",ft),he(Y,"touchstart",ft),Fr(he,H,"pointerdown,touchstart,mousedown",Yn),Fr(he,H,"pointerup,touchend,mouseup",Bn),jr.kill(),Br(he);for(var i=0;i<P.length;i+=3)Nr(he,P[i],P[i+1]),Nr(he,P[i],P[i+2])},o.enable=function(){if(A=window,H=document,Ue=H.documentElement,Y=H.body,p&&(Ar=p.utils.toArray,Tr=p.utils.clamp,vn=p.core.context||ft,sn=p.core.suppressOverwrites||ft,kn=A.history.scrollRestoration||"auto",xn=A.pageYOffset||0,p.core.globals("ScrollTrigger",o),Y)){mr=1,sr=document.createElement("div"),sr.style.height="100vh",sr.style.position="absolute",_i(),Si(),J.register(p),o.isTouch=J.isTouch,Dt=J.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),_n=J.isTouch===1,ge(A,"wheel",tr),Tn=[A,H,Ue,Y],p.matchMedia?(o.matchMedia=function(y){var I=p.matchMedia(),D;for(D in y)I.add(D,y[D]);return I},p.addEventListener("matchMediaInit",function(){return Pn()}),p.addEventListener("matchMediaRevert",function(){return gi()}),p.addEventListener("matchMedia",function(){$t(0,1),Qt("matchMedia")}),p.matchMedia().add("(orientation: portrait)",function(){return un(),un})):console.warn("Requires GSAP 3.11.0 or later"),un(),ge(H,"scroll",tr);var t=Y.hasAttribute("style"),r=Y.style,i=r.borderTopStyle,l=p.core.Animation.prototype,a,d;for(l.revert||Object.defineProperty(l,"revert",{value:function(){return this.time(-.01,!0)}}),r.borderTopStyle="solid",a=xt(Y),ue.m=Math.round(a.top+ue.sc())||0,Be.m=Math.round(a.left+Be.sc())||0,i?r.borderTopStyle=i:r.removeProperty("border-top-style"),t||(Y.setAttribute("style",""),Y.removeAttribute("style")),zr=setInterval(Hn,250),p.delayedCall(.5,function(){return Yr=0}),ge(H,"touchcancel",ft),ge(Y,"touchstart",ft),Fr(ge,H,"pointerdown,touchstart,mousedown",Yn),Fr(ge,H,"pointerup,touchend,mouseup",Bn),mn=p.utils.checkPrefix("transform"),Qr.push(mn),rr=Se(),jr=p.delayedCall(.2,$t).pause(),nr=[H,"visibilitychange",function(){var y=A.innerWidth,I=A.innerHeight;H.hidden?(On=y,Ln=I):(On!==y||Ln!==I)&&yr()},H,"DOMContentLoaded",$t,A,"load",$t,A,"resize",yr],Br(ge),E.forEach(function(y){return y.enable(0,1)}),d=0;d<P.length;d+=3)Nr(he,P[d],P[d+1]),Nr(he,P[d],P[d+2])}},o.config=function(t){"limitCallbacks"in t&&(an=!!t.limitCallbacks);var r=t.syncInterval;r&&clearInterval(zr)||(zr=r)&&setInterval(Hn,r),"ignoreMobileResize"in t&&(_n=o.isTouch===1&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(Br(he)||Br(ge,t.autoRefreshEvents||"none"),ii=(t.autoRefreshEvents+"").indexOf("resize")===-1)},o.scrollerProxy=function(t,r){var i=Xe(t),l=P.indexOf(i),a=Ut(i);~l&&P.splice(l,a?6:2),r&&(a?pt.unshift(A,r,Y,r,Ue,r):pt.unshift(i,r))},o.clearMatchMedia=function(t){E.forEach(function(r){return r._ctx&&r._ctx.query===t&&r._ctx.kill(!0,!0)})},o.isInViewport=function(t,r,i){var l=(qe(t)?Xe(t):t).getBoundingClientRect(),a=l[i?Gt:Vt]*r||0;return i?l.right-a>0&&l.left+a<A.innerWidth:l.bottom-a>0&&l.top+a<A.innerHeight},o.positionInViewport=function(t,r,i){qe(t)&&(t=Xe(t));var l=t.getBoundingClientRect(),a=l[i?Gt:Vt],d=r==null?a/2:r in rn?rn[r]*a:~r.indexOf("%")?parseFloat(r)*a/100:parseFloat(r)||0;return i?(l.left+d)/A.innerWidth:(l.top+d)/A.innerHeight},o.killAll=function(t){if(E.slice(0).forEach(function(i){return i.vars.id!=="ScrollSmoother"&&i.kill()}),t!==!0){var r=Kt.killAll||[];Kt={},r.forEach(function(i){return i()})}},o}();M.version="3.13.0";M.saveStyles=function(o){return o?Ar(o).forEach(function(e){if(e&&e.style){var n=Ve.indexOf(e);n>=0&&Ve.splice(n,5),Ve.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),p.core.getCache(e),vn())}}):Ve};M.revert=function(o,e){return Pn(!o,e)};M.create=function(o,e){return new M(o,e)};M.refresh=function(o){return o?yr(!0):(rr||M.register())&&$t(!0)};M.update=function(o){return++P.cache&&Tt(o===!0?2:0)};M.clearScrollMemory=mi;M.maxScroll=function(o,e){return dt(o,e?Be:ue)};M.getScrollFunc=function(o,e){return It(Xe(o),e?Be:ue)};M.getById=function(o){return wn[o]};M.getAll=function(){return E.filter(function(o){return o.vars.id!=="ScrollSmoother"})};M.isScrolling=function(){return!!tt};M.snapDirectional=An;M.addEventListener=function(o,e){var n=Kt[o]||(Kt[o]=[]);~n.indexOf(e)||n.push(e)};M.removeEventListener=function(o,e){var n=Kt[o],t=n&&n.indexOf(e);t>=0&&n.splice(t,1)};M.batch=function(o,e){var n=[],t={},r=e.interval||.016,i=e.batchMax||1e9,l=function(y,I){var D=[],g=[],u=p.delayedCall(r,function(){I(D,g),D=[],g=[]}).pause();return function(_){D.length||u.restart(!0),D.push(_.trigger),g.push(_),i<=D.length&&u.progress(1)}},a;for(a in e)t[a]=a.substr(0,2)==="on"&&Ee(e[a])&&a!=="onRefreshInit"?l(a,e[a]):e[a];return Ee(i)&&(i=i(),ge(M,"refresh",function(){return i=e.batchMax()})),Ar(o).forEach(function(d){var y={};for(a in t)y[a]=t[a];y.trigger=d,n.push(M.create(y))}),n};var qn=function(e,n,t,r){return n>r?e(r):n<0&&e(0),t>r?(r-n)/(t-n):t<0?n/(n-t):1},dn=function o(e,n){n===!0?e.style.removeProperty("touch-action"):e.style.touchAction=n===!0?"auto":n?"pan-"+n+(J.isTouch?" pinch-zoom":""):"none",e===Ue&&o(Y,n)},Gr={auto:1,scroll:1},Bi=function(e){var n=e.event,t=e.target,r=e.axis,i=(n.changedTouches?n.changedTouches[0]:n).target,l=i._gsap||p.core.getCache(i),a=Se(),d;if(!l._isScrollT||a-l._isScrollT>2e3){for(;i&&i!==Y&&(i.scrollHeight<=i.clientHeight&&i.scrollWidth<=i.clientWidth||!(Gr[(d=et(i)).overflowY]||Gr[d.overflowX]));)i=i.parentNode;l._isScroll=i&&i!==t&&!Ut(i)&&(Gr[(d=et(i)).overflowY]||Gr[d.overflowX]),l._isScrollT=a}(l._isScroll||r==="x")&&(n.stopPropagation(),n._gsapAllow=!0)},yi=function(e,n,t,r){return J.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:n,onWheel:r=r&&Bi,onPress:r,onDrag:r,onScroll:r,onEnable:function(){return t&&ge(H,J.eventTypes[0],Kn,!1,!0)},onDisable:function(){return he(H,J.eventTypes[0],Kn,!0)}})},Fi=/(input|label|select|textarea)/i,Un,Kn=function(e){var n=Fi.test(e.target.tagName);(n||Un)&&(e._gsapAllow=!0,Un=n)},Ni=function(e){Wt(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var n=e,t=n.normalizeScrollX,r=n.momentum,i=n.allowNestedScroll,l=n.onRelease,a,d,y=Xe(e.target)||Ue,I=p.core.globals().ScrollSmoother,D=I&&I.get(),g=Dt&&(e.content&&Xe(e.content)||D&&e.content!==!1&&!D.smooth()&&D.content()),u=It(y,ue),_=It(y,Be),V=1,N=(J.isTouch&&A.visualViewport?A.visualViewport.scale*A.visualViewport.width:A.outerWidth)/A.innerWidth,oe=0,$=Ee(r)?function(){return r(a)}:function(){return r||2.8},me,S,Qe=yi(y,e.type,!0,i),X=function(){return S=!1},x=ft,Me=ft,Fe=function(){d=dt(y,ue),Me=Tr(Dt?1:0,d),t&&(x=Tr(0,dt(y,Be))),me=qt},h=function(){g._gsap.y=_r(parseFloat(g._gsap.y)+u.offset)+"px",g.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(g._gsap.y)+", 0, 1)",u.offset=u.cacheID=0},fe=function(){if(S){requestAnimationFrame(X);var j=_r(a.deltaY/2),ee=Me(u.v-j);if(g&&ee!==u.v+u.offset){u.offset=ee-u.v;var s=_r((parseFloat(g&&g._gsap.y)||0)-u.offset);g.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+s+", 0, 1)",g._gsap.y=s+"px",u.cacheID=P.cache,Tt()}return!0}u.offset&&h(),S=!0},w,ht,se,Ae,Pe=function(){Fe(),w.isActive()&&w.vars.scrollY>d&&(u()>d?w.progress(1)&&u(d):w.resetTo("scrollY",d))};return g&&p.set(g,{y:"+=0"}),e.ignoreCheck=function(B){return Dt&&B.type==="touchmove"&&fe()||V>1.05&&B.type!=="touchstart"||a.isGesturing||B.touches&&B.touches.length>1},e.onPress=function(){S=!1;var B=V;V=_r((A.visualViewport&&A.visualViewport.scale||1)/N),w.pause(),B!==V&&dn(y,V>1.01?!0:t?!1:"x"),ht=_(),se=u(),Fe(),me=qt},e.onRelease=e.onGestureStart=function(B,j){if(u.offset&&h(),!j)Ae.restart(!0);else{P.cache++;var ee=$(),s,ae;t&&(s=_(),ae=s+ee*.05*-B.velocityX/.227,ee*=qn(_,s,ae,dt(y,Be)),w.vars.scrollX=x(ae)),s=u(),ae=s+ee*.05*-B.velocityY/.227,ee*=qn(u,s,ae,dt(y,ue)),w.vars.scrollY=Me(ae),w.invalidate().duration(ee).play(.01),(Dt&&w.vars.scrollY>=d||s>=d-1)&&p.to({},{onUpdate:Pe,duration:ee})}l&&l(B)},e.onWheel=function(){w._ts&&w.pause(),Se()-oe>1e3&&(me=0,oe=Se())},e.onChange=function(B,j,ee,s,ae){if(qt!==me&&Fe(),j&&t&&_(x(s[2]===j?ht+(B.startX-B.x):_()+j-s[1])),ee){u.offset&&h();var zt=ae[2]===ee,kt=zt?se+B.startY-B.y:u()+ee-ae[1],Ze=Me(kt);zt&&kt!==Ze&&(se+=Ze-kt),u(Ze)}(ee||j)&&Tt()},e.onEnable=function(){dn(y,t?!1:"x"),M.addEventListener("refresh",Pe),ge(A,"resize",Pe),u.smooth&&(u.target.style.scrollBehavior="auto",u.smooth=_.smooth=!1),Qe.enable()},e.onDisable=function(){dn(y,!0),he(A,"resize",Pe),M.removeEventListener("refresh",Pe),Qe.kill()},e.lockAxis=e.lockAxis!==!1,a=new J(e),a.iOS=Dt,Dt&&!u()&&u(1),Dt&&p.ticker.add(ft),Ae=a._dc,w=p.to(a,{ease:"power4",paused:!0,inherit:!1,scrollX:t?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:vi(u,u(),function(){return w.pause()})},onUpdate:Tt,onComplete:Ae.vars.onComplete}),a};M.sort=function(o){if(Ee(o))return E.sort(o);var e=A.pageYOffset||0;return M.getAll().forEach(function(n){return n._sortY=n.trigger?e+n.trigger.getBoundingClientRect().top:n.start+A.innerHeight}),E.sort(o||function(n,t){return(n.vars.refreshPriority||0)*-1e6+(n.vars.containerAnimation?1e6:n._sortY)-((t.vars.containerAnimation?1e6:t._sortY)+(t.vars.refreshPriority||0)*-1e6)})};M.observe=function(o){return new J(o)};M.normalizeScroll=function(o){if(typeof o>"u")return Ie;if(o===!0&&Ie)return Ie.enable();if(o===!1){Ie&&Ie.kill(),Ie=o;return}var e=o instanceof J?o:Ni(o);return Ie&&Ie.target===e.target&&Ie.kill(),Ut(e.target)&&(Ie=e),e};M.core={_getVelocityProp:gn,_inputObserver:yi,_scrollers:P,_proxies:pt,bridge:{ss:function(){tt||Qt("scrollStart"),tt=Se()},ref:function(){return Ce}}};li()&&p.registerPlugin(M);ne.registerPlugin(M);class Wi{constructor(){this.isInitialized=!1,this.animations=new Map}init(){this.isInitialized||(this.setupGSAP(),this.setupPageTransitions(),this.setupScrollAnimations(),this.setupInteractiveElements(),this.isInitialized=!0)}setupGSAP(){ne.defaults({duration:b.animations.defaultDuration,ease:b.animations.defaultEase}),ne.from(b.selectors.pageTransition,{duration:b.animations.pageTransition.duration,y:30,opacity:0,ease:b.animations.pageTransition.ease}),ne.from("nav",{duration:b.animations.navigation.duration,y:-100,opacity:0,ease:b.animations.navigation.ease,delay:b.animations.navigation.delay})}setupPageTransitions(){document.addEventListener("click",e=>{const n=e.target.closest("a[href]");n&&n.hostname===window.location.hostname&&!n.hasAttribute("target")&&(e.preventDefault(),ne.to(b.selectors.pageTransition,{duration:.3,opacity:0,y:-20,ease:"power2.in",onComplete:()=>{window.location.href=n.href}}))})}setupScrollAnimations(){ne.utils.toArray(b.selectors.animateOnScroll).forEach(e=>{const n=ne.fromTo(e,{opacity:0,y:50},{opacity:1,y:0,duration:b.animations.scroll.duration,ease:b.animations.scroll.ease,scrollTrigger:{trigger:e,start:b.animations.scroll.trigger.start,end:b.animations.scroll.trigger.end,toggleActions:b.animations.scroll.trigger.toggleActions}});this.animations.set(e,n)}),ne.utils.toArray(b.selectors.parallax).forEach(e=>{const n=ne.to(e,{yPercent:-50,ease:"none",scrollTrigger:{trigger:e,start:"top bottom",end:"bottom top",scrub:!0}});this.animations.set(e,n)}),ne.utils.toArray(b.selectors.staggerChildren).forEach(e=>{const n=e.children,t=ne.fromTo(n,{opacity:0,y:30},{opacity:1,y:0,duration:b.animations.stagger.duration,stagger:b.animations.stagger.stagger,ease:b.animations.stagger.ease,scrollTrigger:{trigger:e,start:"top 80%"}});this.animations.set(e,t)})}setupInteractiveElements(){document.querySelectorAll(b.selectors.buttons).forEach(e=>{e.addEventListener("mouseenter",()=>{ne.to(e,{duration:b.animations.hover.duration,scale:1.05,ease:b.animations.hover.ease})}),e.addEventListener("mouseleave",()=>{ne.to(e,{duration:b.animations.hover.duration,scale:1,ease:b.animations.hover.ease})})}),document.querySelectorAll(b.selectors.cards).forEach(e=>{e.addEventListener("mouseenter",()=>{ne.to(e,{duration:b.animations.hover.duration,y:-8,boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25)",ease:b.animations.hover.ease})}),e.addEventListener("mouseleave",()=>{ne.to(e,{duration:b.animations.hover.duration,y:0,boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",ease:b.animations.hover.ease})})}),document.querySelectorAll(b.selectors.navLinks).forEach(e=>{e.addEventListener("mouseenter",()=>{ne.to(e,{duration:b.animations.hover.duration,scale:1.05,ease:b.animations.hover.ease})}),e.addEventListener("mouseleave",()=>{ne.to(e,{duration:b.animations.hover.duration,scale:1,ease:b.animations.hover.ease})})})}createAnimation(e,n,t){const r=ne.fromTo(e,n,t);return this.animations.set(e,r),r}playAnimation(e){const n=this.animations.get(e);n&&n.play()}pauseAnimation(e){const n=this.animations.get(e);n&&n.pause()}killAnimation(e){const n=this.animations.get(e);n&&(n.kill(),this.animations.delete(e))}refreshScrollTrigger(){M.refresh()}killAll(){this.animations.forEach(e=>e.kill()),this.animations.clear(),M.killAll()}destroy(){this.killAll(),this.isInitialized=!1}}class $i{constructor(){this.notifications=new Map,this.container=null,this.maxVisible=b.notifications.maxVisible,this.init()}init(){this.createContainer(),this.setupGlobalFunction()}createContainer(){this.container=document.createElement("div"),this.container.id="notification-container",this.container.className="fixed top-4 right-4 z-50 space-y-2",document.body.appendChild(this.container)}setupGlobalFunction(){window.showNotification=(e,n="info",t=b.notifications.defaultDuration)=>this.show(e,n,t)}show(e,n="info",t=b.notifications.defaultDuration,r={}){const i=wi("notification"),l=this.createElement(i,e,n,r);return this.container.appendChild(l),this.notifications.set(i,{element:l,type:n,message:e,duration:t,timestamp:Date.now()}),this.animateIn(l),t>0&&setTimeout(()=>{this.remove(i)},t),this.limitVisible(),i}createElement(e,n,t,r={}){const i=document.createElement("div");i.id=e,i.className=`notification notification-${t} max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`;const l=this.getIcon(t),a=r.title||this.getDefaultTitle(t);return i.innerHTML=`
      <div class="p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            ${l}
          </div>
          <div class="ml-3 flex-1">
            ${a?`<p class="text-sm font-medium text-gray-900 dark:text-gray-100">${a}</p>`:""}
            <p class="text-sm text-gray-500 dark:text-gray-400 ${a?"mt-1":""}">
              ${n}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button class="notification-close inline-flex text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 rounded-md">
              <span class="sr-only">Close</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
        ${r.actions?this.createActions(r.actions):""}
      </div>
    `,i.querySelector(".notification-close").addEventListener("click",()=>{this.remove(e)}),i}createActions(e){return`<div class="mt-3">${e.map(t=>`<button class="notification-action text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 transition-colors mr-4" data-action="${t.id}">
        ${t.label}
      </button>`).join("")}</div>`}getIcon(e){const n={success:'<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',error:'<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',warning:'<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',info:'<svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'};return n[e]||n.info}getDefaultTitle(e){return{success:"Success",error:"Error",warning:"Warning",info:"Information"}[e]||""}animateIn(e){ne.fromTo(e,b.notifications.animations.enter,b.notifications.animations.enterActive)}animateOut(e){return new Promise(n=>{ne.to(e,{...b.notifications.animations.exit,onComplete:n})})}async remove(e){const n=this.notifications.get(e);n&&(await this.animateOut(n.element),n.element.remove(),this.notifications.delete(e))}limitVisible(){const e=Array.from(this.notifications.entries());e.length>this.maxVisible&&e.sort((t,r)=>t[1].timestamp-r[1].timestamp).slice(0,e.length-this.maxVisible).forEach(([t])=>this.remove(t))}clearAll(){Array.from(this.notifications.keys()).forEach(n=>this.remove(n))}getCount(e=null){return e?Array.from(this.notifications.values()).filter(n=>n.type===e).length:this.notifications.size}destroy(){this.clearAll(),this.container&&this.container.remove(),delete window.showNotification}}class Gi{constructor(){this.currentTheme=null,this.toggleButton=null,this.observers=[],this.init()}init(){this.loadTheme(),this.setupToggleButton(),this.setupSystemThemeListener(),this.setupAlpineIntegration()}loadTheme(){const e=localStorage.getItem(b.theme.storageKey),n=window.matchMedia("(prefers-color-scheme: dark)").matches;e!==null?this.currentTheme=e==="true"?"dark":"light":this.currentTheme=n?"dark":"light",this.applyTheme(this.currentTheme),xi("debug",`Theme loaded: ${this.currentTheme}`)}applyTheme(e){const n=document.documentElement,t=document.body;e==="dark"?(n.classList.add("dark"),t.classList.add("dark")):(n.classList.remove("dark"),t.classList.remove("dark")),this.updateMetaThemeColor(e),this.notifyObservers(e),localStorage.setItem(b.theme.storageKey,e==="dark"),this.currentTheme=e}updateMetaThemeColor(e){let n=document.querySelector('meta[name="theme-color"]');n||(n=document.createElement("meta"),n.name="theme-color",document.head.appendChild(n));const t={light:"#ffffff",dark:"#1f2937"};n.content=t[e]||t.light}setupToggleButton(){this.toggleButton=document.querySelector("[data-theme-toggle]")}setupSystemThemeListener(){window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",n=>{if(!(localStorage.getItem(b.theme.storageKey)!==null)){const r=n.matches?"dark":"light";this.applyTheme(r)}})}setupAlpineIntegration(){window.Alpine&&window.Alpine.store("theme",{current:this.currentTheme,isDark:this.currentTheme==="dark",toggle:()=>this.toggle(),set:e=>this.setTheme(e)})}toggle(){const e=this.currentTheme==="dark"?"light":"dark";this.setTheme(e)}setTheme(e){var n;if(e!=="light"&&e!=="dark"&&(e="light"),this.applyTheme(e),(n=window.Alpine)!=null&&n.store){const t=window.Alpine.store("theme");t&&(t.current=e,t.isDark=e==="dark")}}getTheme(){return this.currentTheme}isDark(){return this.currentTheme==="dark"}addObserver(e){this.observers.push(e)}removeObserver(e){const n=this.observers.indexOf(e);n>-1&&this.observers.splice(n,1)}notifyObservers(e){this.observers.forEach(n=>{try{n(e)}catch{}})}getSystemTheme(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}resetToSystem(){localStorage.removeItem(b.theme.storageKey);const e=this.getSystemTheme();this.setTheme(e)}destroy(){this.observers=[]}}export{Wi as A,$i as N,Gi as T};
