import{d as h,a as l,b as p}from"./core-lNA10VOR.js";class w{constructor(){this.forms=new Map,this.validators=new Map,this.init()}init(){this.setupFormHandlers(),this.setupValidators(),this.setupEnhancements()}setupFormHandlers(){document.addEventListener("submit",e=>{const t=e.target.closest("form");t&&t.hasAttribute("data-ajax")&&(e.preventDefault(),this.handleAjaxSubmit(t))}),document.addEventListener("input",h(e=>{const t=e.target;t.form&&t.hasAttribute("data-validate")&&this.validateField(t)},300)),document.addEventListener("blur",e=>{const t=e.target;t.form&&t.hasAttribute("data-validate")&&this.validateField(t)},!0)}setupValidators(){this.addValidator("required",(e,t)=>{const a=e.trim()!=="";return{isValid:a,message:a?"":"This field is required"}}),this.addValidator("email",(e,t)=>{const s=!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e);return{isValid:s,message:s?"":"Please enter a valid email address"}}),this.addValidator("minlength",(e,t)=>{const a=parseInt(t.getAttribute("data-minlength")||"0"),s=!e||e.length>=a;return{isValid:s,message:s?"":`Minimum ${a} characters required`}}),this.addValidator("maxlength",(e,t)=>{const a=parseInt(t.getAttribute("data-maxlength")||"999999"),s=!e||e.length<=a;return{isValid:s,message:s?"":`Maximum ${a} characters allowed`}}),this.addValidator("pattern",(e,t)=>{const a=t.getAttribute("data-pattern");if(!a||!e)return{isValid:!0,message:""};const r=new RegExp(a).test(e);return{isValid:r,message:r?"":"Please match the required format"}}),this.addValidator("password-strength",(e,t)=>{if(!e)return{isValid:!0,message:""};const a=8,s=/[A-Z]/.test(e),r=/[a-z]/.test(e),i=/\d/.test(e),n=e.length>=a&&s&&r&&i;let o="";if(!n){const d=[];e.length<a&&d.push(`${a} characters`),s||d.push("uppercase letter"),r||d.push("lowercase letter"),i||d.push("number"),o=`Password must contain: ${d.join(", ")}`}return{isValid:n,message:o}}),this.addValidator("confirm-password",(e,t)=>{const a=t.form.querySelector('[type="password"]:not([data-validate*="confirm-password"])');if(!a||!e)return{isValid:!0,message:""};const s=e===a.value;return{isValid:s,message:s?"":"Passwords do not match"}})}setupEnhancements(){document.querySelectorAll("textarea[data-auto-resize]").forEach(e=>{this.setupAutoResize(e)}),document.querySelectorAll("[data-char-counter]").forEach(e=>{this.setupCharCounter(e)}),document.querySelectorAll('input[type="file"][data-preview]').forEach(e=>{this.setupFilePreview(e)})}async handleAjaxSubmit(e){const t=new FormData(e),a=e.querySelector('[type="submit"]'),s=a==null?void 0:a.textContent;try{if(!this.validateForm(e))return;a&&(a.disabled=!0,a.innerHTML=`
          <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Submitting...
        `);const r=e.getAttribute("data-content-type")||"json";let i=t;if(r==="json"){const c={};t.forEach((m,u)=>{c[u]=m}),i=c}const n=e.method.toLowerCase(),o=e.action.replace(window.location.origin,"");let d;switch(n){case"post":d=r==="json"?await l.post(o,i):await l.upload(o,i);break;case"put":d=await l.put(o,i);break;case"delete":d=await l.delete(o);break;default:d=await l.get(o,i)}this.handleSubmitSuccess(e,d)}catch(r){this.handleSubmitError(e,r)}finally{a&&s&&(a.disabled=!1,a.textContent=s)}}handleSubmitSuccess(e,t){window.showNotification&&window.showNotification(t.message||"Form submitted successfully!","success"),e.hasAttribute("data-reset-on-success")&&(e.reset(),this.clearValidation(e));const a=e.getAttribute("data-redirect")||t.redirect;a&&setTimeout(()=>{window.location.href=a},1500),e.dispatchEvent(new CustomEvent("form:success",{detail:{response:t}}))}handleSubmitError(e,t){t.message.includes("422")&&t.errors?this.showValidationErrors(e,t.errors):p.handleError(t,window.showNotification),e.dispatchEvent(new CustomEvent("form:error",{detail:{error:t}}))}validateForm(e){const t=e.querySelectorAll("[data-validate]");let a=!0;return t.forEach(s=>{this.validateField(s)||(a=!1)}),a}validateField(e){const t=e.getAttribute("data-validate").split(" "),a=e.value;let s=!0,r="";for(const i of t){const n=this.validators.get(i);if(n){const o=n(a,e);if(!o.isValid){s=!1,r=o.message;break}}}return this.showFieldValidation(e,s,r),s}showFieldValidation(e,t,a){const s=e.closest(".form-group")||e.parentElement;let r=s.querySelector(".form-error-message");e.classList.remove("form-input-error","form-input-success"),t?(e.classList.add("form-input-success"),r&&r.remove()):(e.classList.add("form-input-error"),r||(r=document.createElement("div"),r.className="form-error-message mt-1 text-sm text-red-600 dark:text-red-400",s.appendChild(r)),r.textContent=a)}clearValidation(e){e.querySelectorAll("[data-validate]").forEach(a=>{a.classList.remove("form-input-error","form-input-success");const r=(a.closest(".form-group")||a.parentElement).querySelector(".form-error-message");r&&r.remove()})}setupAutoResize(e){const t=()=>{e.style.height="auto",e.style.height=e.scrollHeight+"px"};e.addEventListener("input",t),t()}setupCharCounter(e){const t=e.maxLength||parseInt(e.getAttribute("data-max-length"));if(!t)return;const a=document.createElement("div");a.className="form-help-text text-right";const s=()=>{const r=t-e.value.length;a.textContent=`${r} characters remaining`,r<10?a.classList.add("text-red-500"):a.classList.remove("text-red-500")};e.addEventListener("input",s),e.parentElement.appendChild(a),s()}setupFilePreview(e){const t=document.createElement("div");t.className="mt-2 grid grid-cols-3 gap-2",e.parentElement.appendChild(t),e.addEventListener("change",a=>{t.innerHTML="",Array.from(a.target.files).forEach(s=>{if(s.type.startsWith("image/")){const r=new FileReader;r.onload=i=>{const n=document.createElement("img");n.src=i.target.result,n.className="w-full h-20 object-cover rounded-lg",t.appendChild(n)},r.readAsDataURL(s)}})})}addValidator(e,t){this.validators.set(e,t)}removeValidator(e){this.validators.delete(e)}destroy(){this.forms.clear(),this.validators.clear()}}export{w as F};
