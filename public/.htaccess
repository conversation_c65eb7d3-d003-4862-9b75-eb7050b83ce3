# Security Headers for Production
# Note: For PHP development server, these headers should be set via middleware

<IfModule mod_headers.c>
    # Security Headers

    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Prevent clickjacking attacks
    Header always set X-Frame-Options "SAMEORIGIN"

    # Referrer Policy - control referrer information
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Content Security Policy (CSP) - adjust as needed
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'self';"

    # HTTP Strict Transport Security (HSTS) - only for HTTPS
    # Uncomment when using HTTPS in production
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    # Permissions Policy (formerly Feature Policy)
    Header always set Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"

    # Remove server information
    Header always unset Server
    Header always unset X-Powered-By
</IfModule>

# Hide sensitive files and directories
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# Protect configuration files
<FilesMatch "\.(ini|conf|config|log|sql|bak|backup|old|tmp)$">
    Require all denied
</FilesMatch>

# Protect specific directories
<IfModule mod_alias.c>
    RedirectMatch 403 ^/config/
    RedirectMatch 403 ^/logs/
    RedirectMatch 403 ^/var/
    RedirectMatch 403 ^/vendor/
    RedirectMatch 403 ^/src/
    RedirectMatch 403 ^/tests/
    RedirectMatch 403 ^/docs/
</IfModule>

# URL Rewriting for Slim Framework
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Redirect to HTTPS (uncomment for production with SSL)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Handle Slim routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^ index.php [QSA,L]
</IfModule>

# Disable directory browsing
Options -Indexes

# File upload security
<IfModule mod_php.c>
    # Hide PHP version
    php_flag expose_php Off

    # Session security
    php_value session.cookie_httponly 1
    php_value session.use_strict_mode 1

    # File upload limits
    php_value upload_max_filesize "10M"
    php_value post_max_size "10M"
    php_value max_execution_time "30"
    php_value memory_limit "128M"
</IfModule>