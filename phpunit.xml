<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" colors="true" backupGlobals="false"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.1/phpunit.xsd" cacheDirectory=".phpunit.cache"
         backupStaticProperties="false">
    <coverage/>
    <testsuites>
        <testsuite name="Tests">
            <directory suffix="Test.php">tests/TestCase</directory>
        </testsuite>
    </testsuites>
    <php>
        <!--    APP_ENV has to be "test" for env.test.php (test config values) to load  -->
        <env name="APP_ENV" value="test"/>
        <env name="PHPUNIT_TEST_SUITE" value="1"/>
    </php>
    <source>
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <exclude>
            <directory>bin</directory>
            <directory>build</directory>
            <directory>docs</directory>
            <directory>public</directory>
            <directory>tmp</directory>
            <directory>vendor</directory>
        </exclude>
    </source>
</phpunit>
