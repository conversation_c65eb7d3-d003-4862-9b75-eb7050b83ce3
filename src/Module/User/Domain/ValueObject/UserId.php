<?php

declare(strict_types = 1);

namespace App\Module\User\Domain\ValueObject;

use Ramsey\Uuid\Uuid;

class UserId
{
    private function __construct(
        private readonly string $value,
    ) {
        if (!Uuid::isValid($this->value)) {
            throw new \InvalidArgumentException('Invalid UserId format');
        }
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    public static function generate(): self
    {
        return new self(Uuid::uuid4()->toString());
    }

    public function toRfc4122(): string
    {
        return $this->value;
    }

    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
