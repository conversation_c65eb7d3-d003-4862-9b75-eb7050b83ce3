<?php

declare(strict_types = 1);

namespace App\Module\Article\Domain\Enum;

enum ArticleType: string
{
    case BLOG_POST = 'blog_post';
    case NEWS_ARTICLE = 'news_article';
    case TUTORIAL = 'tutorial';
    case REVIEW = 'review';

    public function getLabel(): string
    {
        return match($this) {
            self::BLOG_POST => 'Blog Post',
            self::NEWS_ARTICLE => 'News Article',
            self::TUTORIAL => 'Tutorial',
            self::REVIEW => 'Review',
        };
    }
}
