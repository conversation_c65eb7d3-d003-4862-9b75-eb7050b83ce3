<?php

declare(strict_types = 1);

namespace App\Module\Mark\Application\Query;

use App\Module\User\Domain\ValueObject\UserId;

final class GetMarksQuery
{
    public function __construct(
        public readonly ?UserId $userId = null,
        public readonly bool $onlyPublic = false,
        public readonly int $limit = 10,
        public readonly int $offset = 0,
    ) {
        if ($this->userId === null && !$this->onlyPublic) {
            throw new \InvalidArgumentException('Either userId must be provided or onlyPublic must be true');
        }
    }
}
