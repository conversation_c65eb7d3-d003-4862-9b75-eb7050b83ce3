<?php

declare(strict_types = 1);

namespace App\Module\Mark\Infrastructure\Http\Controller;

use App\Module\Mark\Application\Command\CreateMarkCommand;
use App\Module\Mark\Application\Query\GetMarksQuery;
use App\Module\User\Domain\ValueObject\UserId;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

class MarkController
{
    public function __construct(
        private readonly \Psr\Container\ContainerInterface $container,
    ) {
    }

    public function create(Request $request, Response $response, array $args): Response
    {
        $data = $request->getParsedBody();
        $userId = new UserId($data['user_id'] ?? ''); // In real app, get from auth

        $command = new CreateMarkCommand(
            $userId,
            $data['title'] ?? '',
            $data['content'] ?? '',
            (bool)($data['is_public'] ?? false)
        );

        $handler = $this->container->get(CreateMarkHandler::class);
        $handler($command);

        $response->getBody()->write(json_encode(['status' => 'success']));

        return $response
            ->withHeader('Content-Type', 'application/json')
            ->withStatus(201);
    }

    public function list(Request $request, Response $response, array $args): Response
    {
        $queryParams = $request->getQueryParams();
        $userId = isset($queryParams['user_id'])
            ? new UserId($queryParams['user_id'])
            : null;

        $query = new GetMarksQuery(
            $userId,
            (bool)($queryParams['only_public'] ?? false),
            (int)($queryParams['limit'] ?? 10),
            (int)($queryParams['offset'] ?? 0)
        );

        $handler = $this->container->get(GetMarksHandler::class);
        $marks = $handler($query);

        $response->getBody()->write(json_encode([
            'status' => 'success',
            'data' => array_map(fn ($mark) => $this->markToArray($mark), $marks),
        ]));

        return $response
            ->withHeader('Content-Type', 'application/json');
    }

    private function markToArray($mark): array
    {
        return [
            'id' => (string)$mark->getId(),
            'user_id' => (string)$mark->getUserId(),
            'title' => $mark->getTitle(),
            'content' => $mark->getContent(),
            'is_public' => $mark->isPublic(),
            'created_at' => $mark->getCreatedAt()->format('c'),
            'updated_at' => $mark->getUpdatedAt()?->format('c'),
        ];
    }
}
