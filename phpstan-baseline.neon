parameters:
	ignoreErrors:
		-
			message: "#^<PERSON>rray has 2 duplicate keys with value 'ResponsiveSk\\\\\\\\Slim4Paths\\\\\\\\Paths' \\(\\\\ResponsiveSk\\\\Slim4Paths\\\\Paths\\:\\:class, \\\\ResponsiveSk\\\\Slim4Paths\\\\Paths\\:\\:class\\)\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Array has 2 duplicate keys with value 'public' \\('public', 'public'\\)\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Call to an undefined method ResponsiveSk\\\\Slim4Paths\\\\Paths\\:\\:data\\(\\)\\.$#"
			count: 2
			path: config/container.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 4
			path: config/container.php

		-
			message: "#^Cannot access offset 'display_error…' on mixed\\.$#"
			count: 2
			path: config/container.php

		-
			message: "#^Cannot access offset 'error' on mixed\\.$#"
			count: 3
			path: config/container.php

		-
			message: "#^Cannot access offset 'level' on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Cannot access offset 'log_errors' on mixed\\.$#"
			count: 2
			path: config/container.php

		-
			message: "#^Cannot access offset 'logger' on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Cannot access offset 'main_contact_email' on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Cannot access offset 'path' on mixed\\.$#"
			count: 2
			path: config/container.php

		-
			message: "#^Cannot access offset 'public' on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Cannot access offset 'renderer' on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 2
			path: config/container.php

		-
			message: "#^Cannot access offset 'test' on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Cannot call method data\\(\\) on mixed\\.$#"
			count: 3
			path: config/container.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Cannot call method getRouteCollector\\(\\) on mixed\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$app of class Selective\\\\BasePath\\\\BasePathMiddleware constructor expects Slim\\\\App, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$basePath of class ResponsiveSk\\\\Slim4Paths\\\\Paths constructor expects string, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$config of class Cake\\\\Database\\\\Connection constructor expects array\\<string, mixed\\>, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$displayErrorDetails of class SlimErrorRenderer\\\\Middleware\\\\NonFatalErrorHandlingMiddleware constructor expects bool, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$pdo of class TestTraits\\\\Console\\\\SqlSchemaGenerator constructor expects PDO, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$responseFactory of class SlimErrorRenderer\\\\Middleware\\\\ExceptionHandlingMiddleware constructor expects Psr\\\\Http\\\\Message\\\\ResponseFactoryInterface, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$settings of class App\\\\Infrastructure\\\\Utility\\\\Settings constructor expects array\\<string, mixed\\>, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\$templatePath of class Slim\\\\Views\\\\PhpRenderer constructor expects string, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#1 \\.\\.\\.\\$arrays of function array_merge expects array, mixed given\\.$#"
			count: 3
			path: config/container.php

		-
			message: "#^Parameter \\#2 \\$logger of class SlimErrorRenderer\\\\Middleware\\\\ExceptionHandlingMiddleware constructor expects Psr\\\\Log\\\\LoggerInterface\\|null, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#2 \\$logger of class SlimErrorRenderer\\\\Middleware\\\\NonFatalErrorHandlingMiddleware constructor expects Psr\\\\Log\\\\LoggerInterface\\|null, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#2 \\.\\.\\.\\$values of function sprintf expects bool\\|float\\|int\\|string\\|null, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#3 \\$displayErrorDetails of class SlimErrorRenderer\\\\Middleware\\\\ExceptionHandlingMiddleware constructor expects bool, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#3 \\$level of class Monolog\\\\Handler\\\\RotatingFileHandler constructor expects 100\\|200\\|250\\|300\\|400\\|500\\|550\\|600\\|'ALERT'\\|'alert'\\|'CRITICAL'\\|'critical'\\|'DEBUG'\\|'debug'\\|'EMERGENCY'\\|'emergency'\\|'ERROR'\\|'error'\\|'INFO'\\|'info'\\|'NOTICE'\\|'notice'\\|'WARNING'\\|'warning'\\|Monolog\\\\Level, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Parameter \\#4 \\$errorReportEmailAddress of class SlimErrorRenderer\\\\Middleware\\\\ExceptionHandlingMiddleware constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: config/container.php

		-
			message: "#^Trying to invoke mixed but it's not a callable\\.$#"
			count: 3
			path: config/container.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: config/env/env.dev.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: config/env/env.phinx.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: config/env/env.phinx.php

		-
			message: "#^Cannot access offset 'environments' on mixed\\.$#"
			count: 1
			path: config/env/env.phinx.php

		-
			message: "#^Cannot access offset 'local' on mixed\\.$#"
			count: 1
			path: config/env/env.phinx.php

		-
			message: "#^Cannot access offset 'phinx' on mixed\\.$#"
			count: 1
			path: config/env/env.phinx.php

		-
			message: "#^Cannot call method get\\(\\) on Psr\\\\Container\\\\ContainerInterface\\|null\\.$#"
			count: 2
			path: config/env/env.phinx.php

		-
			message: "#^PHPDoc tag @var for variable \\$app contains generic class Slim\\\\App but does not specify its types\\: TContainerInterface$#"
			count: 1
			path: config/env/env.phinx.php

		-
			message: "#^Cannot access offset 'allowed_origin' on mixed\\.$#"
			count: 1
			path: src/Application/Middleware/CorsMiddleware.php

		-
			message: "#^Property App\\\\Application\\\\Middleware\\\\CorsMiddleware\\:\\:\\$allowedOrigin \\(string\\|null\\) does not accept mixed\\.$#"
			count: 1
			path: src/Application/Middleware/CorsMiddleware.php

		-
			message: "#^Method App\\\\Application\\\\Middleware\\\\PhpViewMiddleware\\:\\:__construct\\(\\) has parameter \\$app with generic class Slim\\\\App but does not specify its types\\: TContainerInterface$#"
			count: 1
			path: src/Application/Middleware/PhpViewMiddleware.php

		-
			message: "#^Property App\\\\Application\\\\Middleware\\\\PhpViewMiddleware\\:\\:\\$deploymentSettings \\(array\\<string, mixed\\>\\) does not accept mixed\\.$#"
			count: 1
			path: src/Application/Middleware/PhpViewMiddleware.php

		-
			message: "#^Property App\\\\Application\\\\Middleware\\\\PhpViewMiddleware\\:\\:\\$publicSettings \\(array\\<string, mixed\\>\\) does not accept mixed\\.$#"
			count: 1
			path: src/Application/Middleware/PhpViewMiddleware.php

		-
			message: "#^Cannot access offset 'asset_path' on mixed\\.$#"
			count: 1
			path: src/Infrastructure/Utility/JsImportCacheBuster.php

		-
			message: "#^Cannot access offset 'version' on mixed\\.$#"
			count: 1
			path: src/Infrastructure/Utility/JsImportCacheBuster.php

		-
			message: "#^Cannot call method getPathname\\(\\) on mixed\\.$#"
			count: 3
			path: src/Infrastructure/Utility/JsImportCacheBuster.php

		-
			message: "#^Property App\\\\Infrastructure\\\\Utility\\\\JsImportCacheBuster\\:\\:\\$assetPath \\(string\\) does not accept mixed\\.$#"
			count: 1
			path: src/Infrastructure/Utility/JsImportCacheBuster.php

		-
			message: "#^Property App\\\\Infrastructure\\\\Utility\\\\JsImportCacheBuster\\:\\:\\$version \\(string\\|null\\) does not accept mixed\\.$#"
			count: 1
			path: src/Infrastructure/Utility/JsImportCacheBuster.php

		-
			message: "#^Parameter \\#1 \\$datetime of class DateTimeImmutable constructor expects string, mixed given\\.$#"
			count: 2
			path: src/Module/User/Data/UserData.php

		-
			message: "#^Property App\\\\Module\\\\User\\\\Data\\\\UserData\\:\\:\\$email \\(string\\|null\\) does not accept mixed\\.$#"
			count: 1
			path: src/Module/User/Data/UserData.php

		-
			message: "#^Property App\\\\Module\\\\User\\\\Data\\\\UserData\\:\\:\\$firstName \\(string\\|null\\) does not accept mixed\\.$#"
			count: 1
			path: src/Module/User/Data/UserData.php

		-
			message: "#^Property App\\\\Module\\\\User\\\\Data\\\\UserData\\:\\:\\$id \\(int\\|null\\) does not accept mixed\\.$#"
			count: 1
			path: src/Module/User/Data/UserData.php

		-
			message: "#^Property App\\\\Module\\\\User\\\\Data\\\\UserData\\:\\:\\$lastName \\(string\\|null\\) does not accept mixed\\.$#"
			count: 1
			path: src/Module/User/Data/UserData.php

		-
			message: "#^Method App\\\\Module\\\\User\\\\Read\\\\Repository\\\\UserReadFinderRepository\\:\\:findUserById\\(\\) should return array\\<string, mixed\\> but returns mixed\\.$#"
			count: 1
			path: src/Module/User/Read/Repository/UserReadFinderRepository.php

		-
			message: "#^Parameter \\#2 \\$userValues of method App\\\\Module\\\\User\\\\Update\\\\Repository\\\\UserUpdaterRepository\\:\\:updateUser\\(\\) expects array\\<string, int\\|string\\|null\\>, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: src/Module/User/Update/Service/UserUpdater.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/Home/HomePageActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/Home/HomePageActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/Home/HomePageActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/Home/HomePageActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/Home/HomePageActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/Home/HomePageActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\Home\\\\HomePageActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/Home/HomePageActionTest.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Create/UserCreateActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Create/UserCreateActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Create/UserCreateActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Create/UserCreateActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Create/UserCreateActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/User/Create/UserCreateActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\User\\\\Create\\\\UserCreateActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/User/Create/UserCreateActionTest.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Delete/UserDeleteActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Delete/UserDeleteActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Delete/UserDeleteActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Delete/UserDeleteActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Delete/UserDeleteActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/User/Delete/UserDeleteActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\User\\\\Delete\\\\UserDeleteActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/User/Delete/UserDeleteActionTest.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/ApiUserFetchListActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/ApiUserFetchListActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/ApiUserFetchListActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/ApiUserFetchListActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/ApiUserFetchListActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/User/List/ApiUserFetchListActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\User\\\\List\\\\ApiUserFetchListActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/User/List/ApiUserFetchListActionTest.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserFetchListActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserFetchListActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserFetchListActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserFetchListActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserFetchListActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserFetchListActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\User\\\\List\\\\UserFetchListActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/User/List/UserFetchListActionTest.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserListPageActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserListPageActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserListPageActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserListPageActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserListPageActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/User/List/UserListPageActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\User\\\\List\\\\UserListPageActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/User/List/UserListPageActionTest.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Read/UserReadPageActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Read/UserReadPageActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Read/UserReadPageActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Read/UserReadPageActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Read/UserReadPageActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/User/Read/UserReadPageActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\User\\\\Read\\\\UserReadPageActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/User/Read/UserReadPageActionTest.php

		-
			message: "#^Cannot access offset 'database' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Update/UserUpdateActionTest.php

		-
			message: "#^Cannot access offset 'db' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Update/UserUpdateActionTest.php

		-
			message: "#^Cannot access offset 'root_dir' on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Update/UserUpdateActionTest.php

		-
			message: "#^Cannot call method getDriver\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Update/UserUpdateActionTest.php

		-
			message: "#^Cannot call method rollback\\(\\) on mixed\\.$#"
			count: 1
			path: tests/TestCase/User/Update/UserUpdateActionTest.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, mixed given\\.$#"
			count: 1
			path: tests/TestCase/User/Update/UserUpdateActionTest.php

		-
			message: "#^Property App\\\\Test\\\\TestCase\\\\User\\\\Update\\\\UserUpdateActionTest\\:\\:\\$app with generic class Slim\\\\App does not specify its types\\: TContainerInterface$#"
			count: 1
			path: tests/TestCase/User/Update/UserUpdateActionTest.php
