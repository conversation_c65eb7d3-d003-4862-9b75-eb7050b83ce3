<?php

declare(strict_types = 1);

namespace Tests\Modules\Article\Domain\Entity;

use App\Module\Article\Domain\Entity\Article;
use App\Module\Article\Domain\Enum\ArticleType;
use App\Module\Article\Domain\Enum\Currency;
use App\Module\Article\Domain\Enum\ProductStatus;
use App\Module\Article\Domain\Enum\ProductType;
use PHPUnit\Framework\TestCase;

final class ArticleTest extends TestCase
{
    public function testCanBeCreated(): void
    {
        $article = new Article(
            '1',
            'Testovací článok',
            'testovaci-clanok',
            'Toto je testovací výňatok z článku.',
            'Kompletný obsah článku...',
            '/cesta/k/obrazku.jpg',
            'author-123',
            ProductType::ARTICLE,
            ArticleType::BLOG_POST,
            null,
            'ART-BLOG-001',
            19.99,
            Currency::EUR,
            ProductStatus::PUBLISHED,
            true,
            true,
            'Meta title',
            'Meta description',
            'keyword1, keyword2',
            new \DateTimeImmutable('2025-05-26'),
            new \DateTimeImmutable('2025-05-26'),
            null,
            null
        );

        $this->assertSame('1', $article->getId());
        $this->assertSame('Testovací článok', $article->getTitle());
        $this->assertSame('testovaci-clanok', $article->getSlug());
        $this->assertSame('Toto je testovací výňatok z článku.', $article->getExcerpt());
        $this->assertSame('Kompletný obsah článku...', $article->getContent());
        $this->assertSame('/cesta/k/obrazku.jpg', $article->getImage());
        $this->assertSame('author-123', $article->getAuthorId());
        $this->assertSame(ProductType::ARTICLE, $article->getProductType());
        $this->assertSame(ArticleType::BLOG_POST, $article->getArticleType());
        $this->assertSame('ART-BLOG-001', $article->getSku());
        $this->assertSame(19.99, $article->getPrice());
        $this->assertSame(Currency::EUR, $article->getCurrency());
        $this->assertSame(ProductStatus::PUBLISHED, $article->getStatus());
        $this->assertTrue($article->isFeatured());
        $this->assertTrue($article->isPublic());
        $this->assertSame('2025-05-26', $article->getCreatedAt()->format('Y-m-d'));
    }

    public function testToArrayReturnsCorrectData(): void
    {
        $article = new Article(
            '2',
            'Ďalší článok',
            'dalsi-clanok',
            'Výňatok z ďalšieho článku',
            'Obsah ďalšieho článku...',
            '/cesta/k/dalsiemu-obrazku.jpg',
            null,
            ProductType::ARTICLE,
            ArticleType::REVIEW,
            'electronics',
            'ART-REVIE-002',
            5.99,
            Currency::EUR,
            ProductStatus::DRAFT,
            false,
            false,
            null,
            null,
            null,
            null,
            new \DateTimeImmutable('2025-05-25'),
            new \DateTimeImmutable('2025-05-26'),
            null
        );

        $expected = [
            'id' => '2',
            'title' => 'Ďalší článok',
            'slug' => 'dalsi-clanok',
            'excerpt' => 'Výňatok z ďalšieho článku',
            'content' => 'Obsah ďalšieho článku...',
            'image' => '/cesta/k/dalsiemu-obrazku.jpg',
            'author_id' => null,
            'product_type' => 'article',
            'article_type' => 'review',
            'product_subtype' => 'electronics',
            'sku' => 'ART-REVIE-002',
            'price' => 5.99,
            'currency' => 'EUR',
            'status' => 'draft',
            'is_featured' => false,
            'is_public' => false,
            'meta_title' => null,
            'meta_description' => null,
            'meta_keywords' => null,
            'published_at' => null,
            'created_at' => '2025-05-25 00:00:00',
            'updated_at' => '2025-05-26 00:00:00',
            'deleted_at' => null,
        ];

        $this->assertSame($expected, $article->toArray());
    }
}
