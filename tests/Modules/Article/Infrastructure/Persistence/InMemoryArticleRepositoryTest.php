<?php

declare(strict_types = 1);

namespace Tests\Modules\Article\Infrastructure\Persistence;

use App\Module\Article\Domain\Entity\Article;
use App\Module\Article\Infrastructure\Persistence\InMemoryArticleRepository;
use PHPUnit\Framework\TestCase;

final class InMemoryArticleRepositoryTest extends TestCase
{
    private InMemoryArticleRepository $repository;

    protected function setUp(): void
    {
        $this->repository = new InMemoryArticleRepository();
    }

    public function testFindLatestReturnsLimitedNumberOfArticles(): void
    {
        $articles = $this->repository->findLatest(2);

        $this->assertCount(2, $articles);
        $this->assertContainsOnlyInstancesOf(Article::class, $articles);
    }

    public function testFindByIdReturnsCorrectArticle(): void
    {
        $article = $this->repository->findById('1');

        $this->assertInstanceOf(Article::class, $article);
        $this->assertSame('1', $article->getId());
    }

    public function testFindBySlugReturnsCorrectArticle(): void
    {
        $article = $this->repository->findBySlug('gitarove-stupnice-pre-zaciatocnikov');

        $this->assertInstanceOf(Article::class, $article);
        $this->assertSame('Gitarové stupnice pre začiatočníkov', $article->getTitle());
    }

    public function testFindBySlugReturnsNullForNonExistentSlug(): void
    {
        $article = $this->repository->findBySlug('neexistujuci-clanok');

        $this->assertNull($article);
    }
}
