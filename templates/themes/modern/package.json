{"name": "slim4-modern-theme", "version": "1.0.0", "description": "Modern GSAP + Tailwind CSS theme for Slim4 application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:analyze": "vite build --mode analyze", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "clean": "rm -rf ../../../public/assets/themes/modern/*", "lint:js": "echo 'JS linting not configured yet'", "lint:css": "echo 'CSS linting not configured yet'", "type-check": "echo 'Type checking not configured yet'", "test": "echo 'Tests not configured yet'"}, "keywords": ["slim4", "theme", "gsap", "tailwindcss", "modern", "responsive", "modular", "typescript", "vite", "guitar", "music"], "author": "Slim4 Team", "license": "MIT", "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "terser": "^5.39.2", "vite": "^6.3.5"}, "dependencies": {"alpinejs": "^3.14.9", "gsap": "^3.13.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}