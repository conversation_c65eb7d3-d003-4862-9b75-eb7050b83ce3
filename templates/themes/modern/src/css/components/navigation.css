/* Navigation Components */
@layer components {
  /* Navigation links */
  .nav-link {
    @apply px-3 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200;
  }

  .nav-link-active {
    @apply nav-link text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
  }

  /* Navigation bars */
  .navbar {
    @apply bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm;
  }

  .navbar-transparent {
    @apply bg-transparent border-none shadow-none;
  }

  .navbar-glass {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
    @apply shadow-sm;
  }

  .dark .navbar-glass {
    background-color: rgba(17, 25, 40, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
  }

  /* Breadcrumbs */
  .breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400;
  }

  .breadcrumb-item {
    @apply hover:text-gray-700 dark:hover:text-gray-300 transition-colors;
  }

  .breadcrumb-separator {
    @apply text-gray-300 dark:text-gray-600;
  }

  /* Sidebar */
  .sidebar {
    @apply bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 h-full;
  }

  .sidebar-item {
    @apply block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
  }

  .sidebar-item-active {
    @apply sidebar-item bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border-r-2 border-primary-600;
  }

  /* Mobile menu */
  .mobile-menu {
    @apply fixed inset-0 z-50 bg-white dark:bg-gray-800 transform transition-transform duration-300;
  }

  .mobile-menu-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  }

  /* Tabs */
  .tab-list {
    @apply flex border-b border-gray-200 dark:border-gray-700;
  }

  .tab {
    @apply px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600 transition-all;
  }

  .tab-active {
    @apply tab text-primary-600 dark:text-primary-400 border-primary-600;
  }
}
