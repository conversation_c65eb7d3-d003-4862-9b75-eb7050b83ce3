/* Form Components */
@layer components {
  /* Form inputs */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-input-sm {
    @apply form-input px-2 py-1 text-sm;
  }

  .form-input-lg {
    @apply form-input px-4 py-3 text-lg;
  }

  /* Form labels */
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
  }

  .form-label-required::after {
    content: ' *';
    @apply text-red-500;
  }

  /* Form groups */
  .form-group {
    @apply mb-4;
  }

  .form-group-inline {
    @apply flex items-center space-x-4;
  }

  /* Form validation states */
  .form-input-error {
    @apply form-input border-red-500 focus:ring-red-500 focus:border-red-500;
  }

  .form-input-success {
    @apply form-input border-green-500 focus:ring-green-500 focus:border-green-500;
  }

  .form-error-message {
    @apply mt-1 text-sm text-red-600 dark:text-red-400;
  }

  .form-help-text {
    @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
  }

  /* Checkboxes and radios */
  .form-checkbox,
  .form-radio {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800;
  }

  .form-radio {
    @apply rounded-full;
  }

  /* Select dropdowns */
  .form-select {
    @apply form-input pr-10 bg-no-repeat bg-right;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
  }

  /* Textareas */
  .form-textarea {
    @apply form-input resize-y min-h-[100px];
  }

  /* File inputs */
  .form-file {
    @apply block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100 dark:file:bg-primary-900 dark:file:text-primary-300 dark:hover:file:bg-primary-800;
  }
}
