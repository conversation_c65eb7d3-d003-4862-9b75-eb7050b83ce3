/* Button Components */
@layer components {
  /* Base button */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  /* Button variants */
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-accent {
    @apply btn bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500;
  }

  .btn-ghost {
    @apply btn bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 border-gray-300 dark:border-gray-600;
  }

  /* Button sizes */
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-xl {
    @apply px-8 py-4 text-lg;
  }

  /* Button states */
  .btn-loading {
    @apply opacity-75 cursor-not-allowed;
  }

  .btn-disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
  }
}
