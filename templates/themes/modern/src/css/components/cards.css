/* Card Components */
@layer components {
  /* Base card */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-200;
  }

  /* Card variants */
  .card-hover {
    @apply card hover:shadow-xl hover:-translate-y-1;
  }

  .card-flat {
    @apply bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700;
  }

  .card-elevated {
    @apply card shadow-2xl;
  }

  /* Glass morphism card */
  .card-glass {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
    @apply rounded-xl shadow-lg transition-all duration-200;
  }

  .dark .card-glass {
    background-color: rgba(17, 25, 40, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  /* Card sections */
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 rounded-b-xl;
  }

  /* Card sizes */
  .card-sm {
    @apply p-4;
  }

  .card-lg {
    @apply p-8;
  }

  /* Card states */
  .card-loading {
    @apply opacity-75 pointer-events-none;
  }

  .card-disabled {
    @apply opacity-50 pointer-events-none;
  }
}
