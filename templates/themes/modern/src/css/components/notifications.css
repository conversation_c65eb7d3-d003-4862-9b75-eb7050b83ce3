/* Notification Components */
@layer components {
  /* Base notification */
  .notification {
    @apply fixed top-4 right-4 max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden z-50;
  }

  /* Notification variants */
  .notification-success {
    @apply notification border-l-4 border-green-500;
  }

  .notification-error {
    @apply notification border-l-4 border-red-500;
  }

  .notification-warning {
    @apply notification border-l-4 border-yellow-500;
  }

  .notification-info {
    @apply notification border-l-4 border-blue-500;
  }

  /* Notification content */
  .notification-content {
    @apply p-4;
  }

  .notification-title {
    @apply text-sm font-medium text-gray-900 dark:text-gray-100;
  }

  .notification-message {
    @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
  }

  /* Notification actions */
  .notification-actions {
    @apply mt-3 flex space-x-2;
  }

  .notification-action {
    @apply text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 transition-colors;
  }

  /* Notification close button */
  .notification-close {
    @apply absolute top-2 right-2 p-1 rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors;
  }

  /* Toast notifications */
  .toast {
    @apply fixed bottom-4 right-4 max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden z-50 transform transition-all duration-300;
  }

  .toast-enter {
    @apply translate-x-full opacity-0;
  }

  .toast-enter-active {
    @apply translate-x-0 opacity-100;
  }

  .toast-exit {
    @apply translate-x-0 opacity-100;
  }

  .toast-exit-active {
    @apply translate-x-full opacity-0;
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }

  .spinner-lg {
    @apply w-8 h-8 border-4;
  }

  /* Progress bars */
  .progress {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
  }

  .progress-bar {
    @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
  }

  .progress-bar-success {
    @apply bg-green-600;
  }

  .progress-bar-warning {
    @apply bg-yellow-600;
  }

  .progress-bar-error {
    @apply bg-red-600;
  }
}
