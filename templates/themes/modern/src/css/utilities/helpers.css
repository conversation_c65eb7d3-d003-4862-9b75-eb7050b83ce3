/* Helper Utilities */
@layer utilities {
  /* Focus utilities */
  .focus-visible {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  /* Glass morphism utilities */
  .glass {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  .dark .glass {
    background-color: rgba(17, 25, 40, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  .glass-strong {
    backdrop-filter: blur(24px) saturate(200%);
    background-color: rgba(255, 255, 255, 0.85);
  }

  .dark .glass-strong {
    background-color: rgba(17, 25, 40, 0.85);
  }

  /* Text utilities */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent;
  }

  .text-gradient-rainbow {
    @apply bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 bg-clip-text text-transparent;
  }

  /* Background utilities */
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #0ea5e9 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  /* Shadow utilities */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-accent {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }

  .shadow-inner-light {
    box-shadow: inset 0 2px 4px 0 rgba(255, 255, 255, 0.1);
  }

  /* Aspect ratio utilities */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-photo {
    aspect-ratio: 4 / 3;
  }

  /* Positioning utilities */
  .center-absolute {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  }

  .center-fixed {
    @apply fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  }

  /* Interaction utilities */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .select-all {
    -webkit-user-select: all;
    -moz-user-select: all;
    -ms-user-select: all;
    user-select: all;
  }

  /* Print utilities */
  @media print {
    .print-hidden {
      display: none !important;
    }

    .print-visible {
      display: block !important;
    }
  }

  /* Accessibility utilities */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
}
