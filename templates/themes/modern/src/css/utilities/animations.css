/* Animation Utilities */
@layer utilities {
  /* Gradient animations */
  .gradient-animated {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  .dark .gradient-animated {
    background: linear-gradient(-45deg, #1e3a8a, #312e81, #7c3aed, #be185d);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  /* Keyframe animations */
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
    }
  }

  @keyframes slide-in-right {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-out-right {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fade-out {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  @keyframes scale-in {
    from {
      transform: scale(0.9);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Animation classes */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.3s ease-out;
  }

  .animate-slide-out-right {
    animation: slide-out-right 0.3s ease-in;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-fade-out {
    animation: fade-out 0.3s ease-in;
  }

  .animate-scale-in {
    animation: scale-in 0.2s ease-out;
  }

  /* Transition utilities */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }
}
