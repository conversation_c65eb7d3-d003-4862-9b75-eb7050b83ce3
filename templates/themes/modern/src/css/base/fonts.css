/* Local Gilroy Fonts */
@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/assets/themes/modern/fonts/<PERSON>roy-Regular.woff2') format('woff2'),
       url('/assets/themes/modern/fonts/<PERSON>roy-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/assets/themes/modern/fonts/Gilroy-Medium.woff2') format('woff2'),
       url('/assets/themes/modern/fonts/Gilroy-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/assets/themes/modern/fonts/Gilroy-SemiBold.woff2') format('woff2'),
       url('/assets/themes/modern/fonts/Gilroy-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/assets/themes/modern/fonts/<PERSON>roy-Bold.woff2') format('woff2'),
       url('/assets/themes/modern/fonts/<PERSON><PERSON>-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gilroy';
  src: url('/assets/themes/modern/fonts/Gilroy-Black.woff2') format('woff2'),
       url('/assets/themes/modern/fonts/Gilroy-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Import assets to ensure Vite processes them */
.favicon-32 { background-image: url('../assets/images/favicon-32x32.png'); }
.apple-touch-icon { background-image: url('../assets/images/apple-touch-icon.png'); }
.favicon-ico { background-image: url('../assets/images/favicon.ico'); }
