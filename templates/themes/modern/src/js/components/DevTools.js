/**
 * Development Tools Component
 * Handles development-specific functionality and debugging tools
 */
export class DevTools {
  constructor() {
    this.isDevelopment = this.checkDevelopmentMode()
    this.init()
  }

  init() {
    if (!this.isDevelopment) {
      return
    }

    this.logEnvironmentInfo()
    this.setupDevConsole()
    this.setupPerformanceMonitoring()
    this.setupErrorHandling()
    
    console.log('🛠️ Development Tools component initialized')
  }

  checkDevelopmentMode() {
    // Check various indicators for development mode
    return document.body.dataset.env === 'development' ||
           window.location.hostname === 'localhost' ||
           window.location.hostname.includes('dev') ||
           document.querySelector('script[data-dev="true"]') !== null
  }

  logEnvironmentInfo() {
    console.group('🎨 Modern Theme - Development Mode')
    
    // Log library versions
    if (typeof gsap !== 'undefined') {
      console.log('GSAP version:', gsap.version)
    }
    
    if (typeof Alpine !== 'undefined' && Alpine.version) {
      console.log('Alpine.js version:', Alpine.version)
    }
    
    if (typeof tinymce !== 'undefined') {
      console.log('TinyMCE version:', tinymce.majorVersion + '.' + tinymce.minorVersion)
    }

    // Log theme info
    console.log('Theme:', 'Modern Theme')
    console.log('Build time:', new Date().toISOString())
    console.log('User Agent:', navigator.userAgent)
    
    console.groupEnd()
  }

  setupDevConsole() {
    // Add global development helpers
    window.devTools = {
      // Theme utilities
      theme: {
        showNotification: (message, type = 'info') => {
          if (window.modernThemeApp) {
            window.modernThemeApp.showNotification(message, type)
          }
        },
        
        triggerAnimation: (selector, type = 'fadeIn') => {
          if (window.demoAnimations) {
            window.demoAnimations.triggerAnimation(selector, type)
          }
        },
        
        reloadCSS: () => {
          const links = document.querySelectorAll('link[rel="stylesheet"]')
          links.forEach(link => {
            const href = link.href
            link.href = href + (href.includes('?') ? '&' : '?') + 'reload=' + Date.now()
          })
          console.log('🎨 CSS reloaded')
        }
      },

      // Performance utilities
      performance: {
        measure: (name, fn) => {
          const start = performance.now()
          const result = fn()
          const end = performance.now()
          console.log(`⏱️ ${name}: ${(end - start).toFixed(2)}ms`)
          return result
        },
        
        logMemory: () => {
          if (performance.memory) {
            console.table({
              'Used JS Heap Size': (performance.memory.usedJSHeapSize / 1048576).toFixed(2) + ' MB',
              'Total JS Heap Size': (performance.memory.totalJSHeapSize / 1048576).toFixed(2) + ' MB',
              'JS Heap Size Limit': (performance.memory.jsHeapSizeLimit / 1048576).toFixed(2) + ' MB'
            })
          }
        }
      },

      // Debug utilities
      debug: {
        logElements: (selector) => {
          const elements = document.querySelectorAll(selector)
          console.log(`Found ${elements.length} elements for "${selector}":`, elements)
          return elements
        },
        
        highlightElement: (selector) => {
          const elements = document.querySelectorAll(selector)
          elements.forEach(el => {
            el.style.outline = '2px solid red'
            setTimeout(() => {
              el.style.outline = ''
            }, 3000)
          })
        },
        
        logEventListeners: (element) => {
          console.log('Event listeners for element:', element)
          // This would require a more complex implementation
          // or use of browser dev tools
        }
      }
    }

    console.log('🛠️ Development tools available in window.devTools')
  }

  setupPerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0]
        
        console.group('📊 Performance Metrics')
        console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart + 'ms')
        console.log('Load Complete:', perfData.loadEventEnd - perfData.loadEventStart + 'ms')
        console.log('Total Load Time:', perfData.loadEventEnd - perfData.fetchStart + 'ms')
        console.groupEnd()
      }, 1000)
    })

    // Monitor GSAP animations
    if (typeof gsap !== 'undefined') {
      gsap.ticker.add(() => {
        // Could add animation performance monitoring here
      })
    }
  }

  setupErrorHandling() {
    // Enhanced error logging for development
    window.addEventListener('error', (event) => {
      console.group('❌ JavaScript Error')
      console.error('Message:', event.message)
      console.error('File:', event.filename)
      console.error('Line:', event.lineno)
      console.error('Column:', event.colno)
      console.error('Error object:', event.error)
      console.groupEnd()
    })

    // Promise rejection handling
    window.addEventListener('unhandledrejection', (event) => {
      console.group('❌ Unhandled Promise Rejection')
      console.error('Reason:', event.reason)
      console.groupEnd()
    })
  }

  // Method to add custom dev tools
  addTool(name, tool) {
    if (this.isDevelopment && window.devTools) {
      window.devTools[name] = tool
      console.log(`🛠️ Added dev tool: ${name}`)
    }
  }
}
