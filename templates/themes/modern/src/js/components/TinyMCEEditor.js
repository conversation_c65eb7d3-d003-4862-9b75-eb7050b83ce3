/**
 * TinyMCE Editor Component
 * Handles rich text editor initialization and configuration
 */
export class TinyMCEEditor {
  constructor() {
    this.defaultConfig = {
      height: 500,
      menubar: true,
      plugins: [
        'advlist autolink lists link image charmap print preview anchor',
        'searchreplace visualblocks code fullscreen',
        'insertdatetime media table paste code help wordcount'
      ],
      toolbar: 'undo redo | formatselect | ' +
        'bold italic backcolor | alignleft aligncenter ' +
        'alignright alignjustify | bullist numlist outdent indent | ' +
        'removeformat | help',
      content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
      skin: 'oxide',
      content_css: 'default'
    }
    
    this.init()
  }

  init() {
    // Check if TinyMCE is needed on this page
    if (!this.hasTinyMCEElements()) {
      return
    }

    this.loadTinyMCE().then(() => {
      this.initializeEditors()
      console.log('📝 TinyMCE Editor component initialized')
    })
  }

  hasTinyMCEElements() {
    return document.querySelector('textarea[data-editor="tinymce"]') !== null ||
           document.querySelector('#content') !== null
  }

  async loadTinyMCE() {
    // Check if TinyMCE is already loaded
    if (typeof tinymce !== 'undefined') {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js'
      script.referrerPolicy = 'origin'
      
      script.onload = () => resolve()
      script.onerror = () => reject(new Error('Failed to load TinyMCE'))
      
      document.head.appendChild(script)
    })
  }

  initializeEditors() {
    // Initialize default content editor
    if (document.querySelector('#content')) {
      this.initEditor('#content', this.defaultConfig)
    }

    // Initialize all elements with data-editor="tinymce"
    document.querySelectorAll('textarea[data-editor="tinymce"]').forEach(textarea => {
      const customConfig = this.getCustomConfig(textarea)
      this.initEditor(`#${textarea.id}`, customConfig)
    })
  }

  initEditor(selector, config = {}) {
    const finalConfig = {
      selector,
      ...this.defaultConfig,
      ...config,
      setup: (editor) => {
        // Custom setup for each editor
        editor.on('init', () => {
          console.log(`📝 TinyMCE editor initialized for ${selector}`)
        })

        editor.on('change', () => {
          // Auto-save functionality could be added here
          this.handleEditorChange(editor)
        })

        // Call custom setup if provided
        if (config.setup) {
          config.setup(editor)
        }
      }
    }

    tinymce.init(finalConfig)
  }

  getCustomConfig(textarea) {
    const config = {}
    
    // Read configuration from data attributes
    if (textarea.dataset.height) {
      config.height = parseInt(textarea.dataset.height)
    }
    
    if (textarea.dataset.plugins) {
      config.plugins = textarea.dataset.plugins.split(',')
    }
    
    if (textarea.dataset.toolbar) {
      config.toolbar = textarea.dataset.toolbar
    }

    // Simple editor configuration
    if (textarea.dataset.simple === 'true') {
      config.plugins = ['lists', 'link', 'paste']
      config.toolbar = 'bold italic | bullist numlist | link'
      config.menubar = false
    }

    return config
  }

  handleEditorChange(editor) {
    // Trigger custom event for editor changes
    const event = new CustomEvent('tinymce-change', {
      detail: {
        editor: editor,
        content: editor.getContent()
      }
    })
    
    document.dispatchEvent(event)
  }

  // Public methods for external use
  getEditorContent(selector) {
    const editor = tinymce.get(selector.replace('#', ''))
    return editor ? editor.getContent() : null
  }

  setEditorContent(selector, content) {
    const editor = tinymce.get(selector.replace('#', ''))
    if (editor) {
      editor.setContent(content)
    }
  }

  destroyEditor(selector) {
    const editor = tinymce.get(selector.replace('#', ''))
    if (editor) {
      editor.destroy()
    }
  }
}
