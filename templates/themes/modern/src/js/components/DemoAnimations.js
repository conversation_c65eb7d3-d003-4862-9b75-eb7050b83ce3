/**
 * Demo Animations Component
 * Handles demo-specific animations and interactive elements
 */
export class DemoAnimations {
  constructor() {
    this.init()
  }

  init() {
    this.setupWelcomeNotification()
    this.setupTechBadgeAnimations()
    this.setupInteractiveElements()
    
    console.log('🎭 Demo Animations component initialized')
  }

  setupWelcomeNotification() {
    // Show welcome notification on home page
    if (this.isHomePage()) {
      setTimeout(() => {
        if (window.modernThemeApp && window.modernThemeApp.showNotification) {
          window.modernThemeApp.showNotification('Vitajte v Modern Theme! 🎉', 'success', 3000)
        }
      }, 1000)
    }
  }

  setupTechBadgeAnimations() {
    // Animate tech badges if GSAP is available
    if (typeof gsap !== 'undefined' && document.querySelector('.tech-badge')) {
      gsap.from('.tech-badge', {
        duration: 0.8,
        y: 20,
        opacity: 0,
        stagger: 0.1,
        delay: 1,
        ease: 'power2.out'
      })
    }
  }

  setupInteractiveElements() {
    // Add hover effects to cards
    document.querySelectorAll('.card-hover').forEach(card => {
      card.addEventListener('mouseenter', () => {
        if (typeof gsap !== 'undefined') {
          gsap.to(card, {
            duration: 0.3,
            scale: 1.02,
            ease: 'power2.out'
          })
        }
      })

      card.addEventListener('mouseleave', () => {
        if (typeof gsap !== 'undefined') {
          gsap.to(card, {
            duration: 0.3,
            scale: 1,
            ease: 'power2.out'
          })
        }
      })
    })

    // Add click effects to buttons
    document.querySelectorAll('.btn').forEach(button => {
      button.addEventListener('click', (e) => {
        if (typeof gsap !== 'undefined') {
          gsap.fromTo(button, 
            { scale: 1 },
            { 
              scale: 0.95, 
              duration: 0.1, 
              ease: 'power2.out',
              yoyo: true,
              repeat: 1
            }
          )
        }
      })
    })
  }

  isHomePage() {
    return window.location.pathname === '/' || document.querySelector('.hero-section') !== null
  }

  // Method to trigger custom demo animations
  triggerAnimation(selector, animationType = 'fadeIn') {
    if (typeof gsap === 'undefined') return

    const elements = document.querySelectorAll(selector)
    if (elements.length === 0) return

    switch (animationType) {
      case 'fadeIn':
        gsap.from(elements, {
          duration: 0.6,
          opacity: 0,
          y: 20,
          stagger: 0.1,
          ease: 'power2.out'
        })
        break
      
      case 'slideIn':
        gsap.from(elements, {
          duration: 0.8,
          x: -50,
          opacity: 0,
          stagger: 0.1,
          ease: 'power2.out'
        })
        break
      
      case 'scaleIn':
        gsap.from(elements, {
          duration: 0.6,
          scale: 0.8,
          opacity: 0,
          stagger: 0.1,
          ease: 'back.out(1.7)'
        })
        break
    }
  }
}
