/**
 * Mark Panel Component
 * Handles mark panel-specific functionality like sidebar management and keyboard shortcuts
 */
export class MarkPanel {
  constructor() {
    this.init()
  }

  init() {
    if (!this.isMarkPage()) {
      return
    }

    this.setupSidebarAutoClose()
    this.setupKeyboardShortcuts()
    this.setupConfirmDialogs()

    console.log('🔧 Mark Panel component initialized')
  }

  isMarkPage() {
    return document.querySelector('.mark-sidebar') !== null
  }

  setupSidebarAutoClose() {
    // Auto-close mobile sidebar when clicking on links
    document.querySelectorAll('.mark-sidebar a').forEach(link => {
      link.addEventListener('click', () => {
        if (window.innerWidth < 1024 && window.Alpine) {
          // Close sidebar on mobile
          const sidebarData = Alpine.$data(document.querySelector('[x-data*="sidebarOpen"]'))
          if (sidebarData) {
            sidebarData.sidebarOpen = false
          }
        }
      })
    })
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Alt + D for dashboard
      if (e.altKey && e.key === 'd') {
        e.preventDefault()
        window.location.href = '/mark'
      }

      // Alt + U for users
      if (e.altKey && e.key === 'u') {
        e.preventDefault()
        window.location.href = '/mark/users'
      }

      // Alt + S for settings
      if (e.altKey && e.key === 's') {
        e.preventDefault()
        window.location.href = '/mark/settings'
      }

      // Alt + C for content
      if (e.altKey && e.key === 'c') {
        e.preventDefault()
        window.location.href = '/mark/pages'
      }
    })

    console.log('⌨️ Mark keyboard shortcuts: Alt+D (Dashboard), Alt+U (Users), Alt+S (Settings), Alt+C (Content)')
  }

  setupConfirmDialogs() {
    // Enhanced confirm dialogs for delete actions
    document.querySelectorAll('a[onclick*="confirm"]').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault()

        const message = link.getAttribute('onclick').match(/confirm\('([^']+)'\)/)?.[1] || 'Naozaj chcete pokračovať?'

        if (this.showConfirmDialog(message)) {
          window.location.href = link.href
        }
      })

      // Remove inline onclick
      link.removeAttribute('onclick')
    })
  }

  showConfirmDialog(message) {
    // You can replace this with a custom modal if needed
    return confirm(message)
  }

  // Method to show mark panel notifications
  showNotification(message, type = 'info') {
    if (window.modernThemeApp && window.modernThemeApp.showNotification) {
      window.modernThemeApp.showNotification(message, type)
    }
  }
}
