/**
 * Template Helpers
 * Utility functions for use in templates
 */

/**
 * Initialize TinyMCE editor with custom configuration
 * Usage in template: <textarea id="content" data-editor="tinymce" data-height="400"></textarea>
 */
export function initTinyMCE(selector, config = {}) {
  // This will be handled by the TinyMCEEditor component
  // Just add the data attributes to the textarea
  const textarea = document.querySelector(selector)
  if (textarea) {
    textarea.setAttribute('data-editor', 'tinymce')
    
    // Set configuration via data attributes
    Object.keys(config).forEach(key => {
      textarea.setAttribute(`data-${key}`, config[key])
    })
  }
}

/**
 * Show notification using the theme's notification system
 */
export function showNotification(message, type = 'info', duration = 5000) {
  if (window.modernThemeApp && window.modernThemeApp.showNotification) {
    window.modernThemeApp.showNotification(message, type, duration)
  } else {
    // Fallback to console if theme app is not available
    console.log(`[${type.toUpperCase()}] ${message}`)
  }
}

/**
 * Trigger custom animation
 */
export function triggerAnimation(selector, animationType = 'fadeIn') {
  if (window.demoAnimations && window.demoAnimations.triggerAnimation) {
    window.demoAnimations.triggerAnimation(selector, animationType)
  }
}

/**
 * Add confirm dialog to delete links
 * Usage: <a href="/delete/1" data-confirm="Are you sure?">Delete</a>
 */
export function setupConfirmDialogs() {
  document.querySelectorAll('a[data-confirm]').forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault()
      
      const message = link.getAttribute('data-confirm')
      if (confirm(message)) {
        window.location.href = link.href
      }
    })
  })
}

/**
 * Setup form validation
 */
export function setupFormValidation(formSelector) {
  const form = document.querySelector(formSelector)
  if (!form) return

  form.addEventListener('submit', (e) => {
    const requiredFields = form.querySelectorAll('[required]')
    let isValid = true

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        field.classList.add('border-red-500')
        isValid = false
      } else {
        field.classList.remove('border-red-500')
      }
    })

    if (!isValid) {
      e.preventDefault()
      showNotification('Prosím vyplňte všetky povinné polia', 'error')
    }
  })
}

// Make helpers globally available
window.templateHelpers = {
  initTinyMCE,
  showNotification,
  triggerAnimation,
  setupConfirmDialogs,
  setupFormValidation
}
