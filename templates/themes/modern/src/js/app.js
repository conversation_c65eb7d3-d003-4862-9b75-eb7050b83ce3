/**
 * Modern Theme - Main JavaScript Entry Point
 *
 * Modular architecture for better maintainability and performance.
 * Each feature is split into separate modules for easier development.
 */

// Import CSS
import '../css/app.css'

// Import assets for Vite processing
import.meta.glob([
    '../assets/images/**/*',
    '../assets/images/*',
    '../assets/fonts/**',
], { eager: true });

// Core imports
import { config } from './core/config.js'
import { log, measurePerformance } from './core/utils.js'

// Module imports
import { AnimationManager } from './modules/animations.js'
import { NotificationManager } from './modules/notifications.js'
import { ThemeManager } from './modules/theme.js'
import { FormManager } from './modules/forms.js'

// Page imports
import { HomePage } from './pages/home.js'
import { LoginPage } from './pages/login.js'

// Legacy component imports (for backward compatibility)
import { MarkPanel } from './components/MarkPanel.js'
import { DemoAnimations } from './components/DemoAnimations.js'
import { TinyMCEEditor } from './components/TinyMCEEditor.js'
import { DevTools } from './components/DevTools.js'

// Import utilities
import './utils/template-helpers.js'

// Import Alpine.js
import Alpine from 'alpinejs'

// Initialize Alpine.js
window.Alpine = Alpine
Alpine.start()

/**
 * Main Theme Class
 * Orchestrates all modules and handles application lifecycle
 */
class ModernThemeApp {
  constructor() {
    this.isInitialized = false
    this.modules = new Map()
    this.currentPage = null

    // Initialize managers
    this.animationManager = new AnimationManager()
    this.notificationManager = new NotificationManager()
    this.themeManager = new ThemeManager()
    this.formManager = new FormManager()

    // Store managers in modules map
    this.modules.set('animations', this.animationManager)
    this.modules.set('notifications', this.notificationManager)
    this.modules.set('theme', this.themeManager)
    this.modules.set('forms', this.formManager)

    // Initialize
    this.init()
  }

  /**
   * Initialize application
   */
  async init() {
    if (this.isInitialized) return

    try {
      await measurePerformance('theme-initialization', async () => {
        // Initialize core modules
        this.animationManager.init()
        this.themeManager.init()
        this.formManager.init()
        // NotificationManager initializes automatically

        // Initialize page-specific functionality
        this.initializePage()

        // Initialize legacy components for backward compatibility
        this.initializeLegacyComponents()

        this.isInitialized = true
        log('info', '🎨 Modern Theme initialized successfully')
      })
    } catch (error) {
      log('error', 'Failed to initialize theme:', error)
    }
  }

  /**
   * Initialize page-specific functionality
   */
  initializePage() {
    const path = window.location.pathname

    // Determine current page and initialize appropriate module
    if (path === '/' || path === '/home') {
      this.currentPage = new HomePage()
      this.currentPage.init()
    } else if (path === '/login' || path === '/auth/login') {
      this.currentPage = new LoginPage()
      this.currentPage.init()
    }

    log('debug', `Page initialized: ${path}`)
  }

  /**
   * Initialize legacy components for backward compatibility
   */
  initializeLegacyComponents() {
    try {
      // Initialize legacy components
      this.markPanel = new MarkPanel()
      this.demoAnimations = new DemoAnimations()
      this.tinyMCEEditor = new TinyMCEEditor()
      this.devTools = new DevTools()

      // Make components globally available for debugging
      if (config.debug.enabled) {
        window.markPanel = this.markPanel
        window.demoAnimations = this.demoAnimations
        window.tinyMCEEditor = this.tinyMCEEditor
        window.devTools = this.devTools
      }

      log('debug', 'Legacy components initialized')
    } catch (error) {
      log('warn', 'Some legacy components failed to initialize:', error)
    }
  }

  /**
   * Get module by name
   * @param {string} name - Module name
   * @returns {Object} Module instance
   */
  getModule(name) {
    return this.modules.get(name)
  }

  /**
   * Get current page instance
   * @returns {Object} Current page instance
   */
  getCurrentPage() {
    return this.currentPage
  }

  /**
   * Refresh all modules
   */
  refresh() {
    this.modules.forEach(module => {
      if (module.refresh) {
        module.refresh()
      }
    })

    if (this.currentPage && this.currentPage.refresh) {
      this.currentPage.refresh()
    }

    log('debug', 'All modules refreshed')
  }

  /**
   * Destroy application
   */
  destroy() {
    // Destroy current page
    if (this.currentPage && this.currentPage.destroy) {
      this.currentPage.destroy()
    }

    // Destroy all modules
    this.modules.forEach(module => {
      if (module.destroy) {
        module.destroy()
      }
    })

    // Clear references
    this.modules.clear()
    this.currentPage = null
    this.isInitialized = false

    log('info', '🎨 Modern Theme destroyed')
  }

}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Initialize main theme app
    const app = new ModernThemeApp()

    // Make app globally available
    window.modernThemeApp = app
    window.ModernTheme = ModernThemeApp

    // Make modules globally available for debugging
    if (config.debug.enabled) {
      window.themeModules = {
        animations: app.getModule('animations'),
        notifications: app.getModule('notifications'),
        theme: app.getModule('theme'),
        forms: app.getModule('forms'),
        currentPage: app.getCurrentPage()
      }

      log('debug', 'Theme modules available in window.themeModules')
    }

  } catch (error) {
    log('error', 'Failed to initialize application:', error)
  }
})

// Handle page unload
window.addEventListener('beforeunload', () => {
  if (window.modernThemeApp) {
    window.modernThemeApp.destroy()
  }
})

// Export for global access
export { ModernThemeApp }
export default ModernThemeApp
