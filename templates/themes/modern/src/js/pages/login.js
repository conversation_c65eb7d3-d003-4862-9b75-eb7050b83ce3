/**
 * Login Page Module
 * Specific functionality for the login page
 */

import { api, endpoints } from '../core/api.js'
import { log } from '../core/utils.js'

export class LoginPage {
  constructor() {
    this.isInitialized = false
    this.loginForm = null
    this.passwordToggle = null
  }

  /**
   * Initialize login page
   */
  init() {
    if (this.isInitialized) return

    this.setupLoginForm()
    this.setupPasswordToggle()
    this.setupSocialLogin()
    this.setupDemoCredentials()

    this.isInitialized = true
    log('info', '🔐 Login page initialized')
  }

  /**
   * Setup login form handling
   */
  setupLoginForm() {
    this.loginForm = document.getElementById('loginForm')
    if (!this.loginForm) return

    this.loginForm.addEventListener('submit', async (e) => {
      e.preventDefault()
      await this.handleLogin(e.target)
    })
  }

  /**
   * Handle login form submission
   * @param {HTMLFormElement} form - Login form
   */
  async handleLogin(form) {
    const formData = new FormData(form)
    const email = formData.get('email')
    const password = formData.get('password')
    const rememberMe = formData.get('remember-me')

    const button = form.querySelector('#loginButton')
    const buttonText = form.querySelector('#loginButtonText')
    const spinner = form.querySelector('#loginSpinner')

    try {
      // Show loading state
      this.setLoadingState(button, buttonText, spinner, true)

      // Demo authentication (replace with real API call)
      if (email === '<EMAIL>' && password === 'password') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Success
        if (window.showNotification) {
          window.showNotification('Login successful! Welcome back.', 'success')
        }

        // Redirect after delay
        setTimeout(() => {
          window.location.href = '/dashboard'
        }, 1500)

      } else {
        // Real API call (uncomment when backend is ready)
        /*
        const response = await api.post(endpoints.auth.login, {
          email,
          password,
          remember: rememberMe === 'on'
        })

        // Store auth token
        if (response.token) {
          localStorage.setItem('auth_token', response.token)
        }

        // Success notification
        if (window.showNotification) {
          window.showNotification(response.message || 'Login successful!', 'success')
        }

        // Redirect
        const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/dashboard'
        setTimeout(() => {
          window.location.href = redirectUrl
        }, 1500)
        */

        // For now, show error for non-demo credentials
        throw new Error('Invalid credentials')
      }

    } catch (error) {
      // Show error notification
      if (window.showNotification) {
        const message = error.message.includes('Invalid') 
          ? 'Invalid email or password. Please try again.'
          : 'Login failed. Please try again.'
        window.showNotification(message, 'error')
      }

      log('error', 'Login error:', error)

    } finally {
      // Reset loading state
      this.setLoadingState(button, buttonText, spinner, false)
    }
  }

  /**
   * Set loading state for login button
   * @param {HTMLElement} button - Submit button
   * @param {HTMLElement} buttonText - Button text element
   * @param {HTMLElement} spinner - Spinner element
   * @param {boolean} isLoading - Loading state
   */
  setLoadingState(button, buttonText, spinner, isLoading) {
    if (!button || !buttonText || !spinner) return

    if (isLoading) {
      button.disabled = true
      buttonText.classList.add('hidden')
      spinner.classList.remove('hidden')
    } else {
      button.disabled = false
      buttonText.classList.remove('hidden')
      spinner.classList.add('hidden')
    }
  }

  /**
   * Setup password visibility toggle
   */
  setupPasswordToggle() {
    const passwordInput = document.getElementById('password')
    const toggleButton = document.querySelector('[onclick="togglePassword()"]')
    
    if (!passwordInput || !toggleButton) return

    // Replace inline onclick with proper event listener
    toggleButton.removeAttribute('onclick')
    toggleButton.addEventListener('click', () => {
      this.togglePasswordVisibility(passwordInput, toggleButton)
    })

    // Make global function available for backward compatibility
    window.togglePassword = () => {
      this.togglePasswordVisibility(passwordInput, toggleButton)
    }
  }

  /**
   * Toggle password visibility
   * @param {HTMLInputElement} passwordInput - Password input
   * @param {HTMLElement} toggleButton - Toggle button
   */
  togglePasswordVisibility(passwordInput, toggleButton) {
    const eyeIcon = toggleButton.querySelector('svg')
    
    if (passwordInput.type === 'password') {
      passwordInput.type = 'text'
      eyeIcon.innerHTML = `
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
      `
    } else {
      passwordInput.type = 'password'
      eyeIcon.innerHTML = `
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      `
    }
  }

  /**
   * Setup social login buttons
   */
  setupSocialLogin() {
    // Google login
    const googleButton = document.querySelector('[onclick="loginWithGoogle()"]')
    if (googleButton) {
      googleButton.removeAttribute('onclick')
      googleButton.addEventListener('click', () => {
        this.handleSocialLogin('google')
      })
    }

    // GitHub login
    const githubButton = document.querySelector('[onclick="loginWithGitHub()"]')
    if (githubButton) {
      githubButton.removeAttribute('onclick')
      githubButton.addEventListener('click', () => {
        this.handleSocialLogin('github')
      })
    }

    // Make global functions available for backward compatibility
    window.loginWithGoogle = () => this.handleSocialLogin('google')
    window.loginWithGitHub = () => this.handleSocialLogin('github')
  }

  /**
   * Handle social login
   * @param {string} provider - Social provider (google, github)
   */
  handleSocialLogin(provider) {
    // For now, show coming soon message
    if (window.showNotification) {
      const providerName = provider.charAt(0).toUpperCase() + provider.slice(1)
      window.showNotification(`${providerName} login coming soon!`, 'info')
    }

    // TODO: Implement actual social login
    /*
    const authUrl = `/auth/social/${provider}`
    window.location.href = authUrl
    */

    log('info', `Social login attempted: ${provider}`)
  }

  /**
   * Setup demo credentials notification
   */
  setupDemoCredentials() {
    // Show demo credentials hint after page load
    setTimeout(() => {
      if (window.showNotification) {
        window.showNotification(
          'Demo: Use email "<EMAIL>" and password "password"',
          'info',
          8000
        )
      }
    }, 1000)
  }

  /**
   * Pre-fill demo credentials (for development)
   */
  fillDemoCredentials() {
    const emailInput = document.getElementById('email')
    const passwordInput = document.getElementById('password')
    
    if (emailInput && passwordInput) {
      emailInput.value = '<EMAIL>'
      passwordInput.value = 'password'
      
      if (window.showNotification) {
        window.showNotification('Demo credentials filled', 'info', 3000)
      }
    }
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + Enter to submit form
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        if (this.loginForm) {
          this.loginForm.dispatchEvent(new Event('submit'))
        }
      }

      // Alt + D to fill demo credentials (development only)
      if (e.altKey && e.key === 'd' && process.env.NODE_ENV === 'development') {
        e.preventDefault()
        this.fillDemoCredentials()
      }
    })
  }

  /**
   * Destroy login page
   */
  destroy() {
    // Clean up global functions
    delete window.togglePassword
    delete window.loginWithGoogle
    delete window.loginWithGitHub

    this.isInitialized = false
    log('info', '🔐 Login page destroyed')
  }
}
