/**
 * Home Page Module
 * Specific functionality for the home page
 */

import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { log } from '../core/utils.js'

export class HomePage {
  constructor() {
    this.isInitialized = false
    this.animations = []
  }

  /**
   * Initialize home page
   */
  init() {
    if (this.isInitialized) return

    this.setupHeroAnimations()
    this.setupFeatureCards()
    this.setupStatsCounter()
    this.setupTestimonials()
    this.setupCTA()

    this.isInitialized = true
    log('info', '🏠 Home page initialized')
  }

  /**
   * Setup hero section animations
   */
  setupHeroAnimations() {
    const hero = document.querySelector('.hero-section')
    if (!hero) return

    // Hero title animation
    const title = hero.querySelector('h1')
    if (title) {
      const animation = gsap.fromTo(title,
        {
          opacity: 0,
          y: 50
        },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: 'power2.out',
          delay: 0.2
        }
      )
      this.animations.push(animation)
    }

    // Hero subtitle animation
    const subtitle = hero.querySelector('.hero-subtitle')
    if (subtitle) {
      const animation = gsap.fromTo(subtitle,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
          delay: 0.4
        }
      )
      this.animations.push(animation)
    }

    // Hero CTA buttons
    const buttons = hero.querySelectorAll('.hero-cta')
    if (buttons.length) {
      const animation = gsap.fromTo(buttons,
        {
          opacity: 0,
          y: 20
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.6,
          ease: 'power2.out',
          delay: 0.6,
          stagger: 0.1
        }
      )
      this.animations.push(animation)
    }

    // Hero image/illustration
    const heroImage = hero.querySelector('.hero-image')
    if (heroImage) {
      const animation = gsap.fromTo(heroImage,
        {
          opacity: 0,
          scale: 0.8
        },
        {
          opacity: 1,
          scale: 1,
          duration: 1.2,
          ease: 'power2.out',
          delay: 0.3
        }
      )
      this.animations.push(animation)
    }
  }

  /**
   * Setup feature cards animations
   */
  setupFeatureCards() {
    const featureCards = document.querySelectorAll('.feature-card')
    if (!featureCards.length) return

    featureCards.forEach((card, index) => {
      const animation = gsap.fromTo(card,
        {
          opacity: 0,
          y: 50
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: card,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          },
          delay: index * 0.1
        }
      )
      this.animations.push(animation)

      // Hover effect
      card.addEventListener('mouseenter', () => {
        gsap.to(card, {
          duration: 0.3,
          y: -8,
          scale: 1.02,
          ease: 'power2.out'
        })
      })

      card.addEventListener('mouseleave', () => {
        gsap.to(card, {
          duration: 0.3,
          y: 0,
          scale: 1,
          ease: 'power2.out'
        })
      })
    })
  }

  /**
   * Setup stats counter animation
   */
  setupStatsCounter() {
    const statsSection = document.querySelector('.stats-section')
    if (!statsSection) return

    const statNumbers = statsSection.querySelectorAll('.stat-number')
    
    statNumbers.forEach(stat => {
      const finalValue = parseInt(stat.textContent.replace(/[^\d]/g, ''))
      const suffix = stat.textContent.replace(/[\d]/g, '')
      
      const animation = gsap.fromTo(stat,
        {
          textContent: 0
        },
        {
          textContent: finalValue,
          duration: 2,
          ease: 'power2.out',
          snap: { textContent: 1 },
          scrollTrigger: {
            trigger: stat,
            start: 'top 80%',
            toggleActions: 'play none none none'
          },
          onUpdate: function() {
            stat.textContent = Math.ceil(this.targets()[0].textContent) + suffix
          }
        }
      )
      this.animations.push(animation)
    })
  }

  /**
   * Setup testimonials carousel
   */
  setupTestimonials() {
    const testimonials = document.querySelector('.testimonials-carousel')
    if (!testimonials) return

    const slides = testimonials.querySelectorAll('.testimonial-slide')
    const dots = testimonials.querySelectorAll('.testimonial-dot')
    
    if (!slides.length) return

    let currentSlide = 0
    const slideCount = slides.length

    // Auto-advance testimonials
    const autoAdvance = () => {
      currentSlide = (currentSlide + 1) % slideCount
      this.showTestimonial(currentSlide, slides, dots)
    }

    // Start auto-advance
    const interval = setInterval(autoAdvance, 5000)

    // Pause on hover
    testimonials.addEventListener('mouseenter', () => {
      clearInterval(interval)
    })

    testimonials.addEventListener('mouseleave', () => {
      setInterval(autoAdvance, 5000)
    })

    // Dot navigation
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        currentSlide = index
        this.showTestimonial(currentSlide, slides, dots)
      })
    })

    // Initialize first slide
    this.showTestimonial(0, slides, dots)
  }

  /**
   * Show specific testimonial
   * @param {number} index - Slide index
   * @param {NodeList} slides - Slide elements
   * @param {NodeList} dots - Dot elements
   */
  showTestimonial(index, slides, dots) {
    // Hide all slides
    slides.forEach(slide => {
      gsap.to(slide, {
        opacity: 0,
        x: -50,
        duration: 0.3,
        ease: 'power2.out'
      })
    })

    // Show current slide
    gsap.to(slides[index], {
      opacity: 1,
      x: 0,
      duration: 0.5,
      ease: 'power2.out',
      delay: 0.2
    })

    // Update dots
    dots.forEach((dot, i) => {
      if (i === index) {
        dot.classList.add('active')
      } else {
        dot.classList.remove('active')
      }
    })
  }

  /**
   * Setup call-to-action animations
   */
  setupCTA() {
    const ctaSection = document.querySelector('.cta-section')
    if (!ctaSection) return

    const animation = gsap.fromTo(ctaSection,
      {
        opacity: 0,
        y: 50
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: ctaSection,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      }
    )
    this.animations.push(animation)

    // CTA button pulse effect
    const ctaButton = ctaSection.querySelector('.cta-button')
    if (ctaButton) {
      gsap.to(ctaButton, {
        scale: 1.05,
        duration: 1,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        delay: 2
      })
    }
  }

  /**
   * Destroy home page
   */
  destroy() {
    this.animations.forEach(animation => {
      if (animation.kill) {
        animation.kill()
      }
    })
    this.animations = []
    this.isInitialized = false
    log('info', '🏠 Home page destroyed')
  }
}
