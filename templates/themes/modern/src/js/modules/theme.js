/**
 * Theme Module
 * Handles dark/light mode switching and theme persistence
 */

import { config } from '../core/config.js'
import { log } from '../core/utils.js'

export class ThemeManager {
  constructor() {
    this.currentTheme = null
    this.toggleButton = null
    this.observers = []
    this.init()
  }

  /**
   * Initialize theme system
   */
  init() {
    this.loadTheme()
    this.setupToggleButton()
    this.setupSystemThemeListener()
    this.setupAlpineIntegration()
    log('info', '🎨 Theme system initialized')
  }

  /**
   * Load theme from storage or system preference
   */
  loadTheme() {
    const stored = localStorage.getItem(config.theme.storageKey)
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    if (stored !== null) {
      this.currentTheme = stored === 'true' ? 'dark' : 'light'
    } else {
      this.currentTheme = systemPrefersDark ? 'dark' : 'light'
    }

    this.applyTheme(this.currentTheme)
    log('debug', `Theme loaded: ${this.currentTheme}`)
  }

  /**
   * Apply theme to document
   * @param {string} theme - Theme name ('light' or 'dark')
   */
  applyTheme(theme) {
    const html = document.documentElement
    const body = document.body

    if (theme === 'dark') {
      html.classList.add('dark')
      body.classList.add('dark')
    } else {
      html.classList.remove('dark')
      body.classList.remove('dark')
    }

    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme)

    // Notify observers
    this.notifyObservers(theme)

    // Store preference
    localStorage.setItem(config.theme.storageKey, theme === 'dark')

    this.currentTheme = theme
  }

  /**
   * Update meta theme-color for mobile browsers
   * @param {string} theme - Theme name
   */
  updateMetaThemeColor(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta')
      metaThemeColor.name = 'theme-color'
      document.head.appendChild(metaThemeColor)
    }

    const colors = {
      light: '#ffffff',
      dark: '#1f2937'
    }

    metaThemeColor.content = colors[theme] || colors.light
  }

  /**
   * Setup theme toggle button
   */
  setupToggleButton() {
    // Find existing Alpine.js toggle button - don't create a new one
    this.toggleButton = document.querySelector('[data-theme-toggle]')

    // Don't create a new button - we use Alpine.js button from template
    // The Alpine.js button handles the clicking and visual state

    log('debug', 'Theme toggle setup - using Alpine.js button from template')
  }



  /**
   * Setup system theme change listener
   */
  setupSystemThemeListener() {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    mediaQuery.addEventListener('change', (e) => {
      // Only auto-switch if user hasn't manually set a preference
      const hasManualPreference = localStorage.getItem(config.theme.storageKey) !== null

      if (!hasManualPreference) {
        const newTheme = e.matches ? 'dark' : 'light'
        this.applyTheme(newTheme)
        log('debug', `System theme changed to: ${newTheme}`)
      }
    })
  }

  /**
   * Setup Alpine.js integration
   */
  setupAlpineIntegration() {
    // Make theme state available to Alpine.js
    window.Alpine && window.Alpine.store('theme', {
      current: this.currentTheme,
      isDark: this.currentTheme === 'dark',
      toggle: () => this.toggle(),
      set: (theme) => this.setTheme(theme)
    })
  }

  /**
   * Toggle between light and dark theme
   */
  toggle() {
    const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark'
    this.setTheme(newTheme)
    log('debug', `Theme toggled to: ${newTheme}`)
  }

  /**
   * Set specific theme
   * @param {string} theme - Theme name ('light' or 'dark')
   */
  setTheme(theme) {
    if (theme !== 'light' && theme !== 'dark') {
      log('warn', `Invalid theme: ${theme}. Using 'light' as fallback.`)
      theme = 'light'
    }

    this.applyTheme(theme)

    // Update Alpine.js store if available
    if (window.Alpine?.store) {
      const store = window.Alpine.store('theme')
      if (store) {
        store.current = theme
        store.isDark = theme === 'dark'
      }
    }
  }

  /**
   * Get current theme
   * @returns {string} Current theme name
   */
  getTheme() {
    return this.currentTheme
  }

  /**
   * Check if dark mode is active
   * @returns {boolean} Is dark mode active
   */
  isDark() {
    return this.currentTheme === 'dark'
  }

  /**
   * Add theme change observer
   * @param {Function} callback - Callback function
   */
  addObserver(callback) {
    this.observers.push(callback)
  }

  /**
   * Remove theme change observer
   * @param {Function} callback - Callback function
   */
  removeObserver(callback) {
    const index = this.observers.indexOf(callback)
    if (index > -1) {
      this.observers.splice(index, 1)
    }
  }

  /**
   * Notify all observers of theme change
   * @param {string} theme - New theme
   */
  notifyObservers(theme) {
    this.observers.forEach(callback => {
      try {
        callback(theme)
      } catch (error) {
        log('error', 'Theme observer error:', error)
      }
    })
  }

  /**
   * Get system theme preference
   * @returns {string} System theme preference
   */
  getSystemTheme() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }

  /**
   * Reset to system theme
   */
  resetToSystem() {
    localStorage.removeItem(config.theme.storageKey)
    const systemTheme = this.getSystemTheme()
    this.setTheme(systemTheme)
    log('debug', `Theme reset to system: ${systemTheme}`)
  }

  /**
   * Destroy theme manager
   */
  destroy() {
    this.observers = []
    // No need to remove event listeners - Alpine.js handles the button
    log('info', '🎨 Theme system destroyed')
  }
}
