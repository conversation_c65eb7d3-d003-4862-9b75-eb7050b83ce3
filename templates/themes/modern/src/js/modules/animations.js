/**
 * Animation Module
 * Handles GSAP animations and scroll triggers
 */

import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { config } from '../core/config.js'
import { log } from '../core/utils.js'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger)

export class AnimationManager {
  constructor() {
    this.isInitialized = false
    this.animations = new Map()
  }

  /**
   * Initialize animation system
   */
  init() {
    if (this.isInitialized) return

    this.setupGSAP()
    this.setupPageTransitions()
    this.setupScrollAnimations()
    this.setupInteractiveElements()

    this.isInitialized = true
    log('info', '🎬 Animation system initialized')
  }

  /**
   * Setup GSAP defaults
   */
  setupGSAP() {
    gsap.defaults({
      duration: config.animations.defaultDuration,
      ease: config.animations.defaultEase
    })

    // Page load animation
    if (config.features.pageTransitions) {
      gsap.from(config.selectors.pageTransition, {
        duration: config.animations.pageTransition.duration,
        y: 30,
        opacity: 0,
        ease: config.animations.pageTransition.ease
      })

      // Navigation animation
      gsap.from('nav', {
        duration: config.animations.navigation.duration,
        y: -100,
        opacity: 0,
        ease: config.animations.navigation.ease,
        delay: config.animations.navigation.delay
      })
    }
  }

  /**
   * Setup page transitions
   */
  setupPageTransitions() {
    if (!config.features.pageTransitions) return

    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href]')
      if (link && link.hostname === window.location.hostname && !link.hasAttribute('target')) {
        e.preventDefault()

        gsap.to(config.selectors.pageTransition, {
          duration: 0.3,
          opacity: 0,
          y: -20,
          ease: 'power2.in',
          onComplete: () => {
            window.location.href = link.href
          }
        })
      }
    })
  }

  /**
   * Setup scroll animations
   */
  setupScrollAnimations() {
    if (!config.features.scrollAnimations) return

    // Animate elements on scroll
    gsap.utils.toArray(config.selectors.animateOnScroll).forEach(element => {
      const animation = gsap.fromTo(element,
        {
          opacity: 0,
          y: 50
        },
        {
          opacity: 1,
          y: 0,
          duration: config.animations.scroll.duration,
          ease: config.animations.scroll.ease,
          scrollTrigger: {
            trigger: element,
            start: config.animations.scroll.trigger.start,
            end: config.animations.scroll.trigger.end,
            toggleActions: config.animations.scroll.trigger.toggleActions
          }
        }
      )

      this.animations.set(element, animation)
    })

    // Parallax effect for hero sections
    gsap.utils.toArray(config.selectors.parallax).forEach(element => {
      const animation = gsap.to(element, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: element,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      })

      this.animations.set(element, animation)
    })

    // Stagger animations for lists
    gsap.utils.toArray(config.selectors.staggerChildren).forEach(container => {
      const children = container.children
      const animation = gsap.fromTo(children,
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: config.animations.stagger.duration,
          stagger: config.animations.stagger.stagger,
          ease: config.animations.stagger.ease,
          scrollTrigger: {
            trigger: container,
            start: 'top 80%'
          }
        }
      )

      this.animations.set(container, animation)
    })
  }

  /**
   * Setup interactive element animations
   */
  setupInteractiveElements() {
    if (!config.features.hoverEffects) return

    // Button hover animations
    document.querySelectorAll(config.selectors.buttons).forEach(button => {
      button.addEventListener('mouseenter', () => {
        gsap.to(button, {
          duration: config.animations.hover.duration,
          scale: 1.05,
          ease: config.animations.hover.ease
        })
      })

      button.addEventListener('mouseleave', () => {
        gsap.to(button, {
          duration: config.animations.hover.duration,
          scale: 1,
          ease: config.animations.hover.ease
        })
      })
    })

    // Card hover animations
    document.querySelectorAll(config.selectors.cards).forEach(card => {
      card.addEventListener('mouseenter', () => {
        gsap.to(card, {
          duration: config.animations.hover.duration,
          y: -8,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          ease: config.animations.hover.ease
        })
      })

      card.addEventListener('mouseleave', () => {
        gsap.to(card, {
          duration: config.animations.hover.duration,
          y: 0,
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          ease: config.animations.hover.ease
        })
      })
    })

    // Navigation link animations
    document.querySelectorAll(config.selectors.navLinks).forEach(link => {
      link.addEventListener('mouseenter', () => {
        gsap.to(link, {
          duration: config.animations.hover.duration,
          scale: 1.05,
          ease: config.animations.hover.ease
        })
      })

      link.addEventListener('mouseleave', () => {
        gsap.to(link, {
          duration: config.animations.hover.duration,
          scale: 1,
          ease: config.animations.hover.ease
        })
      })
    })
  }

  /**
   * Create custom animation
   * @param {Element} element - Target element
   * @param {Object} fromVars - From properties
   * @param {Object} toVars - To properties
   * @returns {gsap.core.Timeline} GSAP animation
   */
  createAnimation(element, fromVars, toVars) {
    const animation = gsap.fromTo(element, fromVars, toVars)
    this.animations.set(element, animation)
    return animation
  }

  /**
   * Play animation
   * @param {Element} element - Target element
   */
  playAnimation(element) {
    const animation = this.animations.get(element)
    if (animation) {
      animation.play()
    }
  }

  /**
   * Pause animation
   * @param {Element} element - Target element
   */
  pauseAnimation(element) {
    const animation = this.animations.get(element)
    if (animation) {
      animation.pause()
    }
  }

  /**
   * Kill animation
   * @param {Element} element - Target element
   */
  killAnimation(element) {
    const animation = this.animations.get(element)
    if (animation) {
      animation.kill()
      this.animations.delete(element)
    }
  }

  /**
   * Refresh ScrollTrigger
   */
  refreshScrollTrigger() {
    ScrollTrigger.refresh()
  }

  /**
   * Kill all animations
   */
  killAll() {
    this.animations.forEach(animation => animation.kill())
    this.animations.clear()
    ScrollTrigger.killAll()
  }

  /**
   * Destroy animation manager
   */
  destroy() {
    this.killAll()
    this.isInitialized = false
    log('info', '🎬 Animation system destroyed')
  }
}
