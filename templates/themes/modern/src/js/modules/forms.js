/**
 * Forms Module
 * Handles form validation, submission, and enhancement
 */

import { api, apiHelpers } from '../core/api.js'
import { log, debounce } from '../core/utils.js'

export class FormManager {
  constructor() {
    this.forms = new Map()
    this.validators = new Map()
    this.init()
  }

  /**
   * Initialize form system
   */
  init() {
    this.setupFormHandlers()
    this.setupValidators()
    this.setupEnhancements()
    log('info', '📝 Form system initialized')
  }

  /**
   * Setup form handlers
   */
  setupFormHandlers() {
    document.addEventListener('submit', (e) => {
      const form = e.target.closest('form')
      if (form && form.hasAttribute('data-ajax')) {
        e.preventDefault()
        this.handleAjaxSubmit(form)
      }
    })

    // Real-time validation
    document.addEventListener('input', debounce((e) => {
      const input = e.target
      if (input.form && input.hasAttribute('data-validate')) {
        this.validateField(input)
      }
    }, 300))

    // Blur validation
    document.addEventListener('blur', (e) => {
      const input = e.target
      if (input.form && input.hasAttribute('data-validate')) {
        this.validateField(input)
      }
    }, true)
  }

  /**
   * Setup built-in validators
   */
  setupValidators() {
    // Required field validator
    this.addValidator('required', (value, element) => {
      const isValid = value.trim() !== ''
      return {
        isValid,
        message: isValid ? '' : 'This field is required'
      }
    })

    // Email validator
    this.addValidator('email', (value, element) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const isValid = !value || emailRegex.test(value)
      return {
        isValid,
        message: isValid ? '' : 'Please enter a valid email address'
      }
    })

    // Minimum length validator
    this.addValidator('minlength', (value, element) => {
      const minLength = parseInt(element.getAttribute('data-minlength') || '0')
      const isValid = !value || value.length >= minLength
      return {
        isValid,
        message: isValid ? '' : `Minimum ${minLength} characters required`
      }
    })

    // Maximum length validator
    this.addValidator('maxlength', (value, element) => {
      const maxLength = parseInt(element.getAttribute('data-maxlength') || '999999')
      const isValid = !value || value.length <= maxLength
      return {
        isValid,
        message: isValid ? '' : `Maximum ${maxLength} characters allowed`
      }
    })

    // Pattern validator
    this.addValidator('pattern', (value, element) => {
      const pattern = element.getAttribute('data-pattern')
      if (!pattern || !value) return { isValid: true, message: '' }
      
      const regex = new RegExp(pattern)
      const isValid = regex.test(value)
      return {
        isValid,
        message: isValid ? '' : 'Please match the required format'
      }
    })

    // Password strength validator
    this.addValidator('password-strength', (value, element) => {
      if (!value) return { isValid: true, message: '' }
      
      const minLength = 8
      const hasUpper = /[A-Z]/.test(value)
      const hasLower = /[a-z]/.test(value)
      const hasNumber = /\d/.test(value)
      const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value)
      
      const isValid = value.length >= minLength && hasUpper && hasLower && hasNumber
      let message = ''
      
      if (!isValid) {
        const missing = []
        if (value.length < minLength) missing.push(`${minLength} characters`)
        if (!hasUpper) missing.push('uppercase letter')
        if (!hasLower) missing.push('lowercase letter')
        if (!hasNumber) missing.push('number')
        
        message = `Password must contain: ${missing.join(', ')}`
      }
      
      return { isValid, message }
    })

    // Confirm password validator
    this.addValidator('confirm-password', (value, element) => {
      const passwordField = element.form.querySelector('[type="password"]:not([data-validate*="confirm-password"])')
      if (!passwordField || !value) return { isValid: true, message: '' }
      
      const isValid = value === passwordField.value
      return {
        isValid,
        message: isValid ? '' : 'Passwords do not match'
      }
    })
  }

  /**
   * Setup form enhancements
   */
  setupEnhancements() {
    // Auto-resize textareas
    document.querySelectorAll('textarea[data-auto-resize]').forEach(textarea => {
      this.setupAutoResize(textarea)
    })

    // Character counters
    document.querySelectorAll('[data-char-counter]').forEach(input => {
      this.setupCharCounter(input)
    })

    // File upload previews
    document.querySelectorAll('input[type="file"][data-preview]').forEach(input => {
      this.setupFilePreview(input)
    })
  }

  /**
   * Handle AJAX form submission
   * @param {HTMLFormElement} form - Form element
   */
  async handleAjaxSubmit(form) {
    const formData = new FormData(form)
    const submitButton = form.querySelector('[type="submit"]')
    const originalText = submitButton?.textContent
    
    try {
      // Validate form before submission
      if (!this.validateForm(form)) {
        return
      }

      // Show loading state
      if (submitButton) {
        submitButton.disabled = true
        submitButton.innerHTML = `
          <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Submitting...
        `
      }

      // Convert FormData to JSON if needed
      const contentType = form.getAttribute('data-content-type') || 'json'
      let requestData = formData
      
      if (contentType === 'json') {
        const jsonData = {}
        formData.forEach((value, key) => {
          jsonData[key] = value
        })
        requestData = jsonData
      }

      // Submit form
      const method = form.method.toLowerCase()
      const endpoint = form.action.replace(window.location.origin, '')
      
      let response
      switch (method) {
        case 'post':
          response = contentType === 'json' 
            ? await api.post(endpoint, requestData)
            : await api.upload(endpoint, requestData)
          break
        case 'put':
          response = await api.put(endpoint, requestData)
          break
        case 'delete':
          response = await api.delete(endpoint)
          break
        default:
          response = await api.get(endpoint, requestData)
      }

      // Handle success
      this.handleSubmitSuccess(form, response)
      
    } catch (error) {
      // Handle error
      this.handleSubmitError(form, error)
    } finally {
      // Reset button state
      if (submitButton && originalText) {
        submitButton.disabled = false
        submitButton.textContent = originalText
      }
    }
  }

  /**
   * Handle successful form submission
   * @param {HTMLFormElement} form - Form element
   * @param {Object} response - API response
   */
  handleSubmitSuccess(form, response) {
    // Show success notification
    if (window.showNotification) {
      window.showNotification(
        response.message || 'Form submitted successfully!',
        'success'
      )
    }

    // Reset form if specified
    if (form.hasAttribute('data-reset-on-success')) {
      form.reset()
      this.clearValidation(form)
    }

    // Redirect if specified
    const redirectUrl = form.getAttribute('data-redirect') || response.redirect
    if (redirectUrl) {
      setTimeout(() => {
        window.location.href = redirectUrl
      }, 1500)
    }

    // Trigger custom event
    form.dispatchEvent(new CustomEvent('form:success', {
      detail: { response }
    }))

    log('debug', 'Form submitted successfully:', response)
  }

  /**
   * Handle form submission error
   * @param {HTMLFormElement} form - Form element
   * @param {Error} error - Error object
   */
  handleSubmitError(form, error) {
    // Handle validation errors
    if (error.message.includes('422') && error.errors) {
      this.showValidationErrors(form, error.errors)
    } else {
      // Show general error notification
      apiHelpers.handleError(error, window.showNotification)
    }

    // Trigger custom event
    form.dispatchEvent(new CustomEvent('form:error', {
      detail: { error }
    }))

    log('error', 'Form submission error:', error)
  }

  /**
   * Validate entire form
   * @param {HTMLFormElement} form - Form element
   * @returns {boolean} Is form valid
   */
  validateForm(form) {
    const inputs = form.querySelectorAll('[data-validate]')
    let isValid = true

    inputs.forEach(input => {
      if (!this.validateField(input)) {
        isValid = false
      }
    })

    return isValid
  }

  /**
   * Validate single field
   * @param {HTMLInputElement} input - Input element
   * @returns {boolean} Is field valid
   */
  validateField(input) {
    const validators = input.getAttribute('data-validate').split(' ')
    const value = input.value
    let isValid = true
    let errorMessage = ''

    for (const validatorName of validators) {
      const validator = this.validators.get(validatorName)
      if (validator) {
        const result = validator(value, input)
        if (!result.isValid) {
          isValid = false
          errorMessage = result.message
          break
        }
      }
    }

    this.showFieldValidation(input, isValid, errorMessage)
    return isValid
  }

  /**
   * Show field validation state
   * @param {HTMLInputElement} input - Input element
   * @param {boolean} isValid - Is field valid
   * @param {string} message - Error message
   */
  showFieldValidation(input, isValid, message) {
    const container = input.closest('.form-group') || input.parentElement
    let errorElement = container.querySelector('.form-error-message')

    // Remove existing validation classes
    input.classList.remove('form-input-error', 'form-input-success')
    
    if (isValid) {
      input.classList.add('form-input-success')
      if (errorElement) {
        errorElement.remove()
      }
    } else {
      input.classList.add('form-input-error')
      
      if (!errorElement) {
        errorElement = document.createElement('div')
        errorElement.className = 'form-error-message mt-1 text-sm text-red-600 dark:text-red-400'
        container.appendChild(errorElement)
      }
      
      errorElement.textContent = message
    }
  }

  /**
   * Clear form validation
   * @param {HTMLFormElement} form - Form element
   */
  clearValidation(form) {
    const inputs = form.querySelectorAll('[data-validate]')
    inputs.forEach(input => {
      input.classList.remove('form-input-error', 'form-input-success')
      const container = input.closest('.form-group') || input.parentElement
      const errorElement = container.querySelector('.form-error-message')
      if (errorElement) {
        errorElement.remove()
      }
    })
  }

  /**
   * Setup auto-resize for textarea
   * @param {HTMLTextAreaElement} textarea - Textarea element
   */
  setupAutoResize(textarea) {
    const resize = () => {
      textarea.style.height = 'auto'
      textarea.style.height = textarea.scrollHeight + 'px'
    }

    textarea.addEventListener('input', resize)
    resize() // Initial resize
  }

  /**
   * Setup character counter
   * @param {HTMLInputElement} input - Input element
   */
  setupCharCounter(input) {
    const maxLength = input.maxLength || parseInt(input.getAttribute('data-max-length'))
    if (!maxLength) return

    const counter = document.createElement('div')
    counter.className = 'form-help-text text-right'
    
    const updateCounter = () => {
      const remaining = maxLength - input.value.length
      counter.textContent = `${remaining} characters remaining`
      
      if (remaining < 10) {
        counter.classList.add('text-red-500')
      } else {
        counter.classList.remove('text-red-500')
      }
    }

    input.addEventListener('input', updateCounter)
    input.parentElement.appendChild(counter)
    updateCounter()
  }

  /**
   * Setup file preview
   * @param {HTMLInputElement} input - File input element
   */
  setupFilePreview(input) {
    const previewContainer = document.createElement('div')
    previewContainer.className = 'mt-2 grid grid-cols-3 gap-2'
    input.parentElement.appendChild(previewContainer)

    input.addEventListener('change', (e) => {
      previewContainer.innerHTML = ''
      
      Array.from(e.target.files).forEach(file => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader()
          reader.onload = (e) => {
            const img = document.createElement('img')
            img.src = e.target.result
            img.className = 'w-full h-20 object-cover rounded-lg'
            previewContainer.appendChild(img)
          }
          reader.readAsDataURL(file)
        }
      })
    })
  }

  /**
   * Add custom validator
   * @param {string} name - Validator name
   * @param {Function} validator - Validator function
   */
  addValidator(name, validator) {
    this.validators.set(name, validator)
  }

  /**
   * Remove validator
   * @param {string} name - Validator name
   */
  removeValidator(name) {
    this.validators.delete(name)
  }

  /**
   * Destroy form manager
   */
  destroy() {
    this.forms.clear()
    this.validators.clear()
    log('info', '📝 Form system destroyed')
  }
}
