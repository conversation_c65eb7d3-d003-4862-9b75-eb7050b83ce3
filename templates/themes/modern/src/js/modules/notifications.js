/**
 * Notification Module
 * Handles toast notifications and alerts
 */

import { gsap } from 'gsap'
import { config } from '../core/config.js'
import { generateId, log } from '../core/utils.js'

export class NotificationManager {
  constructor() {
    this.notifications = new Map()
    this.container = null
    this.maxVisible = config.notifications.maxVisible
    this.init()
  }

  /**
   * Initialize notification system
   */
  init() {
    this.createContainer()
    this.setupGlobalFunction()
    log('info', '🔔 Notification system initialized')
  }

  /**
   * Create notification container
   */
  createContainer() {
    this.container = document.createElement('div')
    this.container.id = 'notification-container'
    this.container.className = 'fixed top-4 right-4 z-50 space-y-2'
    document.body.appendChild(this.container)
  }

  /**
   * Setup global notification function
   */
  setupGlobalFunction() {
    window.showNotification = (message, type = 'info', duration = config.notifications.defaultDuration) => {
      return this.show(message, type, duration)
    }
  }

  /**
   * Show notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, warning, info)
   * @param {number} duration - Duration in milliseconds
   * @param {Object} options - Additional options
   * @returns {string} Notification ID
   */
  show(message, type = 'info', duration = config.notifications.defaultDuration, options = {}) {
    if (!config.features.notifications) return

    const id = generateId('notification')
    const notification = this.createElement(id, message, type, options)
    
    // Add to container
    this.container.appendChild(notification)
    this.notifications.set(id, {
      element: notification,
      type,
      message,
      duration,
      timestamp: Date.now()
    })

    // Animate in
    this.animateIn(notification)

    // Auto remove
    if (duration > 0) {
      setTimeout(() => {
        this.remove(id)
      }, duration)
    }

    // Limit visible notifications
    this.limitVisible()

    log('debug', `Notification shown: ${type} - ${message}`)
    return id
  }

  /**
   * Create notification element
   * @param {string} id - Notification ID
   * @param {string} message - Message text
   * @param {string} type - Notification type
   * @param {Object} options - Additional options
   * @returns {Element} Notification element
   */
  createElement(id, message, type, options = {}) {
    const notification = document.createElement('div')
    notification.id = id
    notification.className = `notification notification-${type} max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`
    
    const icon = this.getIcon(type)
    const title = options.title || this.getDefaultTitle(type)
    
    notification.innerHTML = `
      <div class="p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            ${icon}
          </div>
          <div class="ml-3 flex-1">
            ${title ? `<p class="text-sm font-medium text-gray-900 dark:text-gray-100">${title}</p>` : ''}
            <p class="text-sm text-gray-500 dark:text-gray-400 ${title ? 'mt-1' : ''}">
              ${message}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button class="notification-close inline-flex text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 rounded-md">
              <span class="sr-only">Close</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
        ${options.actions ? this.createActions(options.actions) : ''}
      </div>
    `

    // Add close event listener
    notification.querySelector('.notification-close').addEventListener('click', () => {
      this.remove(id)
    })

    return notification
  }

  /**
   * Create action buttons
   * @param {Array} actions - Action definitions
   * @returns {string} Actions HTML
   */
  createActions(actions) {
    const actionsHtml = actions.map(action => 
      `<button class="notification-action text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 transition-colors mr-4" data-action="${action.id}">
        ${action.label}
      </button>`
    ).join('')

    return `<div class="mt-3">${actionsHtml}</div>`
  }

  /**
   * Get notification icon
   * @param {string} type - Notification type
   * @returns {string} Icon HTML
   */
  getIcon(type) {
    const icons = {
      success: '<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
      error: '<svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
      warning: '<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
      info: '<svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    }
    return icons[type] || icons.info
  }

  /**
   * Get default title for notification type
   * @param {string} type - Notification type
   * @returns {string} Default title
   */
  getDefaultTitle(type) {
    const titles = {
      success: 'Success',
      error: 'Error',
      warning: 'Warning',
      info: 'Information'
    }
    return titles[type] || ''
  }

  /**
   * Animate notification in
   * @param {Element} element - Notification element
   */
  animateIn(element) {
    gsap.fromTo(element,
      config.notifications.animations.enter,
      config.notifications.animations.enterActive
    )
  }

  /**
   * Animate notification out
   * @param {Element} element - Notification element
   * @returns {Promise} Animation promise
   */
  animateOut(element) {
    return new Promise(resolve => {
      gsap.to(element, {
        ...config.notifications.animations.exit,
        onComplete: resolve
      })
    })
  }

  /**
   * Remove notification
   * @param {string} id - Notification ID
   */
  async remove(id) {
    const notification = this.notifications.get(id)
    if (!notification) return

    await this.animateOut(notification.element)
    
    notification.element.remove()
    this.notifications.delete(id)
    
    log('debug', `Notification removed: ${id}`)
  }

  /**
   * Limit visible notifications
   */
  limitVisible() {
    const notifications = Array.from(this.notifications.entries())
    if (notifications.length > this.maxVisible) {
      const toRemove = notifications
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, notifications.length - this.maxVisible)
      
      toRemove.forEach(([id]) => this.remove(id))
    }
  }

  /**
   * Clear all notifications
   */
  clearAll() {
    const ids = Array.from(this.notifications.keys())
    ids.forEach(id => this.remove(id))
  }

  /**
   * Get notification count by type
   * @param {string} type - Notification type
   * @returns {number} Count
   */
  getCount(type = null) {
    if (!type) return this.notifications.size
    
    return Array.from(this.notifications.values())
      .filter(notification => notification.type === type).length
  }

  /**
   * Destroy notification manager
   */
  destroy() {
    this.clearAll()
    if (this.container) {
      this.container.remove()
    }
    delete window.showNotification
    log('info', '🔔 Notification system destroyed')
  }
}
