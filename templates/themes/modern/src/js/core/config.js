/**
 * Application Configuration
 * Central configuration for the Modern Theme
 */

export const config = {
  // Animation settings
  animations: {
    defaultDuration: 0.6,
    defaultEase: "power2.out",
    pageTransition: {
      duration: 0.8,
      ease: 'power2.out'
    },
    navigation: {
      duration: 1,
      delay: 0.2,
      ease: 'power2.out'
    },
    scroll: {
      duration: 1,
      ease: 'power2.out',
      trigger: {
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse'
      }
    },
    hover: {
      duration: 0.3,
      ease: 'power2.out'
    },
    stagger: {
      duration: 0.6,
      stagger: 0.1,
      ease: 'power2.out'
    }
  },

  // Notification settings
  notifications: {
    defaultDuration: 5000,
    position: 'top-right',
    maxVisible: 5,
    animations: {
      enter: { x: 400, opacity: 0 },
      enterActive: { x: 0, opacity: 1, duration: 0.5, ease: 'power2.out' },
      exit: { x: 400, opacity: 0, duration: 0.3, ease: 'power2.in' }
    }
  },

  // Theme settings
  theme: {
    defaultMode: import.meta.env.VITE_DEFAULT_THEME || 'light',
    storageKey: import.meta.env.VITE_THEME_STORAGE_KEY || 'darkMode',
    transitionDuration: 300
  },

  // API settings
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
    retries: 3
  },

  // Debug settings
  debug: {
    enabled: import.meta.env.VITE_DEBUG_ENABLED === 'true' || import.meta.env.DEV,
    logLevel: import.meta.env.VITE_DEBUG_LOG_LEVEL || 'info',
    showPerformance: import.meta.env.VITE_SHOW_PERFORMANCE === 'true' || import.meta.env.DEV
  },

  // Feature flags
  features: {
    pageTransitions: import.meta.env.VITE_ENABLE_ANIMATIONS !== 'false',
    scrollAnimations: import.meta.env.VITE_ENABLE_ANIMATIONS !== 'false',
    hoverEffects: import.meta.env.VITE_ENABLE_ANIMATIONS !== 'false',
    notifications: import.meta.env.VITE_ENABLE_NOTIFICATIONS !== 'false',
    darkMode: import.meta.env.VITE_ENABLE_DARK_MODE !== 'false',
    devTools: import.meta.env.VITE_ENABLE_DEV_TOOLS === 'true' || import.meta.env.DEV
  },

  // Selectors
  selectors: {
    pageTransition: '.page-transition',
    animateOnScroll: '.animate-on-scroll',
    parallax: '.parallax',
    staggerChildren: '.stagger-children',
    buttons: '.btn',
    cards: '.card-hover',
    navLinks: '.nav-link',
    notifications: '.notification'
  }
}

export default config
