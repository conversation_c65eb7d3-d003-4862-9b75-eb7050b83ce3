/**
 * API Helper Functions
 * Centralized API communication layer
 */

import { config } from './config.js'
import { log } from './utils.js'

/**
 * API Client Class
 */
export class ApiClient {
  constructor(baseUrl = config.api.baseUrl) {
    this.baseUrl = baseUrl
    this.timeout = config.api.timeout
    this.retries = config.api.retries
  }

  /**
   * Make HTTP request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise} Response promise
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: this.timeout
    }

    const requestOptions = { ...defaultOptions, ...options }

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (csrfToken) {
      requestOptions.headers['X-CSRF-TOKEN'] = csrfToken
    }

    log('debug', `API Request: ${requestOptions.method || 'GET'} ${url}`, requestOptions)

    try {
      const response = await this.fetchWithTimeout(url, requestOptions)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      log('debug', `API Response: ${url}`, data)
      
      return data
    } catch (error) {
      log('error', `API Error: ${url}`, error)
      throw error
    }
  }

  /**
   * Fetch with timeout
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise} Fetch promise
   */
  async fetchWithTimeout(url, options) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), options.timeout)

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      })
      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      if (error.name === 'AbortError') {
        throw new Error('Request timeout')
      }
      throw error
    }
  }

  /**
   * GET request
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise} Response promise
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `${endpoint}?${queryString}` : endpoint
    
    return this.request(url, { method: 'GET' })
  }

  /**
   * POST request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request body
   * @returns {Promise} Response promise
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * PUT request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request body
   * @returns {Promise} Response promise
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  /**
   * DELETE request
   * @param {string} endpoint - API endpoint
   * @returns {Promise} Response promise
   */
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' })
  }

  /**
   * Upload file
   * @param {string} endpoint - API endpoint
   * @param {FormData} formData - Form data with file
   * @returns {Promise} Response promise
   */
  async upload(endpoint, formData) {
    return this.request(endpoint, {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set Content-Type for FormData
    })
  }
}

/**
 * Default API client instance
 */
export const api = new ApiClient()

/**
 * API endpoints
 */
export const endpoints = {
  // Authentication
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    register: '/auth/register',
    refresh: '/auth/refresh',
    me: '/auth/me'
  },

  // Users
  users: {
    list: '/users',
    create: '/users',
    read: (id) => `/users/${id}`,
    update: (id) => `/users/${id}`,
    delete: (id) => `/users/${id}`
  },

  // Articles
  articles: {
    list: '/articles',
    create: '/articles',
    read: (id) => `/articles/${id}`,
    update: (id) => `/articles/${id}`,
    delete: (id) => `/articles/${id}`
  },

  // Guitar tools
  guitar: {
    scales: '/guitar/scales',
    chords: '/guitar/chords',
    fretboard: '/guitar/fretboard/generate'
  },

  // System
  system: {
    health: '/v1/health',
    info: '/system/info'
  }
}

/**
 * API helper functions
 */
export const apiHelpers = {
  /**
   * Handle API errors
   * @param {Error} error - Error object
   * @param {Function} showNotification - Notification function
   */
  handleError(error, showNotification) {
    log('error', 'API Error:', error)
    
    let message = 'An unexpected error occurred'
    
    if (error.message.includes('timeout')) {
      message = 'Request timeout. Please try again.'
    } else if (error.message.includes('404')) {
      message = 'Resource not found.'
    } else if (error.message.includes('401')) {
      message = 'Unauthorized. Please log in.'
    } else if (error.message.includes('403')) {
      message = 'Access denied.'
    } else if (error.message.includes('500')) {
      message = 'Server error. Please try again later.'
    }
    
    if (showNotification) {
      showNotification(message, 'error')
    }
  },

  /**
   * Retry API request
   * @param {Function} requestFn - Request function
   * @param {number} retries - Number of retries
   * @param {number} delay - Delay between retries
   * @returns {Promise} Response promise
   */
  async retry(requestFn, retries = config.api.retries, delay = 1000) {
    try {
      return await requestFn()
    } catch (error) {
      if (retries > 0) {
        log('warn', `Retrying request. ${retries} attempts remaining.`)
        await new Promise(resolve => setTimeout(resolve, delay))
        return this.retry(requestFn, retries - 1, delay * 2)
      }
      throw error
    }
  }
}
