import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // Základná konfigurácia
  root: resolve(__dirname, 'src'),
  base: '/dist/',

  // Konfigurácia servera pre vývoj
  server: {
    host: 'localhost',
    port: 3000,
    open: false,
    cors: true,
    hmr: {
      overlay: true
    }
  },

  // Definovanie aliasov pre modulárnu štruktúru
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@core': resolve(__dirname, 'src/js/core'),
      '@modules': resolve(__dirname, 'src/js/modules'),
      '@pages': resolve(__dirname, 'src/js/pages'),
      '@components': resolve(__dirname, 'src/js/components'),
      '@utils': resolve(__dirname, 'src/js/utils'),
      '@css': resolve(__dirname, 'src/css'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },

  // Konfigurácia buildu
  build: {
    // Výstupný adresár pre skompilované súbory
    outDir: resolve(__dirname, '../../../public/assets/themes/modern'),
    emptyOutDir: true,
    manifest: true,
    sourcemap: process.env.NODE_ENV === 'development',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/js/app.js'),
        style: resolve(__dirname, 'src/css/app.css'),
      },
      output: {
        // Nastavenie názvov súborov bez hash
        entryFileNames: 'js/[name].js',
        chunkFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(png|jpe?g|gif|svg|webp)$/.test(assetInfo.name)) {
            return `images/[name].[ext]`;
          }
          if (/\.(css)$/.test(assetInfo.name)) {
            return `css/[name].[ext]`;
          }
          if (/\.(woff|woff2|eot|ttf|otf)$/.test(assetInfo.name)) {
            return `fonts/[name].[ext]`;
          }
          return `[name].[ext]`;
        },
        // Optimalizované code splitting
        manualChunks: {
          // Vendor libraries
          'vendor-gsap': ['gsap'],
          'vendor-alpine': ['alpinejs'],

          // Core modules
          'core': [
            './src/js/core/config.js',
            './src/js/core/utils.js',
            './src/js/core/api.js'
          ],

          // Feature modules
          'modules-ui': [
            './src/js/modules/animations.js',
            './src/js/modules/notifications.js',
            './src/js/modules/theme.js'
          ],

          'modules-forms': [
            './src/js/modules/forms.js'
          ],

          // Legacy components
          'components': [
            './src/js/components/MarkPanel.js',
            './src/js/components/DemoAnimations.js',
            './src/js/components/TinyMCEEditor.js',
            './src/js/components/DevTools.js'
          ]
        }
      },
    },
  },

  // Konfigurácia assetsInclude pre explicitné zahrnutie obrázkov
  assetsInclude: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg', '**/*.webp', '**/*.ico'],

  // CSS konfigurácia
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@css/base/variables.css";`
      }
    }
  },

  // Optimalizácie pre development
  optimizeDeps: {
    include: [
      'gsap',
      'gsap/ScrollTrigger',
      'alpinejs'
    ],
    exclude: [
      // Exclude heavy modules from pre-bundling in dev
    ]
  },

  // Environment variables
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    __PROD__: process.env.NODE_ENV === 'production',
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0')
  },

  // Plugin konfigurácia
  plugins: [
    // Môžeme pridať pluginy pre:
    // - Bundle analyzer
    // - PWA
    // - Legacy browser support
  ],

  // Preview konfigurácia (pre production preview)
  preview: {
    host: 'localhost',
    port: 4173,
    cors: true
  }
});
