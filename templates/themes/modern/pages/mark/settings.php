<?php
// Settings content will be included in mark layout
$pageTitle = $title ?? 'Settings';
$currentRoute = 'mark-settings';
?>

<!-- Settings -->
<div class="space-y-6">

    <!-- Header -->
    <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Nastavenia systému</h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Spravujte konfiguráciu a nastavenia aplikácie</p>
    </div>

    <!-- Settings Tabs -->
    <div class="glass rounded-xl overflow-hidden" x-data="{ activeTab: 'general' }">

        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 dark:border-gray-700">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button @click="activeTab = 'general'"
                        :class="activeTab === 'general' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'"
                        class="py-4 px-1 border-b-2 font-medium text-sm">
                    Všeobecné
                </button>
                <button @click="activeTab = 'security'"
                        :class="activeTab === 'security' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'"
                        class="py-4 px-1 border-b-2 font-medium text-sm">
                    Bezpečnosť
                </button>
                <button @click="activeTab = 'email'"
                        :class="activeTab === 'email' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'"
                        class="py-4 px-1 border-b-2 font-medium text-sm">
                    Email
                </button>
                <button @click="activeTab = 'appearance'"
                        :class="activeTab === 'appearance' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'"
                        class="py-4 px-1 border-b-2 font-medium text-sm">
                    Vzhľad
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">

            <!-- General Settings -->
            <div x-show="activeTab === 'general'" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Názov aplikácie</label>
                        <input type="text" value="Fretboard Pattern Visualizer" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Popis aplikácie</label>
                        <input type="text" value="Guitar learning tools and fretboard visualization" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Jazyk</label>
                        <select class="form-select">
                            <option value="sk">Slovenčina</option>
                            <option value="en">English</option>
                            <option value="cs">Čeština</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Časová zóna</label>
                        <select class="form-select">
                            <option value="Europe/Bratislava">Europe/Bratislava</option>
                            <option value="Europe/Prague">Europe/Prague</option>
                            <option value="UTC">UTC</option>
                        </select>
                    </div>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Registrácia používateľov</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Povoliť registráciu nových používateľov</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" checked class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                    </label>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Údržbový režim</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Dočasne zakázať prístup k aplikácii</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                    </label>
                </div>
            </div>

            <!-- Security Settings -->
            <div x-show="activeTab === 'security'" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Minimálna dĺžka hesla</label>
                        <input type="number" value="8" min="6" max="32" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Session timeout (minúty)</label>
                        <input type="number" value="60" min="15" max="1440" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max. pokusov o prihlásenie</label>
                        <input type="number" value="5" min="3" max="10" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Blokovanie IP (minúty)</label>
                        <input type="number" value="15" min="5" max="60" class="form-input">
                    </div>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Dvojfaktorová autentifikácia</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Vyžadovať 2FA pre admin účty</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                    </label>
                </div>
            </div>

            <!-- Email Settings -->
            <div x-show="activeTab === 'email'" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Server</label>
                        <input type="text" placeholder="smtp.gmail.com" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Port</label>
                        <input type="number" value="587" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email odosielateľa</label>
                        <input type="email" placeholder="<EMAIL>" class="form-input">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meno odosielateľa</label>
                        <input type="text" placeholder="Fretboard Visualizer" class="form-input">
                    </div>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Email notifikácie</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Posielať email notifikácie administrátorom</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" checked class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                    </label>
                </div>
            </div>

            <!-- Appearance Settings -->
            <div x-show="activeTab === 'appearance'" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Predvolený motív</label>
                        <select class="form-select">
                            <option value="light">Svetlý</option>
                            <option value="dark">Tmavý</option>
                            <option value="auto">Automaticky</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Primárna farba</label>
                        <input type="color" value="#3b82f6" class="form-input h-10">
                    </div>
                </div>

                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Animácie</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Povoliť animácie v rozhraní</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" checked class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                    </label>
                </div>
            </div>

        </div>

        <!-- Save Button -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-end space-x-3">
                <button class="btn btn-secondary">Zrušiť</button>
                <button class="btn btn-primary">Uložiť nastavenia</button>
            </div>
        </div>

    </div>

</div>
