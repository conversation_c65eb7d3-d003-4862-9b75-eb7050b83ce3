<?php
// User view content will be included in mark layout
$pageTitle = $title ?? 'User Details';
$currentRoute = 'mark-user-view';
?>

<!-- User Details -->
<div class="space-y-6">



    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="/mark/users" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Detaily používateľa</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Zobrazenie informácií o používateľovi</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <!-- Test notification button -->
            <button onclick="testUserViewNotification()" class="btn btn-secondary">
                🔔 Test notifikácie
            </button>
            <a href="/mark/users/<?php echo htmlspecialchars($user['id'], ENT_QUOTES, 'UTF-8'); ?>/edit" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Upraviť
            </a>
            <button onclick="deleteUser('<?php echo htmlspecialchars($user['id'], ENT_QUOTES, 'UTF-8'); ?>', '<?php echo htmlspecialchars($user['full_name'], ENT_QUOTES, 'UTF-8'); ?>')" class="btn btn-danger">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Zmazať
            </button>
        </div>
    </div>

    <!-- User Profile Card -->
    <div class="glass rounded-xl p-6">
        <div class="flex items-start space-x-6">
            <!-- Avatar -->
            <div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-2xl">
                    <?php echo htmlspecialchars(strtoupper(substr($user['full_name'] ?: $user['username'], 0, 2)), ENT_QUOTES, 'UTF-8'); ?>
                </span>
            </div>

            <!-- User Info -->
            <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['full_name'] ?: $user['username'], ENT_QUOTES, 'UTF-8'); ?>
                    </h2>
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300">
                        <?php echo $user['is_active'] ? 'Aktívny' : 'Neaktívny'; ?>
                    </span>
                </div>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    <?php echo htmlspecialchars($user['email'], ENT_QUOTES, 'UTF-8'); ?>
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Používateľské meno</div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                            <?php echo htmlspecialchars($user['username'], ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Registrovaný</div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                            <?php echo htmlspecialchars($user['created_at_formatted'], ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Posledná aktivita</div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                            <?php echo htmlspecialchars($user['last_activity'], ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

        <!-- Personal Information -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Osobné údaje</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Meno</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['first_name'] ?? 'Nezadané', ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Priezvisko</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['last_name'] ?? 'Nezadané', ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <a href="mailto:<?php echo htmlspecialchars($user['email'], ENT_QUOTES, 'UTF-8'); ?>"
                           class="text-primary-600 dark:text-primary-400 hover:underline">
                            <?php echo htmlspecialchars($user['email'], ENT_QUOTES, 'UTF-8'); ?>
                        </a>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Telefón</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['phone'] ?? 'Nezadané', ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Informácie o účte</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">ID používateľa</label>
                    <p class="text-gray-900 dark:text-gray-100 font-mono text-sm">
                        <?php echo htmlspecialchars($user['id'], ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Rola</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300">
                            <?php echo htmlspecialchars($user['role'] ?? 'Používateľ', ENT_QUOTES, 'UTF-8'); ?>
                        </span>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Stav účtu</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $user['is_active'] ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300'; ?>">
                            <?php echo $user['is_active'] ? 'Aktívny' : 'Neaktívny'; ?>
                        </span>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Dátum registrácie</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['created_at_formatted'], ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Dodatočné informácie</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Oddelenie</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['department'] ?? 'Nezadané', ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Pozícia</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['position'] ?? 'Nezadané', ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Poznámky</label>
                    <p class="text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['notes'] ?? 'Žiadne poznámky', ENT_QUOTES, 'UTF-8'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Activity & Permissions -->
        <div class="glass rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Aktivita a oprávnenia</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Email notifikácie</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <?php echo ($user['email_notifications'] ?? true) ? 'Povolené' : 'Zakázané'; ?>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Vynútená zmena hesla</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <?php echo ($user['force_password_change'] ?? false) ? 'Áno' : 'Nie'; ?>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Posledné prihlásenie</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['last_login'] ?? 'Nikdy', ENT_QUOTES, 'UTF-8'); ?>
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Počet prihlásení</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <?php echo htmlspecialchars($user['login_count'] ?? '0', ENT_QUOTES, 'UTF-8'); ?>
                    </span>
                </div>
            </div>
        </div>

    </div>

</div>

<!-- Delete User Modal (same as in users.php) -->
<div id="deleteUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" style="z-index: 1000;">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20">
                <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mt-2">Zmazať používateľa</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    Naozaj chcete zmazať používateľa <span id="deleteUserName" class="font-medium"></span>?
                    Táto akcia sa nedá vrátiť späť.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmDeleteBtn" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300">
                    Zmazať
                </button>
                <button id="cancelDeleteBtn" class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-base font-medium rounded-md w-24 hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Zrušiť
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Delete user functionality (same as in users.php)
let userToDelete = null;

function deleteUser(userId, userName) {
    userToDelete = userId;
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteUserModal').classList.remove('hidden');
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (userToDelete) {
        fetch(`/mark/users/${userToDelete}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('deleteUserModal').classList.add('hidden');

            if (window.showNotification) {
                window.showNotification('Používateľ bol úspešne zmazaný', 'success');
            }

            // Redirect to users list after deletion
            setTimeout(() => {
                window.location.href = '/mark/users';
            }, 1000);
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('deleteUserModal').classList.add('hidden');

            if (window.showNotification) {
                window.showNotification('Chyba pri mazaní používateľa', 'error');
            }
        });
    }
});

document.getElementById('cancelDeleteBtn').addEventListener('click', function() {
    document.getElementById('deleteUserModal').classList.add('hidden');
    userToDelete = null;
});

document.getElementById('deleteUserModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
        userToDelete = null;
    }
});

// Show notifications based on URL parameters
document.addEventListener('DOMContentLoaded', function() {
    // Wait for showNotification to be available
    const checkAndShowNotifications = () => {
        const urlParams = new URLSearchParams(window.location.search);

        console.log('USER VIEW - Current URL:', window.location.href);
        console.log('USER VIEW - URL params:', urlParams.toString());
        console.log('USER VIEW - Has updated:', urlParams.has('updated'));
        console.log('USER VIEW - showNotification available:', typeof window.showNotification);

        // Success notifications
        if (urlParams.has('updated')) {
            console.log('USER VIEW - Showing updated notification');
            if (window.showNotification) {
                window.showNotification('Používateľ bol úspešne aktualizovaný!', 'success');
                console.log('USER VIEW - Notification triggered successfully');

                // Clean URL after notification is shown (delay to ensure notification appears)
                setTimeout(() => {
                    const newUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);
                    console.log('USER VIEW - URL cleaned');
                }, 1000);
            } else {
                console.error('USER VIEW - showNotification not available, retrying...');
                // Retry after 500ms if showNotification is not ready
                setTimeout(checkAndShowNotifications, 500);
                return;
            }
        }

        console.log('USER VIEW - Notification setup complete');
    };

    // Start checking immediately
    checkAndShowNotifications();
});

// Test notification function for user view
function testUserViewNotification() {
    console.log('Testing user view notification...');
    console.log('showNotification available:', typeof window.showNotification);

    if (window.showNotification) {
        window.showNotification('Test notifikácia z user view!', 'success');

        // Test updated notification
        setTimeout(() => {
            window.showNotification('Používateľ bol úspešne aktualizovaný!', 'success');
        }, 1000);

        // Test URL parameter simulation
        setTimeout(() => {
            console.log('Simulating ?updated=1 URL...');
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('updated', '1');
            window.history.pushState({}, '', currentUrl);

            // Trigger notification manually
            window.showNotification('Simulácia CRUD notifikácie', 'info');

            // Clean URL
            setTimeout(() => {
                window.history.replaceState({}, '', window.location.pathname);
            }, 2000);
        }, 2000);
    } else {
        alert('showNotification nie je dostupné!');
    }
}
</script>
