<?php $this->layout('theme::layouts/base', ['title' => $title]); ?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold gradient-text mb-4">Routes Debug</h1>
            <p class="text-xl text-gray-600 dark:text-gray-300">
                Prehľad všetkých registrovaných routes v aplikácii
            </p>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400"><?php echo $totalRoutes; ?></div>
                <div class="text-gray-600 dark:text-gray-300">Celkom routes</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-green-600 dark:text-green-400"><?php echo count($groupedRoutes['web']); ?></div>
                <div class="text-gray-600 dark:text-gray-300">Web routes</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-purple-600 dark:text-purple-400"><?php echo count($groupedRoutes['admin']); ?></div>
                <div class="text-gray-600 dark:text-gray-300">Admin routes</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-orange-600 dark:text-orange-400"><?php echo count($groupedRoutes['api']); ?></div>
                <div class="text-gray-600 dark:text-gray-300">API routes</div>
            </div>
        </div>

        <!-- Route Files Status -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800 dark:text-white">Route Files</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-2 text-green-600 dark:text-green-400">Načítané súbory</h3>
                    <ul class="space-y-1">
                        <?php foreach ($loadedRoutes as $file) { ?>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                            <?php echo $this->e($file); ?>.php
                        </li>
                        <?php } ?>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-2 text-blue-600 dark:text-blue-400">Dostupné súbory</h3>
                    <ul class="space-y-1">
                        <?php foreach ($availableRoutes as $file) { ?>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <span class="w-2 h-2 <?php echo in_array($file, $loadedRoutes, true) ? 'bg-green-500' : 'bg-gray-400'; ?> rounded-full mr-2"></span>
                            <?php echo $this->e($file); ?>.php
                        </li>
                        <?php } ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Routes by Category -->
        <?php foreach ($groupedRoutes as $category => $categoryRoutes) { ?>
            <?php if (!empty($categoryRoutes)) { ?>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-bold mb-4 text-gray-800 dark:text-white capitalize">
                    <?php echo $this->e($category); ?> Routes (<?php echo count($categoryRoutes); ?>)
                </h2>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b border-gray-200 dark:border-gray-700">
                                <th class="text-left py-2 px-4 font-semibold text-gray-700 dark:text-gray-300">Methods</th>
                                <th class="text-left py-2 px-4 font-semibold text-gray-700 dark:text-gray-300">Pattern</th>
                                <th class="text-left py-2 px-4 font-semibold text-gray-700 dark:text-gray-300">Name</th>
                                <th class="text-left py-2 px-4 font-semibold text-gray-700 dark:text-gray-300">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($categoryRoutes as $route) { ?>
                            <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="py-2 px-4">
                                    <?php foreach ($route['methods'] as $method) { ?>
                                        <span class="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded mr-1">
                                            <?php echo $this->e($method); ?>
                                        </span>
                                    <?php } ?>
                                </td>
                                <td class="py-2 px-4 font-mono text-gray-600 dark:text-gray-300">
                                    <?php echo $this->e($route['pattern']); ?>
                                </td>
                                <td class="py-2 px-4 text-gray-600 dark:text-gray-300">
                                    <?php echo $route['name'] ? $this->e($route['name']) : '<em>unnamed</em>'; ?>
                                </td>
                                <td class="py-2 px-4 text-gray-600 dark:text-gray-300 font-mono text-xs">
                                    <?php if (is_string($route['callable'])) { ?>
                                        <?php echo $this->e(str_replace('App\\Infrastructure\\Http\\Action\\', '', $route['callable'])); ?>
                                    <?php } else { ?>
                                        <em>Closure</em>
                                    <?php } ?>
                                </td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php } ?>
        <?php } ?>

        <!-- Back to Home -->
        <div class="text-center">
            <a href="<?php echo $this->url('home'); ?>" 
               class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Späť na domov
            </a>
        </div>
    </div>
</div>
