<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Font Test</title>
    <link rel="stylesheet" href="/assets/themes/modern/css/style.css">
    <style>
        body {
            font-family: '<PERSON><PERSON>', system-ui, sans-serif;
            padding: 2rem;
            line-height: 1.6;
        }
        .font-test {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .weight-400 { font-weight: 400; }
        .weight-500 { font-weight: 500; }
        .weight-600 { font-weight: 600; }
        .weight-700 { font-weight: 700; }
        .weight-900 { font-weight: 900; }
    </style>
</head>
<body>
    <h1><PERSON><PERSON> Font Test</h1>
    
    <div class="font-test">
        <h2>Font Weight 400 (Regular)</h2>
        <p class="weight-400">
            The quick brown fox jumps over the lazy dog. 1234567890<br>
            <PERSON><PERSON><PERSON><PERSON> hnedá líška skáče cez lenivého psa. ÁČĎÉÍĹŇÓŠŤÚÝŽ
        </p>
    </div>

    <div class="font-test">
        <h2>Font Weight 500 (Medium)</h2>
        <p class="weight-500">
            The quick brown fox jumps over the lazy dog. 1234567890<br>
            Rýchla hnedá líška skáče cez lenivého psa. ÁČĎÉÍĹŇÓŠŤÚÝŽ
        </p>
    </div>

    <div class="font-test">
        <h2>Font Weight 600 (SemiBold)</h2>
        <p class="weight-600">
            The quick brown fox jumps over the lazy dog. 1234567890<br>
            Rýchla hnedá líška skáče cez lenivého psa. ÁČĎÉÍĹŇÓŠŤÚÝŽ
        </p>
    </div>

    <div class="font-test">
        <h2>Font Weight 700 (Bold)</h2>
        <p class="weight-700">
            The quick brown fox jumps over the lazy dog. 1234567890<br>
            Rýchla hnedá líška skáče cez lenivého psa. ÁČĎÉÍĹŇÓŠŤÚÝŽ
        </p>
    </div>

    <div class="font-test">
        <h2>Font Weight 900 (Black)</h2>
        <p class="weight-900">
            The quick brown fox jumps over the lazy dog. 1234567890<br>
            Rýchla hnedá líška skáče cez lenivého psa. ÁČĎÉÍĹŇÓŠŤÚÝŽ
        </p>
    </div>

    <div class="font-test">
        <h2>Tailwind Classes Test</h2>
        <p class="font-normal">font-normal (400) - Normálny text</p>
        <p class="font-medium">font-medium (500) - Stredný text</p>
        <p class="font-semibold">font-semibold (600) - Polotučný text</p>
        <p class="font-bold">font-bold (700) - Tučný text</p>
        <p class="font-black">font-black (900) - Veľmi tučný text</p>
    </div>

    <div class="font-test">
        <h2>Sizes Test</h2>
        <p class="text-xs">text-xs - Veľmi malý text</p>
        <p class="text-sm">text-sm - Malý text</p>
        <p class="text-base">text-base - Základný text</p>
        <p class="text-lg">text-lg - Veľký text</p>
        <p class="text-xl">text-xl - Extra veľký text</p>
        <p class="text-2xl">text-2xl - 2x veľký text</p>
        <p class="text-3xl">text-3xl - 3x veľký text</p>
        <p class="text-4xl">text-4xl - 4x veľký text</p>
    </div>

    <script>
        // Check if fonts are loaded
        document.fonts.ready.then(() => {
            console.log('✅ All fonts loaded successfully');
            
            // Check if Gilroy is available
            const testElement = document.createElement('div');
            testElement.style.fontFamily = 'Gilroy, sans-serif';
            testElement.style.position = 'absolute';
            testElement.style.visibility = 'hidden';
            testElement.textContent = 'Test';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const fontFamily = computedStyle.fontFamily;
            
            if (fontFamily.includes('Gilroy')) {
                console.log('✅ Gilroy font is active');
                document.title = '✅ Gilroy Font Test - LOADED';
            } else {
                console.log('❌ Gilroy font not loaded, fallback used:', fontFamily);
                document.title = '❌ Gilroy Font Test - FALLBACK';
            }
            
            document.body.removeChild(testElement);
        });
    </script>
</body>
</html>
