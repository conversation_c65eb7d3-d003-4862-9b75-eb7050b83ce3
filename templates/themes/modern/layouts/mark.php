<!DOCTYPE html>
<html lang="sk" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' || (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches), sidebarOpen: false }"
      x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))"
      :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars(($title ?? 'Panel') . ' - Slim4 Modern', ENT_QUOTES, 'UTF-8'); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎸</text></svg>">

    <!-- Styles first for faster font loading -->
    <link rel="stylesheet" href="/assets/themes/modern/css/style.css">

    <!-- Preload critical fonts after CSS -->
    <link rel="preload" href="/assets/themes/modern/fonts/Gilroy-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/assets/themes/modern/fonts/Gilroy-Medium.woff2" as="font" type="font/woff2" crossorigin>
<!-- Mark panel specific styles -->
<style>
    .mark-sidebar {
        backdrop-filter: blur(16px) saturate(180%);
        background-color: rgba(255, 255, 255, 0.95);
        border-right: 1px solid rgba(255, 255, 255, 0.125);
    }

    .dark .mark-sidebar {
        background-color: rgba(17, 25, 40, 0.95);
        border-right: 1px solid rgba(255, 255, 255, 0.125);
    }

    .mark-content {
        margin-left: 0;
        transition: margin-left 0.3s ease;
    }

    @media (min-width: 1024px) {
        .mark-content {
            margin-left: 16rem;
        }
    }
</style>
</head>

<body class="font-sans antialiased">

<div class="min-h-screen bg-gray-50 dark:bg-gray-900" x-data="{ sidebarOpen: false }">

    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="sidebarOpen = false"
         class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"></div>

    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 mark-sidebar transform transition-transform duration-300 ease-in-out lg:translate-x-0"
         :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }"
         x-show="sidebarOpen || window.innerWidth >= 1024">

        <div class="flex flex-col h-full">
            <!-- Sidebar header -->
            <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">M</span>
                    </div>
                    <span class="font-bold text-lg text-gradient-primary">Mark Panel</span>
                </div>

                <!-- Close button for mobile -->
                <button @click="sidebarOpen = false" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">

                <!-- Dashboard -->
                <a href="/mark"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 <?php echo (($currentRoute ?? '') === 'mark-dashboard') ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'; ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    Dashboard
                </a>

                <!-- Users -->
                <div x-data="{ open: <?php echo str_contains($currentRoute ?? '', 'user') ? 'true' : 'false'; ?> }">
                    <button @click="open = !open"
                            class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            Používatelia
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div x-show="open"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 -translate-y-2"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 -translate-y-2"
                         class="ml-8 mt-2 space-y-1">
                        <a href="/mark/users"
                           class="block px-4 py-2 text-sm rounded-lg transition-colors <?php echo (($currentRoute ?? '') === 'mark-users') ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/10' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800/50'; ?>">
                            Zoznam
                        </a>
                        <a href="/mark/users/create"
                           class="block px-4 py-2 text-sm rounded-lg transition-colors <?php echo (($currentRoute ?? '') === 'mark-user-create') ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/10' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800/50'; ?>">
                            Nový používateľ
                        </a>
                    </div>
                </div>

                <!-- Content -->
                <div x-data="{ open: <?php echo str_contains($currentRoute ?? '', 'page') ? 'true' : 'false'; ?> }">
                    <button @click="open = !open"
                            class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Obsah
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div x-show="open"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 -translate-y-2"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 -translate-y-2"
                         class="ml-8 mt-2 space-y-1">
                        <a href="/mark/pages"
                           class="block px-4 py-2 text-sm rounded-lg transition-colors <?php echo (($currentRoute ?? '') === 'mark-pages') ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/10' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800/50'; ?>">
                            Stránky
                        </a>
                        <a href="/mark/pages/create"
                           class="block px-4 py-2 text-sm rounded-lg transition-colors <?php echo (($currentRoute ?? '') === 'mark-page-create') ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/10' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800/50'; ?>">
                            Nová stránka
                        </a>
                    </div>
                </div>

                <!-- Settings -->
                <a href="/mark/settings"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 <?php echo (($currentRoute ?? '') === 'mark-settings') ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'; ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Nastavenia
                </a>

                <!-- Logs -->
                <div x-data="{ open: <?php echo str_contains($currentRoute ?? '', 'logs') ? 'true' : 'false'; ?> }">
                    <button @click="open = !open"
                            class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Logy
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div x-show="open"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 -translate-y-2"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 -translate-y-2"
                         class="ml-8 mt-2 space-y-1">
                        <a href="/mark/logs"
                           class="block px-4 py-2 text-sm rounded-lg transition-colors <?php echo (($currentRoute ?? '') === 'mark-logs') ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/10' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800/50'; ?>">
                            Activity Logs
                        </a>
                        <a href="/mark/logs/system"
                           class="block px-4 py-2 text-sm rounded-lg transition-colors <?php echo (($currentRoute ?? '') === 'mark-logs-system') ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/10' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800/50'; ?>">
                            System Logs
                        </a>
                        <a href="/mark/logs/errors"
                           class="block px-4 py-2 text-sm rounded-lg transition-colors <?php echo (($currentRoute ?? '') === 'mark-logs-errors') ? 'text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/10' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800/50'; ?>">
                            Error Logs
                        </a>
                    </div>
                </div>

                <hr class="my-4 border-gray-200 dark:border-gray-700">

                <!-- Back to site -->
                <a href="/"
                   class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Späť na stránku
                </a>
            </nav>

            <!-- User info -->
            <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">
                            <?php echo strtoupper(substr($user['name'] ?? 'Mark', 0, 1)); ?>
                        </span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            <?php echo htmlspecialchars($user['name'] ?? 'Mark User', ENT_QUOTES, 'UTF-8'); ?>
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                            <?php echo htmlspecialchars($user['email'] ?? '<EMAIL>', ENT_QUOTES, 'UTF-8'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <div class="mark-content">
        <!-- Top bar -->
        <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">

                    <!-- Mobile menu button -->
                    <button @click="sidebarOpen = !sidebarOpen"
                            class="lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <!-- Page title -->
                    <div class="flex-1 lg:flex-none">
                        <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                            <?php echo htmlspecialchars($pageTitle ?? $title ?? 'Mark Panel', ENT_QUOTES, 'UTF-8'); ?>
                        </h1>
                    </div>

                    <!-- Right side actions -->
                    <div class="flex items-center space-x-4">

                        <!-- Notifications -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <!-- Notification badge -->
                                <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                            </button>

                            <div x-show="open"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 @click.away="open = false"
                                 class="absolute right-0 top-12 w-80 glass rounded-xl shadow-xl py-4 z-50">
                                <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Notifikácie</h3>
                                </div>
                                <div class="max-h-64 overflow-y-auto">
                                    <div class="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">
                                        Žiadne nové notifikácie
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dark mode toggle -->
                        <button @click="darkMode = !darkMode"
                                class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                                :title="darkMode ? 'Prepnúť na svetlý režim' : 'Prepnúť na tmavý režim'">
                            <!-- Sun icon - show in dark mode (to switch to light) -->
                            <svg x-show="darkMode" class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            <!-- Moon icon - show in light mode (to switch to dark) -->
                            <svg x-show="!darkMode" class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 118.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>

                        <!-- User menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                                <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">
                                        <?php echo strtoupper(substr($user['name'] ?? 'A', 0, 1)); ?>
                                    </span>
                                </div>
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <div x-show="open"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 @click.away="open = false"
                                 class="absolute right-0 top-12 w-48 glass rounded-xl shadow-xl py-2 z-50">
                                <a href="/profile" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Profil</a>
                                <a href="/settings" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Nastavenia</a>
                                <hr class="my-2 border-gray-200 dark:border-gray-700">
                                <a href="/logout" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Odhlásiť sa</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page content -->
        <div class="p-4 sm:p-6 lg:p-8">
            <?php echo $content ?? ''; ?>
        </div>
    </div>
</div>

<!-- Scripts -->
<!-- Load main.js with error handling for GSAP -->
<script>
// Override console.error temporarily to catch GSAP errors
const originalError = console.error;
console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('GSAP target') && message.includes('not found')) {
        console.warn('GSAP target not found (expected in Mark Panel):', ...args);
        return;
    }
    originalError.apply(console, args);
};

// Restore console.error after 2 seconds
setTimeout(() => {
    console.error = originalError;
}, 2000);
</script>

<script type="module" src="/assets/themes/modern/js/main.js"></script>

<script type="module">
// Mark Panel specific initialization

document.addEventListener('DOMContentLoaded', function() {
    // Wait for main.js to initialize
    setTimeout(() => {
        console.log('Mark Panel - Checking main.js initialization...');
        console.log('modernThemeApp available:', typeof window.modernThemeApp);
        console.log('showNotification available:', typeof window.showNotification);
        console.log('Alpine.js available:', typeof window.Alpine);

        // If showNotification is not available, create fallback
        if (!window.showNotification) {
            console.log('Creating fallback notification system...');

            window.showNotification = function(message, type = 'info', duration = 5000) {
                console.log(`[MARK PANEL ${type.toUpperCase()}] ${message}`);

                // Create simple toast notification
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 1rem;
                    right: 1rem;
                    z-index: 9999;
                    max-width: 20rem;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 1rem;
                    border-radius: 0.5rem;
                    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                    font-size: 0.875rem;
                    font-weight: 500;
                `;

                toast.textContent = message;
                document.body.appendChild(toast);

                // Auto remove
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, duration);
            };
        }

        console.log('Mark Panel - Initialization complete');
    }, 500);
});
</script>



</body>
</html>


