<!DOCTYPE html>
<html lang="sk" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }"
      x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))"
      :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo htmlspecialchars($description ?? 'Modern Slim4 aplikácia s GSAP a Tailwind CSS', ENT_QUOTES, 'UTF-8'); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($keywords ?? 'slim4, php, modern, gsap, tailwind', ENT_QUOTES, 'UTF-8'); ?>">
    <meta name="author" content="<?php echo htmlspecialchars($author ?? 'Slim4 Team', ENT_QUOTES, 'UTF-8'); ?>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlspecialchars($currentUrl ?? '', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($title ?? 'Slim4 Modern App', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($description ?? 'Modern Slim4 aplikácia', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($ogImage ?? '/themes/modern/dist/images/og-image.jpg', ENT_QUOTES, 'UTF-8'); ?>">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo htmlspecialchars($currentUrl ?? '', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="twitter:title" content="<?php echo htmlspecialchars($title ?? 'Slim4 Modern App', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="twitter:description" content="<?php echo htmlspecialchars($description ?? 'Modern Slim4 aplikácia', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="twitter:image" content="<?php echo htmlspecialchars($ogImage ?? '/themes/modern/dist/images/og-image.jpg', ENT_QUOTES, 'UTF-8'); ?>">

    <title><?php echo htmlspecialchars($title ?? 'Slim4 Modern App', ENT_QUOTES, 'UTF-8'); ?></title>

    <!-- Favicon (using data URI for simplicity) -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">

    <!-- Styles first for faster font loading -->
    <link rel="stylesheet" href="/assets/themes/modern/css/style.css">

    <!-- Preload critical fonts after CSS -->
    <link rel="preload" href="/assets/themes/modern/fonts/Gilroy-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/assets/themes/modern/fonts/Gilroy-Medium.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Additional head content -->
    <?php echo $headContent ?? ''; ?>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300 antialiased">

    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-lg z-50">
        Preskočiť na hlavný obsah
    </a>

    <!-- Navigation -->
    <nav class="glass fixed top-0 left-0 right-0 z-40 border-b border-gray-200/50 dark:border-gray-700/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">

                <!-- Logo -->
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <a href="/" class="flex items-center space-x-3 group">
                            <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center group-hover:animate-glow transition-all duration-300 shadow-lg">
                                <span class="text-white font-bold text-lg">S4</span>
                            </div>
                            <div class="hidden sm:block">
                                <span class="font-bold text-xl text-gradient-primary">
                                    Slim4 Modern
                                </span>
                                <div class="text-xs text-gray-500 dark:text-gray-400 -mt-1">
                                    GSAP + Tailwind
                                </div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-center space-x-1">
                        <?php if (isset($navigation) && is_array($navigation)) { ?>
                            <?php foreach ($navigation as $item) { ?>
                                <a href="<?php echo htmlspecialchars($item['url'], ENT_QUOTES, 'UTF-8'); ?>"
                                   class="nav-link <?php echo ($item['active'] ?? false) ? 'nav-link-active' : ''; ?>">
                                    <?php if (isset($item['icon'])) { ?>
                                        <span class="mr-2"><?php echo htmlspecialchars($item['icon'], ENT_QUOTES, 'UTF-8'); ?></span>
                                    <?php } ?>
                                    <?php echo htmlspecialchars($item['label'], ENT_QUOTES, 'UTF-8'); ?>
                                </a>
                            <?php } ?>
                        <?php } else { ?>
                            <a href="/" class="nav-link">Domov</a>
                            <a href="/about" class="nav-link">O nás</a>
                            <a href="/contact" class="nav-link">Kontakt</a>
                        <?php } ?>
                    </div>
                </div>

                <!-- Right side controls -->
                <div class="flex items-center space-x-3">

                    <!-- Search (optional) -->
                    <?php if ($showSearch ?? false) { ?>
                    <div class="hidden lg:block" x-data="{ open: false }">
                        <button @click="open = !open" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 top-16 w-80 glass rounded-xl shadow-xl p-4">
                            <input type="search" placeholder="Hľadať..."
                                   class="form-input w-full"
                                   x-ref="searchInput"
                                   @keydown.escape="open = false">
                        </div>
                    </div>
                    <?php } ?>

                    <!-- User menu (if logged in) -->
                    <?php if (isset($user) && $user) { ?>
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium">
                                    <?php echo strtoupper(substr($user['name'] ?? 'U', 0, 1)); ?>
                                </span>
                            </div>
                            <span class="hidden sm:block text-sm font-medium"><?php echo htmlspecialchars($user['name'] ?? 'User', ENT_QUOTES, 'UTF-8'); ?></span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 top-12 w-48 glass rounded-xl shadow-xl py-2 z-50">
                            <a href="/profile" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Profil</a>
                            <a href="/settings" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Nastavenia</a>
                            <?php if (($user['role'] ?? '') === 'admin') { ?>
                            <hr class="my-2 border-gray-200 dark:border-gray-700">
                            <a href="/admin" class="block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Admin</a>
                            <?php } ?>
                            <hr class="my-2 border-gray-200 dark:border-gray-700">
                            <a href="/logout" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Odhlásiť sa</a>
                        </div>
                    </div>
                    <?php } else { ?>
                    <!-- Login/Register buttons -->
                    <div class="flex items-center space-x-2">
                        <a href="/login" class="btn btn-ghost text-sm">Prihlásenie</a>
                        <a href="/register" class="btn btn-primary text-sm">Registrácia</a>
                    </div>
                    <?php } ?>

                    <!-- Dark Mode Toggle -->
                    <button @click="darkMode = !darkMode"
                            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                            :title="darkMode ? 'Prepnúť na svetlý režim' : 'Prepnúť na tmavý režim'">
                        <!-- Sun icon - show in dark mode (to switch to light) -->
                        <svg x-show="darkMode" class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <!-- Moon icon - show in light mode (to switch to dark) -->
                        <svg x-show="!darkMode" class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 118.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>

                    <!-- Mobile menu button -->
                    <div class="md:hidden" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path x-show="!open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                <path x-show="open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>

                        <!-- Mobile menu -->
                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 top-16 w-64 glass rounded-xl shadow-xl py-4 z-50">

                            <!-- Mobile navigation -->
                            <div class="px-4 space-y-2">
                                <?php if (isset($navigation) && is_array($navigation)) { ?>
                                    <?php foreach ($navigation as $item) { ?>
                                        <a href="<?php echo htmlspecialchars($item['url'], ENT_QUOTES, 'UTF-8'); ?>"
                                           class="<?php echo $item['active'] ? 'text-primary-600 dark:text-primary-400 font-medium' : 'text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400'; ?> px-3 py-2 rounded-md text-sm font-medium transition-colors">
                                    <?php echo htmlspecialchars($item['label'], ENT_QUOTES, 'UTF-8'); ?>
                                </a>
                                    <?php } ?>
                                <?php } else { ?>
                                    <a href="/" class="block py-2 text-sm hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Domov</a>
                                    <a href="/about" class="block py-2 text-sm hover:text-primary-600 dark:hover:text-primary-400 transition-colors">O nás</a>
                                    <a href="/contact" class="block py-2 text-sm hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Kontakt</a>
                                <?php } ?>
                            </div>

                            <?php if (!isset($user) || !$user) { ?>
                            <hr class="my-4 border-gray-200 dark:border-gray-700">
                            <div class="px-4 space-y-2">
                                <a href="/login" class="block py-2 text-sm hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Prihlásenie</a>
                                <a href="/register" class="block py-2 text-sm hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Registrácia</a>
                            </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="pt-16 min-h-screen page-transition">
        <?php
        // Output the main content if it's set
        if (isset($mainContent)) {
            echo $mainContent;
        } else {
            // Fallback to output buffer if $mainContent is not set
            $mainContent = ob_get_clean();
            echo $mainContent;
        }
    ?>
    </main>

    <!-- Footer -->
    <footer class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-t border-gray-200/50 dark:border-gray-700/50">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">

                <!-- Brand -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">S4</span>
                        </div>
                        <span class="font-bold text-lg text-gradient-primary">Slim4 Modern</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
                        Moderná PHP aplikácia postavená na Slim4 frameworku s GSAP animáciami a Tailwind CSS dizajnom.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/></svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 tracking-wider uppercase mb-4">
                        Rýchle odkazy
                    </h3>
                    <ul class="space-y-2">
                        <li><a href="/about" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">O nás</a></li>
                        <li><a href="/contact" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Kontakt</a></li>
                        <li><a href="/privacy" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Ochrana súkromia</a></li>
                        <li><a href="/terms" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Podmienky použitia</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 tracking-wider uppercase mb-4">
                        Podpora
                    </h3>
                    <ul class="space-y-2">
                        <li><a href="/help" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Pomoc</a></li>
                        <li><a href="/docs" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Dokumentácia</a></li>
                        <li><a href="/api" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">API</a></li>
                        <li><a href="/status" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Stav služieb</a></li>
                    </ul>
                </div>
            </div>

            <hr class="my-8 border-gray-200 dark:border-gray-700">

            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 dark:text-gray-400 text-sm">
                    © <?php echo date('Y'); ?> Slim4 Modern App. Všetky práva vyhradené.
                </p>
                <p class="text-gray-500 dark:text-gray-500 text-xs mt-2 md:mt-0">
                    Vytvorené s ❤️ pomocou GSAP + Tailwind CSS
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script type="module" src="/assets/themes/modern/js/main.js"></script>
    <?php if (isset($scripts)) { ?>
        <?php foreach ((array)$scripts as $script) { ?>
            <script src="<?php echo htmlspecialchars($script, ENT_QUOTES, 'UTF-8'); ?>"></script>
        <?php } ?>
    <?php } ?>

    <!-- Development tools (handled by DevTools.js component) -->
    <?php if (($_ENV['APP_ENV'] ?? 'production') === 'development') { ?>
    <script data-dev="true">
        // Development tools are automatically initialized by the DevTools component
        document.body.dataset.env = 'development';
    </script>
    <?php } ?>
</body>
</html>
