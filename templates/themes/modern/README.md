# Modern Theme - JavaScript Components

Táto téma používa modulárny prístup k JavaScript kódu. Namiesto inline skriptov v template súboroch sú všetky funkcionality rozdelené do samostatných komponentov.

## Komponenty

### 1. AdminPanel.js
Spravuje admin-špecifické funkcionality:
- Automatické zatvorenie sidebar na mobile
- Klávesové skratky (Alt+D, Alt+U, Alt+S, Alt+C)
- Vylepšené confirm dialógy

**Použitie v template:**
```html
<!-- Automaticky sa inicializuje, ak je prítomný .admin-sidebar -->
<div class="admin-sidebar">...</div>
```

### 2. DemoAnimations.js
Spravuje demo animácie a interaktívne prvky:
- Uvítacia notifikácia na domovskej stránke
- Animácie tech badges
- Hover efekty na kartách a tlačidlách

**Použitie v template:**
```html
<!-- Automaticky sa inicializuje -->
<div class="tech-badge">...</div>
<div class="card-hover">...</div>
```

### 3. TinyMCEEditor.js
Spravuje rich text editory:
- Automatické načítanie TinyMCE
- Konfigurácia cez data atribúty
- Podpora pre viacero editorov

**Použitie v template:**
```html
<!-- Základný editor -->
<textarea id="content" data-editor="tinymce" data-height="500"></textarea>

<!-- Jednoduchý editor -->
<textarea id="simple" data-editor="tinymce" data-simple="true"></textarea>

<!-- Vlastná konfigurácia -->
<textarea id="custom" data-editor="tinymce" 
          data-height="300" 
          data-plugins="lists,link,paste"
          data-toolbar="bold italic | bullist numlist | link"></textarea>
```

### 4. DevTools.js
Vývojárske nástroje (iba v development mode):
- Loguje informácie o knižniciach
- Pridáva globálne helper funkcie
- Monitoruje výkon
- Vylepšené error handling

**Použitie:**
```javascript
// V konzole sú dostupné:
devTools.theme.showNotification('Test', 'success')
devTools.theme.triggerAnimation('.element', 'fadeIn')
devTools.theme.reloadCSS()
devTools.performance.measure('test', () => { /* kód */ })
devTools.debug.logElements('.selector')
```

## Template Helpers

Globálne helper funkcie dostupné v `window.templateHelpers`:

### showNotification(message, type, duration)
```javascript
templateHelpers.showNotification('Úspešne uložené!', 'success', 3000)
```

### triggerAnimation(selector, type)
```javascript
templateHelpers.triggerAnimation('.new-elements', 'fadeIn')
```

### setupConfirmDialogs()
```html
<a href="/delete/1" data-confirm="Naozaj chcete odstrániť?">Odstrániť</a>
<script>templateHelpers.setupConfirmDialogs()</script>
```

### setupFormValidation(formSelector)
```javascript
templateHelpers.setupFormValidation('#myForm')
```

## Migrácia z inline skriptov

### Pred (inline skripty):
```html
<?php $this->start('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Admin specific JavaScript
        document.querySelectorAll('.admin-sidebar a').forEach(link => {
            // ... kód
        });
    });
</script>
<?php $this->stop() ?>
```

### Po (komponenty):
```html
<?php $this->start('scripts') ?>
<!-- Admin panel functionality is handled by AdminPanel.js component -->
<script>
    // Admin panel is automatically initialized by the AdminPanel component
    // No additional JavaScript needed here
</script>
<?php $this->stop() ?>
```

## Výhody modulárneho prístupu

1. **Čistejší kód** - template súbory obsahujú iba HTML a PHP
2. **Znovupoužiteľnosť** - komponenty sa dajú použiť na viacerých stránkach
3. **Lepšia údržba** - JavaScript kód je centralizovaný
4. **Testovateľnosť** - komponenty sa dajú testovať nezávisle
5. **Performance** - kód sa kompiluje a optimalizuje cez Vite
6. **Type safety** - možnosť pridať TypeScript v budúcnosti

## Pridanie nového komponentu

1. Vytvorte súbor v `src/js/components/MyComponent.js`
2. Importujte ho v `src/js/app.js`
3. Inicializujte v DOMContentLoaded event listeneri
4. Aktualizujte dokumentáciu

Príklad:
```javascript
// src/js/components/MyComponent.js
export class MyComponent {
  constructor() {
    this.init()
  }

  init() {
    if (!this.shouldInitialize()) return
    
    this.setupEventListeners()
    console.log('🎯 MyComponent initialized')
  }

  shouldInitialize() {
    return document.querySelector('.my-component') !== null
  }

  setupEventListeners() {
    // Váš kód tu
  }
}
```
