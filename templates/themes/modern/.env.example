# Vite Environment Variables
# Copy this file to .env and adjust values as needed

# Development mode
NODE_ENV=development

# Vite server configuration
VITE_HOST=localhost
VITE_PORT=3000

# API configuration
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=10000

# Feature flags
VITE_ENABLE_ANIMATIONS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_DEV_TOOLS=true

# Debug settings
VITE_DEBUG_ENABLED=true
VITE_DEBUG_LOG_LEVEL=info
VITE_SHOW_PERFORMANCE=true

# Theme settings
VITE_DEFAULT_THEME=light
VITE_THEME_STORAGE_KEY=darkMode

# Build settings
VITE_GENERATE_SOURCEMAP=true
VITE_MINIFY=true

# Analytics (optional)
VITE_ANALYTICS_ID=
VITE_GTM_ID=

# Social login (optional)
VITE_GOOGLE_CLIENT_ID=
VITE_GITHUB_CLIENT_ID=
