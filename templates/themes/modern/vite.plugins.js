/**
 * Vite Plugins Configuration
 * Additional plugins for development and build optimization
 */

import { resolve } from 'path'

/**
 * Bundle analyzer plugin (optional)
 * Uncomment and install rollup-plugin-visualizer if needed
 */
/*
import { visualizer } from 'rollup-plugin-visualizer'

export const bundleAnalyzer = visualizer({
  filename: 'dist/bundle-analysis.html',
  open: true,
  gzipSize: true,
  brotliSize: true
})
*/

/**
 * Development plugins
 */
export const devPlugins = []

/**
 * Production plugins
 */
export const prodPlugins = []

/**
 * Legacy browser support plugin (optional)
 * Uncomment and install @vitejs/plugin-legacy if needed
 */
/*
import legacy from '@vitejs/plugin-legacy'

export const legacyPlugin = legacy({
  targets: ['defaults', 'not IE 11']
})
*/

/**
 * PWA plugin (optional)
 * Uncomment and install vite-plugin-pwa if needed
 */
/*
import { VitePWA } from 'vite-plugin-pwa'

export const pwaPlugin = VitePWA({
  registerType: 'autoUpdate',
  workbox: {
    globPatterns: ['**/*.{js,css,html,ico,png,svg}']
  },
  manifest: {
    name: 'Fretboard Pattern Visualizer',
    short_name: 'FretboardViz',
    description: 'Guitar learning tools and fretboard visualization',
    theme_color: '#3b82f6',
    background_color: '#ffffff',
    display: 'standalone',
    icons: [
      {
        src: 'icons/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png'
      },
      {
        src: 'icons/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png'
      }
    ]
  }
})
*/

/**
 * ESLint plugin (optional)
 * Uncomment and install vite-plugin-eslint if needed
 */
/*
import eslint from 'vite-plugin-eslint'

export const eslintPlugin = eslint({
  cache: false,
  include: ['src/**/*.js'],
  exclude: ['node_modules']
})
*/

export default {
  devPlugins,
  prodPlugins
}
