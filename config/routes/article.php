<?php

/**
 * Article Module Routes.
 *
 * These routes handle article/blog management functionality.
 */

use <PERSON>\App;
use Slim\Routing\RouteCollectorProxy;

return function (App $app) {
    // Article management routes (admin)
    $app->group('/admin/articles', function (RouteCollectorProxy $group) {

        // Article list page
        $group->get('', function ($request, $response) {
            // TODO: Create ArticleListPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('admin-articles-list');

        // Create article page
        $group->get('/create', function ($request, $response) {
            // TODO: Create ArticleCreatePageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('admin-articles-create-page');

        // Submit article creation
        $group->post('/create', function ($request, $response) {
            // TODO: Create ArticleCreateAction
            return $response->with<PERSON><PERSON>(['message' => 'Article creation not implemented yet'], 501);
        })->setName('admin-articles-create-submit');

        // Edit article page
        $group->get('/{article_id}/edit', function ($request, $response, $args) {
            // TODO: Create ArticleEditPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('admin-articles-edit-page');

        // Submit article update
        $group->put('/{article_id}', function ($request, $response, $args) {
            // TODO: Create ArticleUpdateAction
            return $response->withJson(['message' => 'Article update not implemented yet'], 501);
        })->setName('admin-articles-update-submit');

        // Delete article
        $group->delete('/{article_id}', function ($request, $response, $args) {
            // TODO: Create ArticleDeleteAction
            return $response->withJson(['message' => 'Article deletion not implemented yet'], 501);
        })->setName('admin-articles-delete');

        // Bulk actions
        $group->post('/bulk', function ($request, $response) {
            // TODO: Create ArticleBulkAction
            return $response->withJson(['message' => 'Article bulk actions not implemented yet'], 501);
        })->setName('admin-articles-bulk');
    });

    // Article categories management
    $app->group('/admin/categories', function (RouteCollectorProxy $group) {

        // Categories list
        $group->get('', function ($request, $response) {
            // TODO: Create CategoriesListPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('admin-categories-list');

        // Create category
        $group->post('', function ($request, $response) {
            // TODO: Create CategoryCreateAction
            return $response->withJson(['message' => 'Category creation not implemented yet'], 501);
        })->setName('admin-categories-create');

        // Update category
        $group->put('/{category_id}', function ($request, $response, $args) {
            // TODO: Create CategoryUpdateAction
            return $response->withJson(['message' => 'Category update not implemented yet'], 501);
        })->setName('admin-categories-update');

        // Delete category
        $group->delete('/{category_id}', function ($request, $response, $args) {
            // TODO: Create CategoryDeleteAction
            return $response->withJson(['message' => 'Category deletion not implemented yet'], 501);
        })->setName('admin-categories-delete');
    });

    // Article tags management
    $app->group('/admin/tags', function (RouteCollectorProxy $group) {

        // Tags list
        $group->get('', function ($request, $response) {
            // TODO: Create TagsListPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('admin-tags-list');

        // Create tag
        $group->post('', function ($request, $response) {
            // TODO: Create TagCreateAction
            return $response->withJson(['message' => 'Tag creation not implemented yet'], 501);
        })->setName('admin-tags-create');

        // Update tag
        $group->put('/{tag_id}', function ($request, $response, $args) {
            // TODO: Create TagUpdateAction
            return $response->withJson(['message' => 'Tag update not implemented yet'], 501);
        })->setName('admin-tags-update');

        // Delete tag
        $group->delete('/{tag_id}', function ($request, $response, $args) {
            // TODO: Create TagDeleteAction
            return $response->withJson(['message' => 'Tag deletion not implemented yet'], 501);
        })->setName('admin-tags-delete');
    });

    // Public article routes (already defined in web.php as /blog)
    // These are for SEO-friendly URLs

    // Article by slug (alternative to /blog/{slug})
    $app->get('/article/{slug}', function ($request, $response, $args) {
        // Redirect to blog post URL for consistency
        return $response->withHeader('Location', '/blog/' . $args['slug'])->withStatus(301);
    })->setName('article-by-slug-redirect');

    // Article by ID (for admin/internal use)
    $app->get('/articles/{article_id}', function ($request, $response, $args) {
        // TODO: Create ArticleViewAction (admin view)
        return $response->withHeader('Location', '/auth/login')->withStatus(302);
    })->setName('article-view');

    // Article preview (for draft articles)
    $app->get('/articles/{article_id}/preview', function ($request, $response, $args) {
        // TODO: Create ArticlePreviewAction
        return $response->withHeader('Location', '/auth/login')->withStatus(302);
    })->setName('article-preview');

    // Article media upload
    $app->post('/admin/articles/{article_id}/media', function ($request, $response, $args) {
        // TODO: Create ArticleMediaUploadAction
        return $response->withJson(['message' => 'Media upload not implemented yet'], 501);
    })->setName('admin-articles-media-upload');

    // Article media delete
    $app->delete('/admin/articles/{article_id}/media/{media_id}', function ($request, $response, $args) {
        // TODO: Create ArticleMediaDeleteAction
        return $response->withJson(['message' => 'Media deletion not implemented yet'], 501);
    })->setName('admin-articles-media-delete');
};
