<?php

/**
 * Mark Panel Routes.
 *
 * These routes handle mark panel functionality and dashboard.
 * Replaces admin routes with mark terminology.
 */

use App\Application\Responder\JsonResponder;
use Slim\App;
use Slim\Routing\RouteCollectorProxy;

return function (App $app) {
    // Helper function for JSON responses
    $jsonResponse = function ($response, $data, $status = 200) use ($app) {
        $container = $app->getContainer();
        if ($container === null) {
            throw new RuntimeException('Container not available');
        }
        $jsonResponder = $container->get(JsonResponder::class);
        if (!$jsonResponder instanceof JsonResponder) {
            throw new RuntimeException('JsonResponder not found in container');
        }

        return $jsonResponder->encodeAndAddToResponse($response, $data, $status);
    };
    // Mark panel routes
    $app->group('/mark', function (RouteCollectorProxy $group) use ($jsonResponse) {

        // Mark dashboard
        $group->get('', function ($request, $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            // Get dashboard content
            $dashboardContent = $renderer->fetch('themes/modern/pages/mark/dashboard.php', [
                'title' => 'Dashboard'
            ]);

            // Render with mark layout
            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Dashboard',
                'pageTitle' => 'Mark Panel Dashboard',
                'currentRoute' => 'mark-dashboard',
                'content' => $dashboardContent
            ]);
        })->setName('mark-dashboard');

        $group->get('/dashboard', function ($request, $response) {
            return $response->withHeader('Location', '/mark')->withStatus(301);
        })->setName('mark-dashboard-redirect');

        // User management
        $group->group('/users', function (RouteCollectorProxy $users) use ($jsonResponse) {

            // Users overview
            $users->get('', function ($request, $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);
                $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);

                // Get query parameters for pagination and filtering
                $queryParams = $request->getQueryParams();
                $page = (int)($queryParams['page'] ?? 1);
                $limit = (int)($queryParams['limit'] ?? 10);
                $offset = ($page - 1) * $limit;

                // Get users from database
                $userListData = $userListService->getUserList($limit, $offset);

                // Get users content
                $usersContent = $renderer->fetch('themes/modern/pages/mark/users.php', [
                    'title' => 'User Management',
                    'users' => $userListData['users'],
                    'pagination' => $userListData['pagination']
                ]);

                // Render with mark layout
                return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                    'title' => 'User Management',
                    'pageTitle' => 'User Management',
                    'currentRoute' => 'mark-users',
                    'content' => $usersContent
                ]);
            })->setName('mark-users');

            // Create user page
            $users->get('/create', function ($request, $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                // Get user create content
                $userCreateContent = $renderer->fetch('themes/modern/pages/mark/user-create.php', [
                    'title' => 'Create User'
                ]);

                // Render with mark layout
                return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                    'title' => 'Create User',
                    'pageTitle' => 'Create New User',
                    'currentRoute' => 'mark-user-create',
                    'content' => $userCreateContent
                ]);
            })->setName('mark-user-create');

            // Create user POST
            $users->post('/create', function ($request, $response) use ($jsonResponse) {
                $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
                $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);

                try {
                    $formData = $request->getParsedBody();

                    // Debug logging
                    error_log('Create User - Form data: ' . json_encode($formData));

                    // Create user
                    $userId = $userListService->createUser($formData);

                    // Debug logging
                    error_log('Create User - Success, User ID: ' . $userId);

                    // Log activity
                    $activityLogger->logUserCreated($userId, 'admin'); // TODO: Get actual user ID

                    // Redirect to user list with success message
                    return $response->withHeader('Location', '/mark/users?created=' . $userId)->withStatus(302);

                } catch (\InvalidArgumentException $e) {
                    // Debug logging
                    error_log('Create User - Validation Error: ' . $e->getMessage());

                    // Return to form with error
                    $renderer = $this->get(Slim\Views\PhpRenderer::class);

                    $userCreateContent = $renderer->fetch('themes/modern/pages/mark/user-create.php', [
                        'title' => 'Create User',
                        'error' => $e->getMessage(),
                        'formData' => $formData ?? []
                    ]);

                    return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                        'title' => 'Create User',
                        'pageTitle' => 'Create New User',
                        'currentRoute' => 'mark-user-create',
                        'content' => $userCreateContent
                    ])->withStatus(400);

                } catch (\Exception $e) {
                    // Debug logging
                    error_log('Create User - General Error: ' . $e->getMessage());
                    error_log('Create User - Stack trace: ' . $e->getTraceAsString());

                    return $jsonResponse($response, [
                        'error' => 'Failed to create user',
                        'message' => $e->getMessage()
                    ], 500);
                }
            })->setName('mark-user-create-post');

            // User roles management (MUST be before /{id} routes)
            $users->get('/roles', function ($request, $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                // Get user roles content
                $userRolesContent = $renderer->fetch('themes/modern/pages/mark/user-roles.php', [
                    'title' => 'User Roles'
                ]);

                // Render with mark layout
                return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                    'title' => 'User Roles',
                    'pageTitle' => 'User Roles Management',
                    'currentRoute' => 'mark-user-roles',
                    'content' => $userRolesContent
                ]);
            })->setName('mark-user-roles');

            // User import/export (MUST be before /{id} routes)
            $users->get('/import', function ($request, $response) use ($jsonResponse) {
                // TODO: Implement user import page
                return $jsonResponse($response, ['message' => 'User import not implemented yet'], 501);
            })->setName('mark-user-import');

            $users->get('/export', function ($request, $response) use ($jsonResponse) {
                // TODO: Implement user export functionality
                return $jsonResponse($response, ['message' => 'User export not implemented yet'], 501);
            })->setName('mark-user-export');

            // Edit user page
            $users->get('/{id}/edit', function ($request, $response, $args) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);
                $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);

                $userId = $args['id'];

                // Get user by ID from service
                $user = $userListService->getUserById($userId);

                if (!$user) {
                    // User not found - redirect to users list
                    return $response->withHeader('Location', '/mark/users?error=user_not_found')->withStatus(302);
                }

                // Get user edit content
                $userEditContent = $renderer->fetch('themes/modern/pages/mark/user-edit.php', [
                    'title' => 'Edit User',
                    'user' => $user
                ]);

                // Render with mark layout
                return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                    'title' => 'Edit User',
                    'pageTitle' => 'Edit User',
                    'currentRoute' => 'mark-user-edit',
                    'content' => $userEditContent
                ]);
            })->setName('mark-user-edit');

            // Update user POST
            $users->post('/{id}/edit', function ($request, $response, $args) use ($jsonResponse) {
                $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
                $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);
                $userId = $args['id'];

                try {
                    $formData = $request->getParsedBody();

                    // Update user
                    $success = $userListService->updateUser($userId, $formData);

                    if ($success) {
                        // Log activity
                        $activityLogger->logUserUpdated($userId, 'admin', array_keys($formData)); // TODO: Get actual user ID

                        // Redirect to user detail with success message
                        return $response->withHeader('Location', '/mark/users/' . $userId . '?updated=1')->withStatus(302);
                    } else {
                        return $jsonResponse($response, [
                            'error' => 'Failed to update user',
                            'id' => $userId
                        ], 500);
                    }

                } catch (\InvalidArgumentException $e) {
                    // Return to form with error
                    $renderer = $this->get(Slim\Views\PhpRenderer::class);
                    $user = $userListService->getUserById($userId);

                    if (!$user) {
                        return $response->withHeader('Location', '/mark/users?error=user_not_found')->withStatus(302);
                    }

                    // Merge form data with existing user data
                    $user = array_merge($user, $formData ?? []);

                    $userEditContent = $renderer->fetch('themes/modern/pages/mark/user-edit.php', [
                        'title' => 'Edit User',
                        'user' => $user,
                        'error' => $e->getMessage()
                    ]);

                    return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                        'title' => 'Edit User',
                        'pageTitle' => 'Edit User',
                        'currentRoute' => 'mark-user-edit',
                        'content' => $userEditContent
                    ])->withStatus(400);

                } catch (\Exception $e) {
                    return $jsonResponse($response, [
                        'error' => 'Failed to update user',
                        'message' => $e->getMessage(),
                        'id' => $userId
                    ], 500);
                }
            })->setName('mark-user-update');

            // Delete user
            $users->delete('/{id}', function ($request, $response, $args) use ($jsonResponse) {
                $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);
                $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);
                $userId = $args['id'];

                try {
                    $success = $userListService->deleteUser($userId);

                    if ($success) {
                        // Log activity
                        $activityLogger->logUserDeleted($userId, 'admin'); // TODO: Get actual user ID

                        return $jsonResponse($response, [
                            'success' => true,
                            'message' => 'User deleted successfully',
                            'id' => $userId
                        ], 200);
                    } else {
                        return $jsonResponse($response, [
                            'error' => 'Failed to delete user',
                            'id' => $userId
                        ], 500);
                    }

                } catch (\InvalidArgumentException $e) {
                    return $jsonResponse($response, [
                        'error' => $e->getMessage(),
                        'id' => $userId
                    ], 404);

                } catch (\Exception $e) {
                    return $jsonResponse($response, [
                        'error' => 'Failed to delete user',
                        'message' => $e->getMessage(),
                        'id' => $userId
                    ], 500);
                }
            })->setName('mark-user-delete');

            // View user details
            $users->get('/{id}', function ($request, $response, $args) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);
                $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);

                $userId = $args['id'];

                // Get user by ID from service
                $user = $userListService->getUserById($userId);

                if (!$user) {
                    // User not found - redirect to users list
                    return $response->withHeader('Location', '/mark/users?error=user_not_found')->withStatus(302);
                }

                // Get user view content
                $userViewContent = $renderer->fetch('themes/modern/pages/mark/user-view.php', [
                    'title' => 'User Details',
                    'user' => $user
                ]);

                // Render with mark layout
                return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                    'title' => 'User Details',
                    'pageTitle' => 'User Details',
                    'currentRoute' => 'mark-user-view',
                    'content' => $userViewContent
                ]);
            })->setName('mark-user-view');

        });

        // Content management
        $group->group('/pages', function (RouteCollectorProxy $pages) {

            // Pages overview
            $pages->get('', function ($request, $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                return $renderer->render($response, 'themes/modern/pages/mark/pages.php', [
                    'title' => 'Page Management',
                    'pageTitle' => 'Page Management',
                    'currentRoute' => 'mark-pages'
                ]);
            })->setName('mark-pages');

            // Create page
            $pages->get('/create', function ($request, $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                return $renderer->render($response, 'themes/modern/pages/mark/page-create.php', [
                    'title' => 'Create Page',
                    'pageTitle' => 'Create New Page',
                    'currentRoute' => 'mark-page-create'
                ]);
            })->setName('mark-page-create');

        });

        // Settings
        $group->get('/settings', function ($request, $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            // Get settings content
            $settingsContent = $renderer->fetch('themes/modern/pages/mark/settings.php', [
                'title' => 'Settings'
            ]);

            // Render with mark layout
            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Settings',
                'pageTitle' => 'Mark Panel Settings',
                'currentRoute' => 'mark-settings',
                'content' => $settingsContent
            ]);
        })->setName('mark-settings');

        // Logs
        $group->get('/logs', function ($request, $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            // Get activity logs content
            $logsContent = $renderer->fetch('themes/modern/pages/mark/logs.php', [
                'title' => 'Activity Logs'
            ]);

            // Render with mark layout
            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Activity Logs',
                'pageTitle' => 'Activity Logs',
                'currentRoute' => 'mark-logs',
                'content' => $logsContent
            ]);
        })->setName('mark-logs');

        // Analytics and reports
        $group->group('/analytics', function (RouteCollectorProxy $analytics) use ($jsonResponse) {

            // Analytics dashboard
            $analytics->get('', function ($request, $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                return $renderer->render($response, 'themes/modern/pages/mark/analytics.php', [
                    'title' => 'Analytics',
                    'pageTitle' => 'Analytics Dashboard',
                    'currentRoute' => 'mark-analytics'
                ]);
            })->setName('mark-analytics');

            // User analytics
            $analytics->get('/users', function ($request, $response) use ($jsonResponse) {
                return $jsonResponse($response, [
                    'message' => 'User analytics not implemented yet',
                    'data' => [
                        'total_users' => 42,
                        'active_users' => 28,
                        'new_users_today' => 3
                    ]
                ], 501);
            })->setName('mark-analytics-users');

        });

        // File management
        $group->group('/files', function (RouteCollectorProxy $files) use ($jsonResponse) {

            // File manager
            $files->get('', function ($request, $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                return $renderer->render($response, 'themes/modern/pages/mark/files.php', [
                    'title' => 'File Manager',
                    'pageTitle' => 'File Manager',
                    'currentRoute' => 'mark-files'
                ]);
            })->setName('mark-files');

            // Upload files
            $files->post('/upload', function ($request, $response) use ($jsonResponse) {
                return $jsonResponse($response, ['message' => 'File upload not implemented yet'], 501);
            })->setName('mark-files-upload');

        });

    });

    // Article management routes (mark panel)
    $app->group('/mark/articles', function (RouteCollectorProxy $group) {

        // Article list page
        $group->get('', function ($request, $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/articles.php', [
                'title' => 'Article Management',
                'pageTitle' => 'Article Management',
                'currentRoute' => 'mark-articles'
            ]);
        })->setName('mark-articles-list');

        // Create article page
        $group->get('/create', function ($request, $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/article-create.php', [
                'title' => 'Create Article',
                'pageTitle' => 'Create New Article',
                'currentRoute' => 'mark-article-create'
            ]);
        })->setName('mark-articles-create-page');

    });

    // Article categories management
    $app->group('/mark/categories', function (RouteCollectorProxy $group) use ($jsonResponse) {

        // Categories list
        $group->get('', function ($request, $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/categories.php', [
                'title' => 'Categories',
                'pageTitle' => 'Category Management',
                'currentRoute' => 'mark-categories'
            ]);
        })->setName('mark-categories-list');

        // Create category
        $group->post('', function ($request, $response) use ($jsonResponse) {
            return $jsonResponse($response, ['message' => 'Category creation not implemented yet'], 501);
        })->setName('mark-categories-create');

    });

    // Dashboard API endpoints
    $app->group('/mark/api', function (RouteCollectorProxy $api) use ($jsonResponse) {

        // Dashboard statistics
        $api->get('/dashboard-stats', function ($request, $response) use ($jsonResponse) {
            $userListService = $this->get(\App\Module\User\Read\Service\UserListService::class);

            try {
                // Get user statistics
                $userStats = $userListService->getUserStatistics();

                // Get system statistics
                $systemStats = [
                    'totalArticles' => 67, // TODO: Implement ArticleService
                    'articlesGrowth' => '+5 tento týždeň',
                    'totalViews' => '45.2K', // TODO: Implement Analytics
                    'viewsGrowth' => '+8% tento mesiac',
                    'systemStatus' => 'Online',
                    'systemUptime' => '99.9% uptime'
                ];

                // Get recent activity from logs
                $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);
                $recentLogs = $activityLogger->getRecentActivity(10);

                $recentActivity = array_map(function($log) {
                    $colorMap = [
                        'user_created' => 'bg-green-500',
                        'user_updated' => 'bg-blue-500',
                        'user_deleted' => 'bg-red-500',
                        'login_success' => 'bg-green-500',
                        'login_failed' => 'bg-red-500',
                        'system_' => 'bg-yellow-500'
                    ];

                    $color = 'bg-gray-500';
                    foreach ($colorMap as $type => $typeColor) {
                        if (strpos($log['type'], $type) === 0) {
                            $color = $typeColor;
                            break;
                        }
                    }

                    return [
                        'type' => $log['type'],
                        'message' => $log['message'],
                        'time' => $log['time_ago'],
                        'color' => $color
                    ];
                }, $recentLogs);

                $dashboardData = array_merge($userStats, $systemStats, [
                    'recentActivity' => $recentActivity,
                    'lastUpdated' => date('Y-m-d H:i:s')
                ]);

                return $jsonResponse($response, $dashboardData, 200);

            } catch (\Exception $e) {
                error_log('Dashboard API Error: ' . $e->getMessage());

                return $jsonResponse($response, [
                    'error' => 'Failed to load dashboard data',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-dashboard-stats');

        // Activity logs
        $api->get('/activity-logs', function ($request, $response) use ($jsonResponse) {
            $activityLogger = $this->get(\App\Infrastructure\Logging\ActivityLogger::class);

            try {
                $queryParams = $request->getQueryParams();
                $limit = (int)($queryParams['limit'] ?? 50);
                $page = (int)($queryParams['page'] ?? 1);

                // Get logs
                $logs = $activityLogger->getRecentActivity($limit);
                $stats = $activityLogger->getActivityStats();

                return $jsonResponse($response, [
                    'logs' => $logs,
                    'total' => $stats['total'],
                    'today' => $stats['today'],
                    'this_week' => $stats['this_week'],
                    'by_type' => $stats['by_type'],
                    'page' => $page,
                    'limit' => $limit
                ], 200);

            } catch (\Exception $e) {
                error_log('Activity Logs API Error: ' . $e->getMessage());

                return $jsonResponse($response, [
                    'error' => 'Failed to load activity logs',
                    'message' => $e->getMessage()
                ], 500);
            }
        })->setName('mark-api-activity-logs');

    });
};
