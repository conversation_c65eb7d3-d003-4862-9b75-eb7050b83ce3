<?php

/**
 * Authentication Routes.
 *
 * These routes handle user authentication, registration, and password management.
 */

use <PERSON>\App;
use Slim\Routing\RouteCollectorProxy;

return function (App $app) {
    // Authentication routes
    $app->group('/auth', function (RouteCollectorProxy $group) {

        // Login
        $group->get('/login', function ($request, $response) {
            // TODO: Create LoginPageAction
            // For now, serve the login template directly
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/login.php');
        })->setName('login-page');

        $group->post('/login', function ($request, $response) {
            // TODO: Create LoginSubmitAction
            return $response->withJson(['message' => 'Login endpoint not implemented yet'], 501);
        })->setName('login-submit');

        // Registration
        $group->get('/register', function ($request, $response) {
            // TODO: Create RegisterPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('register-page');

        $group->post('/register', function ($request, $response) {
            // TODO: Create RegisterSubmitAction
            return $response->withJson(['message' => 'Registration endpoint not implemented yet'], 501);
        })->setName('register-submit');

        // Password reset
        $group->get('/forgot-password', function ($request, $response) {
            // TODO: Create ForgotPasswordPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('forgot-password-page');

        $group->post('/forgot-password', function ($request, $response) {
            // TODO: Create ForgotPasswordSubmitAction
            return $response->withJson(['message' => 'Password reset endpoint not implemented yet'], 501);
        })->setName('forgot-password-submit');

        // Password reset confirmation
        $group->get('/reset-password/{token}', function ($request, $response, $args) {
            // TODO: Create ResetPasswordPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('reset-password-page');

        $group->post('/reset-password', function ($request, $response) {
            // TODO: Create ResetPasswordSubmitAction
            return $response->withJson(['message' => 'Password reset confirmation endpoint not implemented yet'], 501);
        })->setName('reset-password-submit');

        // Logout
        $group->post('/logout', function ($request, $response) {
            // TODO: Create LogoutAction
            return $response->withHeader('Location', '/')->withStatus(302);
        })->setName('logout');

        // Email verification
        $group->get('/verify-email/{token}', function ($request, $response, $args) {
            // TODO: Create EmailVerificationAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('verify-email');

        // Resend verification email
        $group->post('/resend-verification', function ($request, $response) {
            // TODO: Create ResendVerificationAction
            return $response->withJson(['message' => 'Resend verification endpoint not implemented yet'], 501);
        })->setName('resend-verification');
    });

    // Social authentication routes
    $app->group('/auth/social', function (RouteCollectorProxy $group) {

        // Google OAuth
        $group->get('/google', function ($request, $response) {
            // TODO: Create GoogleOAuthAction
            return $response->withJson(['message' => 'Google OAuth not implemented yet'], 501);
        })->setName('auth-google');

        $group->get('/google/callback', function ($request, $response) {
            // TODO: Create GoogleOAuthCallbackAction
            return $response->withJson(['message' => 'Google OAuth callback not implemented yet'], 501);
        })->setName('auth-google-callback');

        // GitHub OAuth
        $group->get('/github', function ($request, $response) {
            // TODO: Create GitHubOAuthAction
            return $response->withJson(['message' => 'GitHub OAuth not implemented yet'], 501);
        })->setName('auth-github');

        $group->get('/github/callback', function ($request, $response) {
            // TODO: Create GitHubOAuthCallbackAction
            return $response->withJson(['message' => 'GitHub OAuth callback not implemented yet'], 501);
        })->setName('auth-github-callback');
    });

    // Direct routes (primary)
    $app->get('/login', function ($request, $response) {
        // Serve the login template directly
        $renderer = $this->get(Slim\Views\PhpRenderer::class);

        return $renderer->render($response, 'themes/modern/pages/login.php');
    })->setName('login');

    $app->post('/login', function ($request, $response) {
        // TODO: Create LoginSubmitAction
        return $response->withJson(['message' => 'Login endpoint not implemented yet'], 501);
    })->setName('login-submit-direct');

    $app->get('/register', function ($request, $response) {
        // TODO: Create RegisterPageAction
        return $response->withHeader('Location', '/login')->withStatus(302);
    })->setName('register');

    $app->post('/register', function ($request, $response) {
        // TODO: Create RegisterSubmitAction
        return $response->withJson(['message' => 'Registration endpoint not implemented yet'], 501);
    })->setName('register-submit-direct');

    $app->get('/forgot-password', function ($request, $response) {
        // TODO: Create ForgotPasswordPageAction
        return $response->withHeader('Location', '/login')->withStatus(302);
    })->setName('forgot-password');
};
