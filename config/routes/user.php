<?php

/**
 * User Module Routes.
 *
 * These routes handle user management functionality.
 */

use Slim\App;
use Slim\Routing\RouteCollectorProxy;

return function (App $app) {
    // User management routes
    $app->group('/users', function (RouteCollectorProxy $group) {

        // User list page
        $group->get('/list', \App\Module\User\List\Action\UserListPageAction::class)
            ->setName('user-list-page');

        // Fetch user list for Ajax call
        $group->get('', \App\Module\User\List\Action\UserFetchListAction::class)
            ->setName('user-list');

        // Submit user creation form
        $group->post('', \App\Module\User\Create\Action\UserCreateAction::class)
            ->setName('user-create-submit');

        // User read page
        $group->get('/{user_id:[0-9]+}', \App\Module\User\Read\Action\UserReadPageAction::class)
            ->setName('user-read-page');

        // Submit user update form
        $group->put('/{user_id:[0-9]+}', \App\Module\User\Update\Action\UserUpdateAction::class)
            ->setName('user-update-submit');

        // Submit delete user
        $group->delete('/{user_id:[0-9]+}', \App\Module\User\Delete\Action\UserDeleteAction::class)
            ->setName('user-delete-submit');
    });

    // User profile routes (for authenticated users)
    $app->group('/profile', function (RouteCollectorProxy $group) {

        // View own profile
        $group->get('', function ($request, $response) {
            // TODO: Create ProfilePageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('profile-page');

        // Edit profile page
        $group->get('/edit', function ($request, $response) {
            // TODO: Create ProfileEditPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('profile-edit-page');

        // Update profile
        $group->post('/edit', function ($request, $response) {
            // TODO: Create ProfileUpdateAction
            return $response->withJson(['message' => 'Profile update not implemented yet'], 501);
        })->setName('profile-update');

        // Change password page
        $group->get('/password', function ($request, $response) {
            // TODO: Create ChangePasswordPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('change-password-page');

        // Change password
        $group->post('/password', function ($request, $response) {
            // TODO: Create ChangePasswordAction
            return $response->withJson(['message' => 'Change password not implemented yet'], 501);
        })->setName('change-password');

        // Account settings
        $group->get('/settings', function ($request, $response) {
            // TODO: Create AccountSettingsPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('account-settings-page');

        // Update account settings
        $group->post('/settings', function ($request, $response) {
            // TODO: Create AccountSettingsUpdateAction
            return $response->withJson(['message' => 'Account settings update not implemented yet'], 501);
        })->setName('account-settings-update');

        // Delete account page
        $group->get('/delete', function ($request, $response) {
            // TODO: Create DeleteAccountPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('delete-account-page');

        // Delete account
        $group->post('/delete', function ($request, $response) {
            // TODO: Create DeleteAccountAction
            return $response->withJson(['message' => 'Delete account not implemented yet'], 501);
        })->setName('delete-account');
    });

    // User dashboard routes
    $app->group('/dashboard', function (RouteCollectorProxy $group) {

        // Main dashboard
        $group->get('', function ($request, $response) {
            // TODO: Create DashboardPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('dashboard');

        // User activity
        $group->get('/activity', function ($request, $response) {
            // TODO: Create UserActivityPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('user-activity');

        // User favorites
        $group->get('/favorites', function ($request, $response) {
            // TODO: Create UserFavoritesPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('user-favorites');

        // User progress
        $group->get('/progress', function ($request, $response) {
            // TODO: Create UserProgressPageAction
            return $response->withHeader('Location', '/auth/login')->withStatus(302);
        })->setName('user-progress');
    });
};
