<?php

/**
 * API Routes.
 *
 * These routes handle API endpoints for frontend applications and external integrations.
 */

use App\Application\Responder\JsonResponder;
use Slim\App;
use Slim\Routing\RouteCollectorProxy;

return function (App $app) {
    // Helper function for JSON responses
    $jsonResponse = function ($response, $data, $status = 200) use ($app) {
        $container = $app->getContainer();
        if ($container === null) {
            throw new RuntimeException('Container not available');
        }
        $jsonResponder = $container->get(JsonResponder::class);
        if (!$jsonResponder instanceof JsonResponder) {
            throw new RuntimeException('JsonResponder not found in container');
        }

        return $jsonResponder->encodeAndAddToResponse($response, $data, $status);
    };

    // API routes with CORS middleware
    $app->group('/api', function (RouteCollectorProxy $group) use ($jsonResponse) {

        // API root endpoint
        $group->get('', function ($request, $response) use ($jsonResponse) {
            $data = [
                'name' => 'Fretboard Pattern Visualizer API',
                'version' => '1.0.0',
                'description' => 'REST API for guitar learning tools',
                'endpoints' => [
                    'v1' => '/api/v1',
                    'users' => '/api/users',
                    'health' => '/api/v1/health',
                ],
                'documentation' => '/api/docs',
                'timestamp' => date('c'),
            ];

            return $jsonResponse($response, $data);
        })->setName('api-root');

        // API version 1
        $group->group('/v1', function (RouteCollectorProxy $v1) use ($jsonResponse) {

            // Users API
            $v1->group('/users', function (RouteCollectorProxy $users) use ($jsonResponse) {
                $users->get('', \App\Module\User\List\Action\ApiUserFetchListAction::class)
                    ->setName('api-v1-users-list');

                $users->post('', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiUserCreateAction
                    return $jsonResponse($response, ['message' => 'User creation API not implemented yet'], 501);
                })->setName('api-v1-users-create');

                $users->get('/{user_id:[0-9]+}', function ($request, $response, $_args) use ($jsonResponse) {
                    // TODO: Create ApiUserReadAction
                    // $_args will contain ['user_id' => '123'] when implemented
                    return $jsonResponse($response, ['message' => 'User read API not implemented yet'], 501);
                })->setName('api-v1-users-read');

                $users->put('/{user_id:[0-9]+}', function ($request, $response, $_args) use ($jsonResponse) {
                    // TODO: Create ApiUserUpdateAction
                    return $jsonResponse($response, ['message' => 'User update API not implemented yet'], 501);
                })->setName('api-v1-users-update');

                $users->delete('/{user_id:[0-9]+}', function ($request, $response, $_args) use ($jsonResponse) {
                    // TODO: Create ApiUserDeleteAction
                    return $jsonResponse($response, ['message' => 'User delete API not implemented yet'], 501);
                })->setName('api-v1-users-delete');
            });

            // Articles API
            $v1->group('/articles', function (RouteCollectorProxy $articles) use ($jsonResponse) {
                $articles->get('', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiArticleListAction
                    return $jsonResponse($response, ['message' => 'Articles list API not implemented yet'], 501);
                })->setName('api-v1-articles-list');

                $articles->post('', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiArticleCreateAction
                    return $jsonResponse($response, ['message' => 'Article creation API not implemented yet'], 501);
                })->setName('api-v1-articles-create');

                $articles->get('/{article_id}', function ($request, $response, $_args) use ($jsonResponse) {
                    // TODO: Create ApiArticleReadAction
                    return $jsonResponse($response, ['message' => 'Article read API not implemented yet'], 501);
                })->setName('api-v1-articles-read');

                $articles->put('/{article_id}', function ($request, $response, $_args) use ($jsonResponse) {
                    // TODO: Create ApiArticleUpdateAction
                    return $jsonResponse($response, ['message' => 'Article update API not implemented yet'], 501);
                })->setName('api-v1-articles-update');

                $articles->delete('/{article_id}', function ($request, $response, $_args) use ($jsonResponse) {
                    // TODO: Create ApiArticleDeleteAction
                    return $jsonResponse($response, ['message' => 'Article delete API not implemented yet'], 501);
                })->setName('api-v1-articles-delete');
            });

            // Guitar tools API
            $v1->group('/guitar', function (RouteCollectorProxy $guitar) use ($jsonResponse) {

                // Scales API
                $guitar->group('/scales', function (RouteCollectorProxy $scales) use ($jsonResponse) {
                    $scales->get('', function ($request, $response) use ($jsonResponse) {
                        // TODO: Create ApiScalesListAction
                        return $jsonResponse($response, ['message' => 'Scales list API not implemented yet'], 501);
                    })->setName('api-v1-scales-list');

                    $scales->get('/{scale_name}', function ($request, $response, $_args) use ($jsonResponse) {
                        // TODO: Create ApiScaleDetailsAction
                        return $jsonResponse($response, ['message' => 'Scale details API not implemented yet'], 501);
                    })->setName('api-v1-scale-details');
                });

                // Chords API
                $guitar->group('/chords', function (RouteCollectorProxy $chords) use ($jsonResponse) {
                    $chords->get('', function ($request, $response) use ($jsonResponse) {
                        // TODO: Create ApiChordsListAction
                        return $jsonResponse($response, ['message' => 'Chords list API not implemented yet'], 501);
                    })->setName('api-v1-chords-list');

                    $chords->get('/{chord_name}', function ($request, $response, $_args) use ($jsonResponse) {
                        // TODO: Create ApiChordDetailsAction
                        return $jsonResponse($response, ['message' => 'Chord details API not implemented yet'], 501);
                    })->setName('api-v1-chord-details');
                });

                // Fretboard API
                $guitar->group('/fretboard', function (RouteCollectorProxy $fretboard) use ($jsonResponse) {
                    $fretboard->post('/generate', function ($request, $response) use ($jsonResponse) {
                        // TODO: Create ApiFretboardGenerateAction
                        return $jsonResponse($response, ['message' => 'Fretboard generation API not implemented yet'], 501);
                    })->setName('api-v1-fretboard-generate');
                });
            });

            // Authentication API
            $v1->group('/auth', function (RouteCollectorProxy $auth) use ($jsonResponse) {
                $auth->post('/login', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiLoginAction
                    return $jsonResponse($response, ['message' => 'API login not implemented yet'], 501);
                })->setName('api-v1-auth-login');

                $auth->post('/register', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiRegisterAction
                    return $jsonResponse($response, ['message' => 'API registration not implemented yet'], 501);
                })->setName('api-v1-auth-register');

                $auth->post('/logout', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiLogoutAction
                    return $jsonResponse($response, ['message' => 'API logout not implemented yet'], 501);
                })->setName('api-v1-auth-logout');

                $auth->post('/refresh', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiRefreshTokenAction
                    return $jsonResponse($response, ['message' => 'API token refresh not implemented yet'], 501);
                })->setName('api-v1-auth-refresh');

                $auth->get('/me', function ($request, $response) use ($jsonResponse) {
                    // TODO: Create ApiUserProfileAction
                    return $jsonResponse($response, ['message' => 'API user profile not implemented yet'], 501);
                })->setName('api-v1-auth-me');
            });

            // Health check
            $v1->get('/health', function ($request, $response) use ($jsonResponse) {
                $data = [
                    'status' => 'ok',
                    'timestamp' => date('c'),
                    'version' => '1.0.0',
                ];

                return $jsonResponse($response, $data);
            })->setName('api-v1-health');
        });

        // Legacy API routes (without version)
        $group->get('/users', \App\Module\User\List\Action\ApiUserFetchListAction::class)
            ->setName('api-fetch-users-list');

    })// Cross-Origin Resource Sharing (CORS) middleware
    ->add(\App\Application\Middleware\CorsMiddleware::class);
};
