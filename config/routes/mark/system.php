<?php

declare(strict_types=1);

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Routing\RouteCollectorProxy;

/**
 * Mark Panel - System Administration Routes
 *
 * Routes for settings, logs, file management, system monitoring
 */
return function (RouteCollectorProxy $group) use ($jsonResponse) {

    // Settings
    $group->group('/settings', function (RouteCollectorProxy $settings) use ($jsonResponse) {

        // Settings overview
        $settings->get('', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            $settingsContent = $renderer->fetch('themes/modern/pages/mark/settings.php', [
                'title' => 'Settings'
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Settings',
                'pageTitle' => 'System Settings',
                'currentRoute' => 'mark-settings',
                'content' => $settingsContent
            ]);
        })->setName('mark-settings');

        // General settings
        $settings->get('/general', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/settings-general.php', [
                'title' => 'General Settings',
                'pageTitle' => 'General Settings',
                'currentRoute' => 'mark-settings-general'
            ]);
        })->setName('mark-settings-general');

        // Security settings
        $settings->get('/security', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/settings-security.php', [
                'title' => 'Security Settings',
                'pageTitle' => 'Security Settings',
                'currentRoute' => 'mark-settings-security'
            ]);
        })->setName('mark-settings-security');

        // Email settings
        $settings->get('/email', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/settings-email.php', [
                'title' => 'Email Settings',
                'pageTitle' => 'Email Configuration',
                'currentRoute' => 'mark-settings-email'
            ]);
        })->setName('mark-settings-email');

        // Update settings
        $settings->post('/{category}', function (Request $request, Response $response, array $args) use ($jsonResponse) {
            $category = $args['category'];

            return $jsonResponse($response, [
                'message' => "Settings update for category '{$category}' not implemented yet"
            ], 501);
        })->setName('mark-settings-update');

    });

    // Logs
    $group->group('/logs', function (RouteCollectorProxy $logs) use ($jsonResponse) {

        // Activity logs
        $logs->get('', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            $logsContent = $renderer->fetch('themes/modern/pages/mark/logs.php', [
                'title' => 'Activity Logs'
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Activity Logs',
                'pageTitle' => 'Activity Logs',
                'currentRoute' => 'mark-logs',
                'content' => $logsContent
            ]);
        })->setName('mark-logs');

        // System logs
        $logs->get('/system', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            $systemLogsContent = $renderer->fetch('themes/modern/pages/mark/logs-system.php', [
                'title' => 'System Logs'
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'System Logs',
                'pageTitle' => 'System Logs',
                'currentRoute' => 'mark-logs-system',
                'content' => $systemLogsContent
            ]);
        })->setName('mark-logs-system');

        // Error logs
        $logs->get('/errors', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            $errorLogsContent = $renderer->fetch('themes/modern/pages/mark/logs-errors.php', [
                'title' => 'Error Logs'
            ]);

            return $renderer->render($response, 'themes/modern/layouts/mark.php', [
                'title' => 'Error Logs',
                'pageTitle' => 'Error Logs',
                'currentRoute' => 'mark-logs-errors',
                'content' => $errorLogsContent
            ]);
        })->setName('mark-logs-errors');

        // Clear logs
        $logs->delete('/{type}', function (Request $request, Response $response, array $args) use ($jsonResponse) {
            $type = $args['type'];

            return $jsonResponse($response, [
                'message' => "Log clearing for type '{$type}' not implemented yet"
            ], 501);
        })->setName('mark-logs-clear');

        // Export logs
        $logs->post('/export', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Log export not implemented yet'
            ], 501);
        })->setName('mark-logs-export');

    });

    // File Management
    $group->group('/files', function (RouteCollectorProxy $files) use ($jsonResponse) {

        // File manager
        $files->get('', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/files.php', [
                'title' => 'File Manager',
                'pageTitle' => 'File Manager',
                'currentRoute' => 'mark-files'
            ]);
        })->setName('mark-files');

        // Upload files
        $files->post('/upload', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'File upload not implemented yet'
            ], 501);
        })->setName('mark-files-upload');

        // Delete files
        $files->delete('/{path:.*}', function (Request $request, Response $response, array $args) use ($jsonResponse) {
            $path = $args['path'];

            return $jsonResponse($response, [
                'message' => "File deletion for path '{$path}' not implemented yet"
            ], 501);
        })->setName('mark-files-delete');

        // Download files
        $files->get('/download/{path:.*}', function (Request $request, Response $response, array $args) use ($jsonResponse) {
            $path = $args['path'];

            return $jsonResponse($response, [
                'message' => "File download for path '{$path}' not implemented yet"
            ], 501);
        })->setName('mark-files-download');

    });

    // System Monitoring
    $group->group('/monitoring', function (RouteCollectorProxy $monitoring) use ($jsonResponse) {

        // System status
        $monitoring->get('/status', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'status' => 'online',
                'uptime' => '99.9%',
                'memory_usage' => '45%',
                'disk_usage' => '67%',
                'cpu_usage' => '23%',
                'database_status' => 'connected',
                'cache_status' => 'active'
            ], 200);
        })->setName('mark-monitoring-status');

        // Performance metrics
        $monitoring->get('/performance', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'response_time' => '45ms',
                'throughput' => '1250 req/min',
                'error_rate' => '0.1%',
                'cache_hit_rate' => '89%'
            ], 200);
        })->setName('mark-monitoring-performance');

        // Health check
        $monitoring->get('/health', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'status' => 'healthy',
                'checks' => [
                    'database' => 'ok',
                    'cache' => 'ok',
                    'storage' => 'ok',
                    'external_apis' => 'ok'
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ], 200);
        })->setName('mark-monitoring-health');

    });

    // Backup & Maintenance
    $group->group('/maintenance', function (RouteCollectorProxy $maintenance) use ($jsonResponse) {

        // Backup system
        $maintenance->post('/backup', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Backup creation not implemented yet'
            ], 501);
        })->setName('mark-maintenance-backup');

        // Restore system
        $maintenance->post('/restore', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'System restore not implemented yet'
            ], 501);
        })->setName('mark-maintenance-restore');

        // Clear cache
        $maintenance->post('/cache/clear', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Cache clearing not implemented yet'
            ], 501);
        })->setName('mark-maintenance-cache-clear');

        // Optimize database
        $maintenance->post('/database/optimize', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Database optimization not implemented yet'
            ], 501);
        })->setName('mark-maintenance-database-optimize');

    });

};
