<?php

declare(strict_types=1);

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Routing\RouteCollectorProxy;

/**
 * Mark Panel - Dashboard Routes
 * 
 * Routes for dashboard, analytics and system overview
 */
return function (RouteCollectorProxy $group) use ($jsonResponse) {

    // Dashboard home (redirect to dashboard)
    $group->get('', function (Request $request, Response $response) {
        return $response->withHeader('Location', '/mark/dashboard')->withStatus(302);
    })->setName('mark-home');

    // Main dashboard
    $group->get('/dashboard', function (Request $request, Response $response) {
        $renderer = $this->get(Slim\Views\PhpRenderer::class);
        
        $dashboardContent = $renderer->fetch('themes/modern/pages/mark/dashboard.php', [
            'title' => 'Dashboard'
        ]);
        
        return $renderer->render($response, 'themes/modern/layouts/mark.php', [
            'title' => 'Dashboard',
            'pageTitle' => 'Dashboard',
            'currentRoute' => 'mark-dashboard',
            'content' => $dashboardContent
        ]);
    })->setName('mark-dashboard');

    // Analytics and reports
    $group->group('/analytics', function (RouteCollectorProxy $analytics) use ($jsonResponse) {

        // Analytics dashboard
        $analytics->get('', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/analytics.php', [
                'title' => 'Analytics',
                'pageTitle' => 'Analytics Dashboard',
                'currentRoute' => 'mark-analytics'
            ]);
        })->setName('mark-analytics');

        // User analytics
        $analytics->get('/users', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'User analytics not implemented yet',
                'data' => [
                    'total_users' => 42,
                    'active_users' => 28,
                    'new_users_today' => 3
                ]
            ], 501);
        })->setName('mark-analytics-users');

        // Content analytics
        $analytics->get('/content', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Content analytics not implemented yet',
                'data' => [
                    'total_articles' => 67,
                    'published_articles' => 45,
                    'draft_articles' => 22
                ]
            ], 501);
        })->setName('mark-analytics-content');

        // System analytics
        $analytics->get('/system', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'System analytics not implemented yet',
                'data' => [
                    'uptime' => '99.9%',
                    'response_time' => '45ms',
                    'error_rate' => '0.1%'
                ]
            ], 501);
        })->setName('mark-analytics-system');

    });

    // Reports
    $group->group('/reports', function (RouteCollectorProxy $reports) use ($jsonResponse) {

        // Reports overview
        $reports->get('', function (Request $request, Response $response) {
            $renderer = $this->get(Slim\Views\PhpRenderer::class);

            return $renderer->render($response, 'themes/modern/pages/mark/reports.php', [
                'title' => 'Reports',
                'pageTitle' => 'System Reports',
                'currentRoute' => 'mark-reports'
            ]);
        })->setName('mark-reports');

        // User reports
        $reports->get('/users', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'User reports not implemented yet'
            ], 501);
        })->setName('mark-reports-users');

        // Activity reports
        $reports->get('/activity', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Activity reports not implemented yet'
            ], 501);
        })->setName('mark-reports-activity');

        // Export reports
        $reports->post('/export', function (Request $request, Response $response) use ($jsonResponse) {
            return $jsonResponse($response, [
                'message' => 'Report export not implemented yet'
            ], 501);
        })->setName('mark-reports-export');

    });

};
