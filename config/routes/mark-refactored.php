<?php

declare(strict_types=1);

use App\Application\Responder\JsonResponder;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\App;
use Slim\Routing\RouteCollectorProxy;

/**
 * Mark Panel Routes - Refactored Modular Structure
 * 
 * Administrative interface routes organized by functional categories:
 * 
 * 📊 Dashboard & Analytics - Overview, reports, system monitoring
 * 👥 User Management - CRUD operations, roles, permissions, import/export
 * 📝 Content Management - Articles, categories, media files (future)
 * ⚙️ System Administration - Settings, logs, file management, maintenance
 * 🔌 API Endpoints - RESTful APIs for all functionality
 * 
 * Benefits of this structure:
 * - Logical grouping by functionality
 * - Easier maintenance and debugging
 * - Clear separation of concerns
 * - Scalable for future features
 * - Consistent naming conventions
 */
return function (App $app) {
    // Helper function for JSON responses
    $jsonResponse = function ($response, $data, $status = 200) use ($app) {
        $container = $app->getContainer();
        if ($container === null) {
            throw new RuntimeException('Container not available');
        }
        $jsonResponder = $container->get(JsonResponder::class);
        if (!$jsonResponder instanceof JsonResponder) {
            throw new RuntimeException('JsonResponder not found in container');
        }

        return $jsonResponder->encodeAndAddToResponse($response, $data, $status);
    };

    // Mark Panel main group
    $app->group('/mark', function (RouteCollectorProxy $mark) use ($jsonResponse) {

        // 📊 Dashboard & Analytics Routes
        // Includes: dashboard overview, analytics, reports, system monitoring
        $mark->group('', require __DIR__ . '/mark/dashboard.php');

        // 👥 User Management Routes  
        // Includes: CRUD operations, roles, permissions, import/export, bulk operations
        $mark->group('/users', require __DIR__ . '/mark/users.php');

        // 📝 Content Management Routes (future implementation)
        // Will include: articles, categories, media files, SEO tools
        $mark->group('/content', function (RouteCollectorProxy $content) use ($jsonResponse) {
            
            // Articles management
            $content->get('/articles', function (Request $request, Response $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                return $renderer->render($response, 'themes/modern/pages/mark/articles.php', [
                    'title' => 'Articles',
                    'pageTitle' => 'Article Management',
                    'currentRoute' => 'mark-articles'
                ]);
            })->setName('mark-articles');

            // Categories management
            $content->get('/categories', function (Request $request, Response $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                return $renderer->render($response, 'themes/modern/pages/mark/categories.php', [
                    'title' => 'Categories',
                    'pageTitle' => 'Category Management',
                    'currentRoute' => 'mark-categories'
                ]);
            })->setName('mark-categories');

            // Media management
            $content->get('/media', function (Request $request, Response $response) {
                $renderer = $this->get(Slim\Views\PhpRenderer::class);

                return $renderer->render($response, 'themes/modern/pages/mark/media.php', [
                    'title' => 'Media',
                    'pageTitle' => 'Media Library',
                    'currentRoute' => 'mark-media'
                ]);
            })->setName('mark-media');

        });

        // ⚙️ System Administration Routes
        // Includes: settings, logs, file management, system monitoring, maintenance
        $mark->group('', require __DIR__ . '/mark/system.php');

        // 🔌 API Endpoints
        // RESTful APIs for dashboard, users, content, system data
        $mark->group('/api', require __DIR__ . '/mark/api.php');

    });

    /**
     * Route Organization Benefits:
     * 
     * 1. 📁 Modular Structure:
     *    - Each category in separate file
     *    - Easy to find and maintain
     *    - Clear responsibility boundaries
     * 
     * 2. 🎯 Logical Grouping:
     *    - Dashboard: /mark, /mark/dashboard, /mark/analytics, /mark/reports
     *    - Users: /mark/users, /mark/users/create, /mark/users/{id}, /mark/users/roles
     *    - System: /mark/settings, /mark/logs, /mark/files, /mark/monitoring
     *    - API: /mark/api/dashboard, /mark/api/users, /mark/api/activity
     * 
     * 3. 🔄 Consistent Patterns:
     *    - RESTful conventions
     *    - Predictable URL structure
     *    - Standard HTTP methods
     * 
     * 4. 🚀 Scalability:
     *    - Easy to add new categories
     *    - Simple to extend existing functionality
     *    - Future-proof architecture
     * 
     * 5. 🧪 Testability:
     *    - Isolated route groups
     *    - Clear dependencies
     *    - Mockable services
     */

};
