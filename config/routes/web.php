<?php

/**
 * Web Routes - Main website pages.
 *
 * These routes handle the main website pages and public content.
 */

use <PERSON>\App;
use <PERSON>\Routing\RouteCollectorProxy;

return function (App $app) {
    // Home page
    $app->get('/', \App\Module\Home\Action\HomePageAction::class)->setName('home-page');
    $app->get('/home', \App\Module\Home\Action\RedirectToHomePageAction::class)->setName('home-redirect');

    // Static pages
    $app->get('/about', function ($request, $response) {
        // TODO: Create AboutPageAction
        return $response->withHeader('Location', '/')->withStatus(302);
    })->setName('about-page');

    $app->get('/contact', function ($request, $response) {
        // TODO: Create ContactPageAction
        return $response->withHeader('Location', '/')->withStatus(302);
    })->setName('contact-page');

    $app->get('/privacy', function ($request, $response) {
        // TODO: Create PrivacyPageAction
        return $response->withHeader('Location', '/')->withStatus(302);
    })->setName('privacy-page');

    $app->get('/terms', function ($request, $response) {
        // TODO: Create TermsPageAction
        return $response->withHeader('Location', '/')->withStatus(302);
    })->setName('terms-page');

    // Blog/Articles section
    $app->group('/blog', function (RouteCollectorProxy $group) {
        // Blog listing page
        $group->get('', function ($request, $response) {
            // TODO: Create BlogListPageAction
            return $response->withHeader('Location', '/')->withStatus(302);
        })->setName('blog-list');

        // Individual blog post
        $group->get('/{slug}', function ($request, $response, $args) {
            // TODO: Create BlogPostPageAction
            return $response->withHeader('Location', '/')->withStatus(302);
        })->setName('blog-post');
    });

    // Guitar tools section
    $app->group('/tools', function (RouteCollectorProxy $group) {
        // Fretboard visualizer
        $group->get('/fretboard', function ($request, $response) {
            // TODO: Create FretboardVisualizerPageAction
            return $response->withHeader('Location', '/')->withStatus(302);
        })->setName('fretboard-tool');

        // Scale finder
        $group->get('/scales', function ($request, $response) {
            // TODO: Create ScaleFinderPageAction
            return $response->withHeader('Location', '/')->withStatus(302);
        })->setName('scales-tool');

        // Chord finder
        $group->get('/chords', function ($request, $response) {
            // TODO: Create ChordFinderPageAction
            return $response->withHeader('Location', '/')->withStatus(302);
        })->setName('chords-tool');
    });
};
