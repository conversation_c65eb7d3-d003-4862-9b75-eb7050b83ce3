<?php

declare(strict_types = 1);

use <PERSON>\App;
use Slim\Exception\HttpNotFoundException;

/**
 * Application Routes.
 *
 * This file loads all route modules to keep routes organized and maintainable.
 * Routes are split into logical groups for better organization.
 */

return function (App $app) {
    // Load route modules
    $routeModules = [
        'web.php',      // Main website pages
        'auth.php',     // Authentication routes
        'user.php',     // User management routes
        'article.php',  // Article/blog routes
        'api.php',      // API endpoints
        'mark.php',     // Mark panel routes
    ];

    foreach ($routeModules as $module) {
        $routeFile = __DIR__ . '/routes/' . $module;
        if (file_exists($routeFile)) {
            $routeLoader = require $routeFile;
            if (is_callable($routeLoader)) {
                $routeLoader($app);
            }
        }
    }

    /**
     * Catch-all route to serve a 404 Not Found page if none of the routes match
     * This route must be defined last.
     */
    $app->map(
        ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        '/{routes:.+}',
        function ($request, $response) {
            // Throw exception 404 with route that was not found.
            throw new HttpNotFoundException(
                $request,
                'Route "' . $request->getUri()->getHost() . $request->getUri()->getPath() . '" not found.'
            );
        }
    );
};
