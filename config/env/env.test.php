<?php

// Enable display_error_details for testing as this will throw an ErrorException for notices and warnings
$settings['error']['display_error_details'] = true;

// Database for integration testing must include the word "test"
$settings['db']['database'] = 'slim_starter_test';
$settings['db']['default']['database'] = 'var/db/user/test.sqlite';
$settings['db']['default']['driver'] = 'pdo_sqlite';

// Override SQLite paths for test databases
$settings['sqlite']['path'] = 'var/db';

// Override database connections for testing
$settings['db']['connections']['user']['database'] = 'var/db/user/test.sqlite';
$settings['db']['connections']['article']['database'] = 'var/db/article/test.sqlite';
$settings['db']['connections']['mark']['database'] = 'var/db/mark/test.sqlite';

// Add example.com domain to allowed origin to test CORS in API call
$settings['api']['allowed_origin'] = 'https://example.com/';

// Enable test mode for the logger
$settings['logger']['test'] = true;
