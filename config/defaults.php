<?php

/**
 * Default configuration values.
 *
 * This file should contain all keys, even secret ones to serve as template.
 *
 * This is the first file loaded in settings.php and can safely define arrays
 * without the risk of overwriting something.
 * The only file where the following is permitted: $settings['db'] = ['key' => 'val', 'nextKey' => 'nextVal'];
 *
 * Documentation: https://samuel-gfeller.ch/docs/Configuration.
 */
// Set default locale
setlocale(LC_ALL, 'en_US.utf8', 'en_US');

// Init settings var
$settings = [];

// Project root dir (1 parent)
$settings['root_dir'] = dirname(__DIR__, 1);

$settings['deployment'] = [
    // Version string or null.
    // If JsImportCacheBuster is enabled, `null` removes all query param versions from js imports
    'version' => '0.3.0',
    // When true, JsImportCacheBuster is enabled and goes through all js files and changes the version number
    // from the imports. Should be disabled in env.prod.php.
    // https://samuel-gfeller.ch/docs/Template-Rendering#js-import-cache-busting
    'update_js_imports_version' => false,
    // Asset path required by the JsImportCacheBuster
    'asset_path' => $settings['root_dir'] . '/public/assets',
];

// Error handler: https://github.com/samuelgfeller/slim-error-renderer
$settings['error'] = [
    // MUST be set to false in production.
    // When set to true, it shows error details and throws an ErrorException for notices and warnings.
    'display_error_details' => false,
    'log_errors' => true,
];

$settings['public'] = [
    'app_name' => 'Fretboard Pattern Visualizer',
    'main_contact_email' => '<EMAIL>',
];

// Secret values are overwritten in env.php
// Query Builder: https://book.cakephp.org/5/en/orm/query-builder.html
// Documentation: https://samuel-gfeller.ch/docs/Repository-and-Query-Builder

// Base database configuration
$settings['db'] = [
    'default' => [
        'driver' => 'pdo_sqlite',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'quoteIdentifiers' => true,
        'log' => false,
        'flags' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_EMULATE_PREPARES => true,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ],
    ],
    'connections' => [
        'user' => [
            'driver' => 'pdo_sqlite',
            'database' => $settings['root_dir'] . '/var/db/user.sqlite',
            'prefix' => 'user_',
        ],
        'article' => [
            'driver' => 'pdo_sqlite',
            'database' => $settings['root_dir'] . '/var/db/article.sqlite',
            'prefix' => 'article_',
        ],
        'mark' => [
            'driver' => 'pdo_sqlite',
            'database' => $settings['root_dir'] . '/var/db/mark.sqlite',
            'prefix' => 'mark_',
        ],
    ],
];

// SQLite specific settings
$settings['sqlite'] = [
    'path' => 'var/db',
    'journal_mode' => 'WAL',
];

// API documentation: https://samuel-gfeller.ch/docs/API-Endpoint
$settings['api'] = [
    // Url that is allowed to make api calls to this app
    'allowed_origin' => null,
];

// Phinx database migration settings
// Documentation: https://samuel-gfeller.ch/docs/Database-Migrations
// Libraries: https://github.com/odan/phinx-migrations-generator, https://book.cakephp.org/phinx/0/en/index.html
$settings['phinx'] = [
    'paths' => [
        'migrations' => $settings['root_dir'] . '/resources/migrations',
        'seeds' => $settings['root_dir'] . '/resources/seeds',
    ],
    'schema_file' => $settings['root_dir'] . '/resources/schema/schema.php',
    'default_migration_prefix' => 'db_change_',
    'generate_migration_name' => true,
    'environments' => [
        // Table that keeps track of the migrations
        'default_migration_table' => 'phinx_migration_log',
        'default_environment' => 'local',
        'local' => [
            /* Environment specifics such as db credentials from the secret config are added in env.phinx.php */
        ],
    ],
];

// Template renderer settings
// Documentation: https://samuel-gfeller.ch/docs/Template-Rendering
$settings['renderer'] = [
    // Template path
    'path' => $settings['root_dir'] . '/templates',
];

// Logger: https://samuel-gfeller.ch/docs/Logging
$settings['logger'] = [
    // Log file location
    'path' => $settings['root_dir'] . '/logs',
    // Default log level
    'level' => Monolog\Level::Debug,
    // Maximum number of log files to keep (0 = unlimited, 30 = keep 30 days)
    'max_files' => 30,
];

// Security settings
$settings['security'] = [
    // Force HTTPS (set to true in production)
    'force_https' => false,
    // Custom security headers (optional)
    'headers' => [
        // Add custom headers here if needed
    ],
];

// Rate limiting settings
$settings['rate_limit'] = [
    // Storage directory for rate limit data
    'storage_dir' => $settings['root_dir'] . '/var/rate_limits',
    // Rate limits per category (requests per time window in seconds)
    'limits' => [
        'global' => ['requests' => 100, 'window' => 60], // 100 requests per minute
        'api' => ['requests' => 60, 'window' => 60],      // 60 API requests per minute
        'auth' => ['requests' => 5, 'window' => 300],     // 5 auth attempts per 5 minutes
        'mark' => ['requests' => 30, 'window' => 60],     // 30 admin requests per minute
    ],
    // IP whitelist (local development IPs)
    'whitelist' => [
        '127.0.0.1',
        '::1',
        'localhost'
    ],
];

return $settings;
