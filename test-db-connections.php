<?php

require __DIR__ . '/vendor/autoload.php';

use DI\ContainerBuilder;

// Create container
$containerBuilder = new ContainerBuilder();
$container = $containerBuilder->addDefinitions(__DIR__ . '/config/container.php')->build();

echo "🔍 Testing database connections...\n\n";

// Test User connection
try {
    $userConnection = $container->get('db.connection.user');
    $userPdo = $userConnection->getDriver()->getPdo();
    $result = $userPdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
    echo "✅ User DB: " . $userPdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "\n";
    echo "   Tables: " . implode(', ', array_column($result, 'name')) . "\n\n";
} catch (Exception $e) {
    echo "❌ User DB Error: " . $e->getMessage() . "\n\n";
}

// Test Article connection
try {
    $articleConnection = $container->get('db.connection.article');
    $articlePdo = $articleConnection->getDriver()->getPdo();
    $result = $articlePdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
    echo "✅ Article DB: " . $articlePdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "\n";
    echo "   Tables: " . implode(', ', array_column($result, 'name')) . "\n\n";
} catch (Exception $e) {
    echo "❌ Article DB Error: " . $e->getMessage() . "\n\n";
}

// Test Mark connection
try {
    $markConnection = $container->get('db.connection.mark');
    $markPdo = $markConnection->getDriver()->getPdo();
    $result = $markPdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
    echo "✅ Mark DB: " . $markPdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "\n";
    echo "   Tables: " . implode(', ', array_column($result, 'name')) . "\n\n";
} catch (Exception $e) {
    echo "❌ Mark DB Error: " . $e->getMessage() . "\n\n";
}

// Test default connection
try {
    $defaultConnection = $container->get(\Cake\Database\Connection::class);
    $defaultPdo = $defaultConnection->getDriver()->getPdo();
    $result = $defaultPdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
    echo "✅ Default DB: " . $defaultPdo->getAttribute(PDO::ATTR_CONNECTION_STATUS) . "\n";
    echo "   Tables: " . implode(', ', array_column($result, 'name')) . "\n\n";
} catch (Exception $e) {
    echo "❌ Default DB Error: " . $e->getMessage() . "\n\n";
}

echo "🎯 Database structure:\n";
echo "Production:\n";
echo "  - var/db/user/database.sqlite\n";
echo "  - var/db/article/database.sqlite\n";
echo "  - var/db/mark/database.sqlite\n\n";
echo "Test:\n";
echo "  - var/db/user/test.sqlite\n";
echo "  - var/db/article/test.sqlite\n";
echo "  - var/db/mark/test.sqlite\n\n";
